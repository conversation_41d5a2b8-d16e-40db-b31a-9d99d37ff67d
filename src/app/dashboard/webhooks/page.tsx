"use client";

import { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Checkbox } from "@/components/ui/checkbox";
import { Webhook } from "@/types";
import { toast } from "sonner";
import {
  useGetWebhooksQuery,
  useCreateWebhookMutation,
  useUpdateWebhookMutation,
  useDeleteWebhookMutation,
  useGetWebhookDeliveriesQuery
} from "@/lib/api/apiSlice";

const AVAILABLE_EVENTS = [
  { id: "credit.consumed", label: "Credit Consumed" },
  { id: "credit.added", label: "Credit Added" },
  { id: "api_key.created", label: "API Key Created" },
  { id: "api_key.updated", label: "API Key Updated" },
  { id: "api_key.deleted", label: "API Key Deleted" },
  { id: "subscription.created", label: "Subscription Created" },
  { id: "subscription.updated", label: "Subscription Updated" },
  { id: "subscription.cancelled", label: "Subscription Cancelled" },
];

export default function WebhooksPage() {
  const [newWebhookDialogOpen, setNewWebhookDialogOpen] = useState(false);
  const [deliveriesDialogOpen, setDeliveriesDialogOpen] = useState(false);
  const [selectedWebhook, setSelectedWebhook] = useState<Webhook | null>(null);
  const [formData, setFormData] = useState({
    name: "",
    url: "",
    secret: "",
    events: [] as string[],
  });

  // No need to manually manage the session token
  // NextAuth.js handles this for us

  // Fetch webhooks using RTK Query
  const {
    data: webhooks = [],
    isLoading: webhooksLoading,
    error: webhooksError
  } = useGetWebhooksQuery();

  // Fetch webhook deliveries when a webhook is selected
  const {
    data: deliveries = [],
    isLoading: deliveriesLoading
  } = useGetWebhookDeliveriesQuery(selectedWebhook?.id || '', {
    skip: !selectedWebhook,
  });

  // Mutations for creating, updating, and deleting webhooks
  const [createWebhookMutation] = useCreateWebhookMutation();
  const [updateWebhookMutation] = useUpdateWebhookMutation();
  const [deleteWebhookMutation] = useDeleteWebhookMutation();

  // Show error toast if webhooks fetch fails
  useEffect(() => {
    if (webhooksError) {
      toast.error("Failed to load webhooks");
      console.error("Error fetching webhooks:", webhooksError);
    }
  }, [webhooksError]);

  const handleCreateWebhook = async () => {
    if (!formData.name || !formData.url || formData.events.length === 0) {
      toast.error("Please fill in all required fields");
      return;
    }

    try {
      await createWebhookMutation(formData).unwrap();
      setNewWebhookDialogOpen(false);
      setFormData({
        name: "",
        url: "",
        secret: "",
        events: [],
      });
      toast.success("Webhook created successfully");
    } catch (error) {
      console.error("Error creating webhook:", error);
      toast.error("Failed to create webhook");
    }
  };

  const handleToggleWebhook = async (webhook: Webhook) => {
    try {
      const result = await updateWebhookMutation({
        id: webhook.id,
        data: { active: !webhook.active }
      }).unwrap();

      toast.success(`Webhook ${result.active ? "enabled" : "disabled"}`);
    } catch (error) {
      console.error("Error updating webhook:", error);
      toast.error("Failed to update webhook");
    }
  };

  const handleDeleteWebhook = async (id: string) => {
    try {
      await deleteWebhookMutation(id).unwrap();
      toast.success("Webhook deleted");
    } catch (error) {
      console.error("Error deleting webhook:", error);
      toast.error("Failed to delete webhook");
    }
  };

  const handleViewDeliveries = (webhook: Webhook) => {
    setSelectedWebhook(webhook);
    setDeliveriesDialogOpen(true);
  };

  const handleEventChange = (eventId: string, checked: boolean) => {
    if (checked) {
      setFormData({
        ...formData,
        events: [...formData.events, eventId],
      });
    } else {
      setFormData({
        ...formData,
        events: formData.events.filter((id) => id !== eventId),
      });
    }
  };

  if (webhooksLoading) {
    return (
      <div className="flex h-[calc(100vh-4rem)] items-center justify-center">
        <div className="h-8 w-8 animate-spin rounded-full border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className="container py-10">
      <div className="flex justify-between items-center mb-8">
        <h1 className="text-3xl font-bold">Webhooks</h1>
        <Dialog open={newWebhookDialogOpen} onOpenChange={setNewWebhookDialogOpen}>
          <DialogTrigger asChild>
            <Button>Create New Webhook</Button>
          </DialogTrigger>
          <DialogContent className="sm:max-w-[500px]">
            <DialogHeader>
              <DialogTitle>Create New Webhook</DialogTitle>
              <DialogDescription>
                Create a webhook to receive notifications when events occur in your account.
              </DialogDescription>
            </DialogHeader>
            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="name" className="text-right">
                  Name
                </Label>
                <Input
                  id="name"
                  value={formData.name}
                  onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                  className="col-span-3"
                  placeholder="My Webhook"
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="url" className="text-right">
                  URL
                </Label>
                <Input
                  id="url"
                  value={formData.url}
                  onChange={(e) => setFormData({ ...formData, url: e.target.value })}
                  className="col-span-3"
                  placeholder="https://example.com/webhook"
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="secret" className="text-right">
                  Secret
                </Label>
                <Input
                  id="secret"
                  value={formData.secret}
                  onChange={(e) => setFormData({ ...formData, secret: e.target.value })}
                  className="col-span-3"
                  placeholder="Optional: Used to verify webhook signatures"
                />
              </div>
              <div className="grid grid-cols-4 items-start gap-4">
                <Label className="text-right pt-2">Events</Label>
                <div className="col-span-3 space-y-2">
                  {AVAILABLE_EVENTS.map((event) => (
                    <div key={event.id} className="flex items-center space-x-2">
                      <Checkbox
                        id={`event-${event.id}`}
                        checked={formData.events.includes(event.id)}
                        onCheckedChange={(checked) => handleEventChange(event.id, checked === true)}
                      />
                      <Label htmlFor={`event-${event.id}`}>{event.label}</Label>
                    </div>
                  ))}
                </div>
              </div>
            </div>
            <DialogFooter>
              <Button
                onClick={handleCreateWebhook}
                disabled={!formData.name || !formData.url || formData.events.length === 0}
              >
                Create
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Your Webhooks</CardTitle>
          <CardDescription>
            Manage webhooks for receiving event notifications
          </CardDescription>
        </CardHeader>
        <CardContent>
          {webhooks.length > 0 ? (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Name</TableHead>
                  <TableHead>URL</TableHead>
                  <TableHead>Events</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {webhooks.map((webhook) => (
                  <TableRow key={webhook.id}>
                    <TableCell className="font-medium">{webhook.name}</TableCell>
                    <TableCell className="max-w-[200px] truncate">{webhook.url}</TableCell>
                    <TableCell>{webhook.events.length} events</TableCell>
                    <TableCell>
                      <span
                        className={`inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium ${
                          webhook.active
                            ? "bg-green-100 text-green-800"
                            : "bg-red-100 text-red-800"
                        }`}
                      >
                        {webhook.active ? "Active" : "Disabled"}
                      </span>
                    </TableCell>
                    <TableCell className="text-right">
                      <div className="flex justify-end gap-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleViewDeliveries(webhook)}
                        >
                          Deliveries
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleToggleWebhook(webhook)}
                        >
                          {webhook.active ? "Disable" : "Enable"}
                        </Button>
                        <Button
                          variant="destructive"
                          size="sm"
                          onClick={() => handleDeleteWebhook(webhook.id)}
                        >
                          Delete
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          ) : (
            <div className="py-8 text-center text-muted-foreground">
              No webhooks found. Create your first webhook to receive event notifications.
            </div>
          )}
        </CardContent>
      </Card>

      {/* Webhook Deliveries Dialog */}
      <Dialog
        open={deliveriesDialogOpen}
        onOpenChange={(open) => {
          setDeliveriesDialogOpen(open);
          if (!open) setSelectedWebhook(null);
        }}
      >
        <DialogContent className="sm:max-w-[800px]">
          <DialogHeader>
            <DialogTitle>Webhook Deliveries</DialogTitle>
            <DialogDescription>
              {selectedWebhook && `Recent delivery attempts for ${selectedWebhook.name}`}
            </DialogDescription>
          </DialogHeader>
          {deliveriesLoading ? (
            <div className="py-8 flex justify-center">
              <div className="h-8 w-8 animate-spin rounded-full border-b-2 border-primary"></div>
            </div>
          ) : deliveries.length > 0 ? (
            <div className="max-h-[400px] overflow-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Event</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Time</TableHead>
                    <TableHead>Duration</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {deliveries.map((delivery) => (
                    <TableRow key={delivery.id}>
                      <TableCell>{delivery.event}</TableCell>
                      <TableCell>
                        <span
                          className={`inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium ${
                            delivery.success
                              ? "bg-green-100 text-green-800"
                              : "bg-red-100 text-red-800"
                          }`}
                        >
                          {delivery.success ? "Success" : `Failed (${delivery.status_code})`}
                        </span>
                      </TableCell>
                      <TableCell>{new Date(delivery.created_at).toLocaleString()}</TableCell>
                      <TableCell>{delivery.duration}ms</TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          ) : (
            <div className="py-8 text-center text-muted-foreground">
              No delivery attempts found for this webhook.
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
}
