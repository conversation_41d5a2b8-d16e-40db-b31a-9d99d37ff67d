/**
 * ADC Credit SDK Usage Module
 */
import { ApiClient } from './client';
import { 
  APIResponse, 
  AnalyticsSummary, 
  AnalyticsTrend, 
  EndpointAnalytics, 
  PerformanceMetrics, 
  Usage, 
  UsageSummary 
} from './types';

/**
 * Usage module for the ADC Credit SDK
 */
export class UsageStats {
  private client: ApiClient;

  /**
   * Creates a new UsageStats instance
   * @param client API client
   */
  constructor(client: ApiClient) {
    this.client = client;
  }

  /**
   * Gets usage data
   * @param params Query parameters
   * @returns Usage data
   */
  public async getUsage(params?: {
    start_date?: string;
    end_date?: string;
    api_key_id?: string;
    endpoint?: string;
    method?: string;
    limit?: number;
    offset?: number;
  }): Promise<APIResponse<Usage[]>> {
    // Convert params to query string
    const queryParams = new URLSearchParams();
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined) {
          queryParams.append(key, value.toString());
        }
      });
    }
    
    const queryString = queryParams.toString();
    const endpoint = queryString ? `/usage?${queryString}` : '/usage';
    
    return this.client.get<Usage[]>(endpoint);
  }

  /**
   * Gets usage summary
   * @param params Query parameters
   * @returns Usage summary
   */
  public async getUsageSummary(params?: {
    start_date?: string;
    end_date?: string;
    api_key_id?: string;
    endpoint?: string;
    method?: string;
  }): Promise<APIResponse<UsageSummary>> {
    // Convert params to query string
    const queryParams = new URLSearchParams();
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined) {
          queryParams.append(key, value.toString());
        }
      });
    }
    
    const queryString = queryParams.toString();
    const endpoint = queryString ? `/usage/summary?${queryString}` : '/usage/summary';
    
    return this.client.get<UsageSummary>(endpoint);
  }

  /**
   * Gets analytics summary
   * @param params Query parameters
   * @returns Analytics summary
   */
  public async getAnalyticsSummary(params?: {
    start_date?: string;
    end_date?: string;
  }): Promise<APIResponse<AnalyticsSummary>> {
    // Convert params to query string
    const queryParams = new URLSearchParams();
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined) {
          queryParams.append(key, value.toString());
        }
      });
    }
    
    const queryString = queryParams.toString();
    const endpoint = queryString ? `/analytics/summary?${queryString}` : '/analytics/summary';
    
    return this.client.get<AnalyticsSummary>(endpoint);
  }

  /**
   * Gets analytics trends
   * @param params Query parameters
   * @returns Analytics trends
   */
  public async getAnalyticsTrends(params?: {
    start_date?: string;
    end_date?: string;
    interval?: 'day' | 'week' | 'month';
  }): Promise<APIResponse<AnalyticsTrend[]>> {
    // Convert params to query string
    const queryParams = new URLSearchParams();
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined) {
          queryParams.append(key, value.toString());
        }
      });
    }
    
    const queryString = queryParams.toString();
    const endpoint = queryString ? `/analytics/trends?${queryString}` : '/analytics/trends';
    
    return this.client.get<AnalyticsTrend[]>(endpoint);
  }

  /**
   * Gets endpoint analytics
   * @param params Query parameters
   * @returns Endpoint analytics
   */
  public async getEndpointAnalytics(params?: {
    start_date?: string;
    end_date?: string;
  }): Promise<APIResponse<EndpointAnalytics[]>> {
    // Convert params to query string
    const queryParams = new URLSearchParams();
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined) {
          queryParams.append(key, value.toString());
        }
      });
    }
    
    const queryString = queryParams.toString();
    const endpoint = queryString ? `/analytics/endpoints?${queryString}` : '/analytics/endpoints';
    
    return this.client.get<EndpointAnalytics[]>(endpoint);
  }

  /**
   * Gets performance metrics
   * @param params Query parameters
   * @returns Performance metrics
   */
  public async getPerformanceMetrics(params?: {
    start_date?: string;
    end_date?: string;
    metric?: 'response_time' | 'error_rate' | 'requests' | 'credits';
  }): Promise<APIResponse<PerformanceMetrics>> {
    // Convert params to query string
    const queryParams = new URLSearchParams();
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined) {
          queryParams.append(key, value.toString());
        }
      });
    }
    
    const queryString = queryParams.toString();
    const endpoint = queryString ? `/analytics/performance?${queryString}` : '/analytics/performance';
    
    return this.client.get<PerformanceMetrics>(endpoint);
  }
}
