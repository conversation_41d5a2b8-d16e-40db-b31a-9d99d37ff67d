package main

import (
	"log"

	"github.com/adc-credit/backend/internal/database"
	"github.com/adc-credit/backend/internal/services"
	"github.com/joho/godotenv"
)

func main() {
	// Load environment variables
	if err := godotenv.Load(); err != nil {
		log.Println("No .env file found, using environment variables")
	}

	// Initialize database connection
	if err := database.InitDB(); err != nil {
		log.Fatalf("Failed to connect to database: %v", err)
	}

	// Create credit reset service
	creditResetService := services.NewCreditResetService()

	// Process credit resets
	count, err := creditResetService.ProcessCreditResets()
	if err != nil {
		log.Fatalf("Failed to process credit resets: %v", err)
	}

	log.Printf("Successfully processed %d credit resets", count)
}
