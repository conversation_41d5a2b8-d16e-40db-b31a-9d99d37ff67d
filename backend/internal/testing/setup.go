package testing

import (
	"fmt"
	"log"
	"os"
	"testing"

	"github.com/adc-credit/backend/internal/models"
	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/joho/godotenv"
	"gorm.io/driver/postgres"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

var (
	TestDB     *gorm.DB
	TestRouter *gin.Engine
)

// SetupTestEnvironment initializes the test environment
func SetupTestEnvironment() error {
	// Load test environment variables
	if err := godotenv.Load("../../.env.test"); err != nil {
		if err := godotenv.Load("../.env.test"); err != nil {
			if err := godotenv.Load(".env.test"); err != nil {
				log.Println("No .env.test file found, using environment variables")
			}
		}
	}

	// Set Gin to test mode
	gin.SetMode(gin.TestMode)

	// Initialize test database
	if err := setupTestDatabase(); err != nil {
		return fmt.Errorf("failed to setup test database: %w", err)
	}

	// Initialize test router
	setupTestRouter()

	return nil
}

// setupTestDatabase initializes the test database connection
func setupTestDatabase() error {
	// Get test database URL
	testDBURL := os.Getenv("TEST_DATABASE_URL")
	if testDBURL == "" {
		// Fallback to main database URL with test suffix
		mainDBURL := os.Getenv("DATABASE_URL")
		if mainDBURL == "" {
			return fmt.Errorf("TEST_DATABASE_URL or DATABASE_URL environment variable is required")
		}
		testDBURL = mainDBURL + "_test"
	}

	// Connect to test database
	db, err := gorm.Open(postgres.Open(testDBURL), &gorm.Config{
		Logger: logger.Default.LogMode(logger.Silent), // Reduce log noise in tests
	})
	if err != nil {
		return fmt.Errorf("failed to connect to test database: %w", err)
	}

	TestDB = db

	// Auto-migrate test database
	if err := autoMigrateTestDB(); err != nil {
		return fmt.Errorf("failed to migrate test database: %w", err)
	}

	return nil
}

// autoMigrateTestDB runs auto-migration for all models
func autoMigrateTestDB() error {
	// Migrate in dependency order - avoid circular dependencies
	// First, create base models without foreign key constraints
	if err := TestDB.AutoMigrate(
		&models.Organization{},
		&models.Branch{},
		&models.SubscriptionTier{},
	); err != nil {
		return err
	}

	// Create User table without foreign key constraints
	if err := TestDB.AutoMigrate(&models.User{}); err != nil {
		return err
	}

	// Create MerchantShop first (legacy model)
	if err := TestDB.AutoMigrate(&models.MerchantShop{}); err != nil {
		return err
	}

	// Temporarily disable foreign key constraints for Shop model migration
	if err := TestDB.Exec("SET session_replication_role = replica;").Error; err != nil {
		return err
	}

	// Skip Shop and ShopBranch models for now due to foreign key issues
	// TODO: Fix foreign key constraints and re-enable
	// if err := TestDB.AutoMigrate(
	//	&models.Shop{},
	//	&models.ShopBranch{},
	// ); err != nil {
	//	return err
	// }

	// Re-enable foreign key constraints
	if err := TestDB.Exec("SET session_replication_role = DEFAULT;").Error; err != nil {
		return err
	}

	// Create models that depend on User but not Shop
	if err := TestDB.AutoMigrate(
		&models.Subscription{},
		&models.Webhook{},
	); err != nil {
		return err
	}

	// Skip APIKey model for now due to Shop foreign key dependency
	// TODO: Re-enable once Shop model is fixed
	// if err := TestDB.AutoMigrate(
	//	&models.APIKey{},
	// ); err != nil {
	//	return err
	// }

	// Create dependent models
	return TestDB.AutoMigrate(
		&models.Usage{},
		&models.Transaction{},
		&models.WebhookDelivery{},
		&models.AnalyticsData{},
		&models.ShopCustomer{},
		&models.CreditCode{},
		&models.ShopCreditTransaction{},
	)
}

// setupTestRouter initializes the test Gin router
func setupTestRouter() {
	TestRouter = gin.New()
	// Add middleware and routes here
	// This will be populated when we create the route tests
}

// CleanupTestEnvironment cleans up after tests
func CleanupTestEnvironment() {
	if TestDB != nil {
		// Clean up test data
		CleanTestData()

		// Close database connection
		sqlDB, err := TestDB.DB()
		if err == nil {
			sqlDB.Close()
		}
	}
}

// CleanTestData removes all test data from the database
func CleanTestData() {
	if TestDB == nil {
		return
	}

	// Delete in reverse order of dependencies
	TestDB.Exec("DELETE FROM webhook_deliveries")
	TestDB.Exec("DELETE FROM webhooks")
	TestDB.Exec("DELETE FROM analytics_data")
	TestDB.Exec("DELETE FROM shop_credit_transactions")
	TestDB.Exec("DELETE FROM credit_codes")
	TestDB.Exec("DELETE FROM shop_customers")
	TestDB.Exec("DELETE FROM shop_branches")
	TestDB.Exec("DELETE FROM shops")
	TestDB.Exec("DELETE FROM merchant_shops")
	TestDB.Exec("DELETE FROM usage")
	TestDB.Exec("DELETE FROM transactions")
	TestDB.Exec("DELETE FROM api_keys")
	TestDB.Exec("DELETE FROM subscriptions")
	TestDB.Exec("DELETE FROM subscription_tiers")
	TestDB.Exec("DELETE FROM branches")
	TestDB.Exec("DELETE FROM organizations")
	TestDB.Exec("DELETE FROM users")
}

// TestMain sets up and tears down the test environment
func TestMain(m *testing.M) {
	// Setup
	if err := SetupTestEnvironment(); err != nil {
		log.Fatalf("Failed to setup test environment: %v", err)
	}

	// Run tests
	code := m.Run()

	// Cleanup
	CleanupTestEnvironment()

	// Exit
	os.Exit(code)
}

// CreateTestUser creates a test user for testing
func CreateTestUser(email, name string) (*models.User, error) {
	userID := uuid.New()
	user := &models.User{
		ID:       userID,
		Email:    email,
		Name:     name,
		Role:     "user",
		GoogleID: "test_google_" + userID.String(), // Use unique GoogleID for each test user
	}

	if err := TestDB.Create(user).Error; err != nil {
		return nil, err
	}

	return user, nil
}

// CreateTestAPIKey creates a test API key for testing
func CreateTestAPIKey(userID uuid.UUID, name string) (*models.APIKey, error) {
	apiKey := &models.APIKey{
		ID:     uuid.New(),
		UserID: userID,
		Name:   name,
		Key:    "test_" + uuid.New().String(),
	}

	if err := TestDB.Create(apiKey).Error; err != nil {
		return nil, err
	}

	return apiKey, nil
}

// CreateTestSubscriptionTier creates a test subscription tier
func CreateTestSubscriptionTier(name string, creditLimit int) (*models.SubscriptionTier, error) {
	tier := &models.SubscriptionTier{
		Name:        name,
		Description: "Test tier",
		Price:       9.99,
		CreditLimit: creditLimit,
		Features:    models.StringSlice{"feature1", "feature2"},
	}

	if err := TestDB.Create(tier).Error; err != nil {
		return nil, err
	}

	return tier, nil
}

// CreateTestSubscription creates a test subscription
func CreateTestSubscription(userID uuid.UUID, tierID uint) (*models.Subscription, error) {
	subscription := &models.Subscription{
		ID:                 uuid.New(),
		UserID:             userID,
		SubscriptionTierID: tierID,
		Status:             "active",
		CreditBalance:      100,
		SubscriptionType:   "personal",
	}

	if err := TestDB.Create(subscription).Error; err != nil {
		return nil, err
	}

	return subscription, nil
}

// CreateTestOrganization creates a test organization
func CreateTestOrganization(name, slug string, ownerID uuid.UUID) (*models.Organization, error) {
	org := &models.Organization{
		ID:          uuid.New(),
		Name:        name,
		Slug:        slug,
		Description: "Test organization",
		OwnerUserID: ownerID,
	}

	if err := TestDB.Create(org).Error; err != nil {
		return nil, err
	}

	return org, nil
}

// CreateTestMerchantShop creates a test merchant shop
func CreateTestMerchantShop(name, slug string, ownerID uuid.UUID) (*models.MerchantShop, error) {
	shop := &models.MerchantShop{
		ID:           uuid.New(),
		Name:         name,
		Slug:         slug,
		Description:  "Test shop",
		ContactEmail: "<EMAIL>",
		ContactPhone: "+1234567890",
		OwnerUserID:  ownerID,
	}

	if err := TestDB.Create(shop).Error; err != nil {
		return nil, err
	}

	return shop, nil
}

// CreateTestShop creates a test unified shop
func CreateTestShop(name, slug string, ownerUserID uuid.UUID, shopType string) (*models.Shop, error) {
	shop := &models.Shop{
		ID:           uuid.New(),
		Name:         name,
		Slug:         slug,
		Description:  "Test shop description",
		ShopType:     shopType,
		ContactEmail: "<EMAIL>",
		ContactPhone: "+1234567890",
		OwnerUserID:  ownerUserID,
	}

	if err := TestDB.Create(shop).Error; err != nil {
		return nil, err
	}

	return shop, nil
}

// SetupTestDB initializes the test database (alias for SetupTestEnvironment)
func SetupTestDB() error {
	return SetupTestEnvironment()
}

// CleanupTestDB cleans up the test database (alias for CleanupTestEnvironment)
func CleanupTestDB() {
	CleanupTestEnvironment()
}

// CleanupAllTestData removes all test data (alias for CleanTestData)
func CleanupAllTestData() {
	CleanTestData()
}
