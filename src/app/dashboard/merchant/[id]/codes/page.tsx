"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import Link from "next/link";
import { But<PERSON> } from "@/components/ui/button";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { useToast } from "@/components/ui/use-toast";
import { ArrowLeft, Plus, QrCode, Copy, Eye } from "phosphor-react";
import {
  useGetMerchantShopQuery,
  useGetCreditCodesQuery,
  useGenerateCreditCodeMutation
} from "@/lib/api/apiSlice";

export default function CreditCodesPage({ params }: { params: { id: string } }) {
  const router = useRouter();
  const { toast } = useToast();
  
  // Fetch shop and credit codes data
  const { data: shop, isLoading: isShopLoading } = useGetMerchantShopQuery(params.id);
  const { data: creditCodes, isLoading: isCodesLoading, refetch: refetchCodes } = useGetCreditCodesQuery(params.id);
  
  // Generate code mutation
  const [generateCode] = useGenerateCreditCodeMutation();
  
  // Dialog state
  const [isGenerateDialogOpen, setIsGenerateDialogOpen] = useState(false);
  
  // Form state
  const [codeAmount, setCodeAmount] = useState(0);
  const [codeDescription, setCodeDescription] = useState("");
  const [codeExpiresIn, setCodeExpiresIn] = useState(30);
  
  // Handle generating a credit code
  const handleGenerateCode = async () => {
    if (codeAmount <= 0) {
      toast({
        title: "Error",
        description: "Code amount must be greater than 0",
        variant: "destructive",
      });
      return;
    }
    
    try {
      const result = await generateCode({
        shopId: params.id,
        amount: codeAmount,
        description: codeDescription,
        expiresIn: codeExpiresIn
      }).unwrap();
      
      toast({
        title: "Success",
        description: "Credit code generated successfully",
      });
      
      setCodeAmount(0);
      setCodeDescription("");
      setIsGenerateDialogOpen(false);
      refetchCodes();
      
      // Navigate to the code detail page to show the QR code
      router.push(`/dashboard/merchant/${params.id}/codes/${result.credit_code.id}`);
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.data?.message || "Failed to generate code",
        variant: "destructive",
      });
    }
  };
  
  const copyToClipboard = (code: string) => {
    navigator.clipboard.writeText(code);
    toast({
      title: "Copied to clipboard",
      description: "Credit code has been copied to clipboard",
    });
  };
  
  if (isShopLoading) {
    return <div className="p-4 text-center">Loading shop details...</div>;
  }
  
  if (!shop) {
    return <div className="p-4 text-center">Shop not found</div>;
  }

  return (
    <div className="relative flex size-full min-h-screen flex-col bg-[#fbfaf9] justify-between group/design-root overflow-x-hidden">
      <div>
        <div className="flex items-center bg-[#fbfaf9] p-4 pb-2 justify-between">
          <Link href={`/dashboard/merchant/${params.id}`} className="text-[#181510] flex size-12 shrink-0 items-center">
            <ArrowLeft size={24} weight="regular" />
          </Link>
          <h2 className="text-[#181510] text-lg font-bold leading-tight tracking-[-0.015em] flex-1 text-center pr-12">Credit Codes</h2>
        </div>
        
        <div className="p-4">
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-lg font-medium">Credit Codes for {shop.name}</h3>
            <Button onClick={() => setIsGenerateDialogOpen(true)}>
              <Plus size={16} className="mr-2" />
              Generate Code
            </Button>
          </div>
          
          {isCodesLoading ? (
            <div className="text-center p-4">Loading credit codes...</div>
          ) : creditCodes && creditCodes.length > 0 ? (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Code</TableHead>
                  <TableHead>Amount</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Expires</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {creditCodes.map((code) => (
                  <TableRow key={code.id}>
                    <TableCell className="font-mono">{code.code}</TableCell>
                    <TableCell>${code.amount}</TableCell>
                    <TableCell>{code.is_used ? "Used" : "Available"}</TableCell>
                    <TableCell>{code.expires_at ? new Date(code.expires_at).toLocaleDateString() : "Never"}</TableCell>
                    <TableCell>
                      <div className="flex gap-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => router.push(`/dashboard/merchant/${params.id}/codes/${code.id}`)}
                        >
                          <Eye size={16} />
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => copyToClipboard(code.code)}
                          disabled={code.is_used}
                        >
                          <Copy size={16} />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          ) : (
            <div className="text-center p-8 border rounded-lg bg-muted">
              <QrCode size={48} className="mx-auto mb-4 text-muted-foreground" />
              <p className="text-muted-foreground mb-4">No credit codes generated yet</p>
              <Button onClick={() => setIsGenerateDialogOpen(true)}>
                Generate Your First Code
              </Button>
            </div>
          )}
        </div>
      </div>
      
      {/* Generate Code Dialog */}
      <Dialog open={isGenerateDialogOpen} onOpenChange={setIsGenerateDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Generate Credit Code</DialogTitle>
            <DialogDescription>
              Create a credit code that customers can redeem
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid gap-2">
              <Label htmlFor="codeAmount">Credit Amount</Label>
              <Input 
                id="codeAmount" 
                type="number"
                min="1"
                value={codeAmount || ""}
                onChange={(e) => setCodeAmount(parseInt(e.target.value) || 0)}
                placeholder="Amount of credits"
              />
            </div>
            
            <div className="grid gap-2">
              <Label htmlFor="codeDescription">Description (Optional)</Label>
              <Input 
                id="codeDescription" 
                value={codeDescription}
                onChange={(e) => setCodeDescription(e.target.value)}
                placeholder="Purpose of this code"
              />
            </div>
            
            <div className="grid gap-2">
              <Label htmlFor="expiresIn">Expires In (Days)</Label>
              <Input 
                id="expiresIn" 
                type="number"
                min="0"
                value={codeExpiresIn || ""}
                onChange={(e) => setCodeExpiresIn(parseInt(e.target.value) || 0)}
                placeholder="0 for no expiration"
              />
              <p className="text-xs text-muted-foreground">Set to 0 for no expiration</p>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsGenerateDialogOpen(false)}>
              Cancel
            </Button>
            <Button 
              onClick={handleGenerateCode}
              disabled={codeAmount <= 0}
            >
              Generate Code
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
      
      <div>
        <div className="flex justify-stretch">
          <div className="flex flex-1 gap-3 flex-wrap px-4 py-3 justify-between">
            <Button 
              className="flex w-full cursor-pointer items-center justify-center overflow-hidden rounded-lg h-12 px-5 bg-[#e5ccb2] text-[#181510] text-base font-bold leading-normal tracking-[0.015em]"
              onClick={() => router.push(`/dashboard/merchant/${params.id}`)}
            >
              <span className="truncate">Back to Shop</span>
            </Button>
          </div>
        </div>
        <div className="h-5 bg-[#fbfaf9]"></div>
      </div>
    </div>
  );
}
