import { User as NextA<PERSON>User } from "next-auth";

// Extend the built-in session types
declare module "next-auth" {
  interface Session {
    accessToken?: string;
    error?: string;
    user: User & NextAuthUser & {
      accessToken?: string; // Add accessToken to user object as well
    };
  }

  interface User {
    id: string;
    email: string;
    name: string;
    picture: string;
    role: string;
    accessToken?: string; // Add accessToken to User interface
  }
}

// API Types
export interface APIKey {
  id: string;
  user_id: string;
  name: string;
  key: string;
  last_used?: string;
  enabled: boolean;
  permissions: string[];
  rate_limit_max: number;
  rate_limit_rate: number;
  created_at: string;
  updated_at: string;
}



export interface SubscriptionTier {
  id: number;
  name: string;
  description: string;
  price: number;
  credit_limit: number;
  features: string[];
  default_rate_limit_max: number;
  default_rate_limit_rate: number;
  max_webhooks: number;
  advanced_analytics: boolean;
  created_at: string;
  updated_at: string;
}

export interface Subscription {
  id: string;
  user_id: string;
  subscription_tier_id: number;
  subscription_tier: SubscriptionTier;
  start_date: string;
  end_date?: string;
  auto_renew: boolean;
  status: string;
  credit_balance: number;
  subscription_type: 'personal' | 'merchant' | 'customer';
  merchant_shop_id?: string;
  merchant_shop?: MerchantShop;
  shop_customer_id?: string;
  shop_customer?: ShopCustomer;
  created_at: string;
  updated_at: string;
}

export interface Transaction {
  id: string;
  user_id: string;
  subscription_id: string;
  type: "credit_add" | "credit_use" | "credit_scheduled";
  amount: number;
  description: string;
  reference: string;
  created_at: string;
  updated_at: string;
}

export interface Usage {
  id: string;
  api_key_id: string;
  endpoint: string;
  method: string;
  credits: number;
  timestamp: string;
  success: boolean;
  ip_address: string;
  user_agent: string;
  response_time: number;
  status_code: number;
  created_at: string;
  updated_at: string;
}

export interface UsageSummary {
  total_usage: number;
  total_credits: number;
  api_keys: APIKeySummary[];
  time_series: TimePeriodSummary[];
}

export interface APIKeySummary {
  id: string;
  name: string;
  total_usage: number;
  total_credits: number;
  endpoints: EndpointSummary[];
}

export interface EndpointSummary {
  endpoint: string;
  total_usage: number;
  total_credits: number;
}

export interface TimePeriodSummary {
  period: string;
  total_usage: number;
  total_credits: number;
}

// Webhook types
export interface Webhook {
  id: string;
  user_id: string;
  name: string;
  url: string;
  secret: string;
  events: string[];
  active: boolean;
  last_called?: string;
  fail_count: number;
  created_at: string;
  updated_at: string;
}

export interface WebhookDelivery {
  id: string;
  webhook_id: string;
  event: string;
  payload: string;
  status_code: number;
  response: string;
  success: boolean;
  duration: number;
  created_at: string;
  updated_at: string;
}

// Analytics types
export interface AnalyticsSummary {
  total_requests: number;
  total_credits: number;
  avg_response_time: number;
  error_rate: number;
  p95_response_time: number;
  p99_response_time: number;
  start_date: string;
  end_date: string;
  subscription_tier: string;
}

export interface AnalyticsTrend {
  period: string;
  total_requests: number;
  total_credits: number;
  avg_response_time: number;
  error_rate: number;
}

export interface EndpointAnalytics {
  endpoint: string;
  total_requests: number;
  total_credits: number;
  avg_response_time: number;
  error_rate: number;
  p95_response_time: number;
  p99_response_time: number;
}

export interface PerformanceMetrics {
  metric: string;
  data: {
    date: string;
    value: number;
  }[];
}

// Unified Shop types
export interface Shop {
  id: string;
  slug: string;
  name: string;
  description: string;
  shop_type: 'retail' | 'api_service' | 'enterprise';
  contact_email: string;
  contact_phone: string;
  owner_user_id: string;
  branches?: ShopBranch[];
  customers?: ShopCustomer[];
  credit_codes?: CreditCode[];
  api_keys?: APIKey[];
  created_at: string;
  updated_at: string;
}

export interface ShopBranch {
  id: string;
  shop_id: string;
  name: string;
  description: string;
  branch_type: 'location' | 'department' | 'division';
  contact_email: string;
  contact_phone: string;
  address: string;
  users?: ExternalUser[];
  api_keys?: APIKey[];
  created_at: string;
  updated_at: string;
}

// Legacy types (for backward compatibility)
export interface Organization {
  id: string;
  name: string;
  slug: string;
  description: string;
  owner_user_id: string;
  branches?: Branch[];
  created_at: string;
  updated_at: string;
}

export interface Branch {
  id: string;
  organization_id: string;
  name: string;
  description: string;
  users?: ExternalUser[];
  created_at: string;
  updated_at: string;
}

export interface ExternalUser {
  id: string;
  email: string;
  name: string;
  picture?: string;
  google_id?: string;
  role: string;
  organization_id: string;
  branch_id: string;
  monthly_credits: number;
  next_credit_reset_date: string;
  is_external_user: boolean;
  external_user_identifier: string;
  subscriptions?: Subscription[];
  created_at: string;
  updated_at: string;
}

export interface ExternalUserCreditResponse {
  credit_balance: number;
  message: string;
}

// Merchant types
export interface MerchantShop {
  id: string;
  slug: string;
  name: string;
  description: string;
  contact_email: string;
  contact_phone: string;
  owner_user_id: string;
  customers?: ShopCustomer[];
  credit_codes?: CreditCode[];
  created_at: string;
  updated_at: string;
}

export interface ShopCustomer {
  id: string;
  shop_id: string;
  user_id: string;
  phone?: string;
  credit_balance: number;
  created_at: string;
  updated_at: string;
  user?: {
    name: string;
    email: string;
    phone?: string;
  };
}

export interface CreditCode {
  id: string;
  shop_id: string;
  code: string;
  amount: number;
  description: string;
  is_used: boolean;
  used_by_user_id?: string;
  expires_at?: string;
  created_at: string;
  updated_at: string;
}
