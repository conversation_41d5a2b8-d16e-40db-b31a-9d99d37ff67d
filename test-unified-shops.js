/**
 * Test script for the unified shops API
 * This script tests the new unified shops endpoints
 */

const API_URL = 'http://localhost:8400';

async function testUnifiedShopsAPI() {
  console.log('🧪 Testing Unified Shops API...\n');

  try {
    // Test 1: Check if the shops endpoint exists
    console.log('1. Testing GET /api/v1/shops endpoint...');
    const response = await fetch(`${API_URL}/api/v1/shops`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    console.log(`   Status: ${response.status}`);
    
    if (response.status === 401) {
      console.log('   ✅ Endpoint exists (requires authentication)');
    } else if (response.status === 200) {
      const data = await response.json();
      console.log('   ✅ Endpoint accessible');
      console.log(`   📊 Found ${data.length || 0} shops`);
    } else {
      console.log(`   ❌ Unexpected status: ${response.status}`);
    }

    // Test 2: Check if the create shop endpoint exists
    console.log('\n2. Testing POST /api/v1/shops endpoint...');
    const createResponse = await fetch(`${API_URL}/api/v1/shops`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        name: 'Test Shop',
        shop_type: 'retail',
      }),
    });

    console.log(`   Status: ${createResponse.status}`);
    
    if (createResponse.status === 401) {
      console.log('   ✅ Endpoint exists (requires authentication)');
    } else if (createResponse.status === 201) {
      console.log('   ✅ Shop creation endpoint working');
    } else {
      console.log(`   ❌ Unexpected status: ${createResponse.status}`);
    }

    // Test 3: Check if the branches endpoint exists
    console.log('\n3. Testing GET /api/v1/shops/branches endpoint...');
    const branchesResponse = await fetch(`${API_URL}/api/v1/shops/branches?shop_id=test`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    console.log(`   Status: ${branchesResponse.status}`);
    
    if (branchesResponse.status === 401) {
      console.log('   ✅ Branches endpoint exists (requires authentication)');
    } else if (branchesResponse.status === 400) {
      console.log('   ✅ Branches endpoint exists (invalid shop_id)');
    } else {
      console.log(`   ❌ Unexpected status: ${branchesResponse.status}`);
    }

    // Test 4: Check server health
    console.log('\n4. Testing server health...');
    const healthResponse = await fetch(`${API_URL}/health`, {
      method: 'GET',
    });

    if (healthResponse.status === 200) {
      console.log('   ✅ Server is healthy');
    } else {
      console.log(`   ⚠️  Server health check failed: ${healthResponse.status}`);
    }

    console.log('\n🎉 Unified Shops API test completed!');
    console.log('\n📋 Summary:');
    console.log('   - Unified shops endpoints are available');
    console.log('   - Authentication is properly configured');
    console.log('   - Ready for frontend integration');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    
    if (error.code === 'ECONNREFUSED') {
      console.log('\n💡 Make sure the backend server is running:');
      console.log('   cd backend && go run cmd/api/main.go');
    }
  }
}

// Run the test
testUnifiedShopsAPI();
