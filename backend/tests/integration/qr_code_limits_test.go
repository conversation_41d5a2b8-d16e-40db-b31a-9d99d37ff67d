package integration

import (
	"bytes"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/adc-credit/backend/internal/database"
	"github.com/adc-credit/backend/internal/models"
	"github.com/adc-credit/backend/internal/routes"
	testutils "github.com/adc-credit/backend/internal/testing"
	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/stretchr/testify/suite"
)

type QRCodeLimitsTestSuite struct {
	suite.Suite
	router *gin.Engine
}

func (suite *QRCodeLimitsTestSuite) SetupSuite() {
	err := testutils.SetupTestDB()
	suite.Require().NoError(err, "Failed to setup test database")

	err = database.InitDB()
	suite.Require().NoError(err, "Failed to initialize database")

	gin.SetMode(gin.TestMode)
	suite.router = gin.New()
	routes.RegisterRoutes(suite.router)
}

func (suite *QRCodeLimitsTestSuite) TearDownSuite() {
	testutils.CleanupTestDB()
}

func (suite *QRCodeLimitsTestSuite) SetupTest() {
	testutils.CleanupAllTestData()
}

// Helper function to create subscription tier with specific QR code limit
func (suite *QRCodeLimitsTestSuite) createQRCodeLimitTier(name string, qrCodeLimit int, unlimited bool) *models.SubscriptionTier {
	db := database.DB

	tier := &models.SubscriptionTier{
		Name:                 name,
		Description:          fmt.Sprintf("%s plan for QR code testing", name),
		Price:                0.00,
		CreditLimit:          100000,
		Features:             models.StringSlice{"QR Code testing"},
		MaxShops:             1,
		MaxCustomersPerShop:  50,
		MaxAPIKeysPerShop:    1,
		MaxBranchesPerShop:   0,
		MaxQRCodesPerMonth:   qrCodeLimit,
		MaxWebhooks:          0,
		AnalyticsHistoryDays: 30,
		SupportLevel:         "community",
		AllowedShopTypes:     models.StringSlice{"retail"},
		UnlimitedShops:       false,
		UnlimitedCustomers:   false,
		UnlimitedQRCodes:     unlimited,
		UnlimitedBranches:    false,
	}

	suite.Require().NoError(db.Create(tier).Error)
	return tier
}

// Helper function to create user with subscription
func (suite *QRCodeLimitsTestSuite) createUserWithSubscription(email, name string, tier *models.SubscriptionTier) (*models.User, *models.Subscription, string) {
	user, token := testutils.CreateTestUserWithAuth(suite.T(), email, name)

	subscription := &models.Subscription{
		ID:                 uuid.New(),
		UserID:             user.ID,
		SubscriptionTierID: tier.ID,
		StartDate:          time.Now(),
		AutoRenew:          true,
		Status:             "active",
		CreditBalance:      tier.CreditLimit,
		SubscriptionType:   "personal",
	}

	suite.Require().NoError(database.DB.Create(subscription).Error)

	return user, subscription, token
}

// Helper function to create QR codes for testing
func (suite *QRCodeLimitsTestSuite) createQRCodesForUser(userID uuid.UUID, shopID uuid.UUID, count int) {
	db := database.DB

	for i := 0; i < count; i++ {
		creditCode := &models.CreditCode{
			ID:          uuid.New(),
			ShopID:      shopID,
			Code:        fmt.Sprintf("TEST-QR-%d-%d", userID.ID(), i),
			Amount:      100,
			Description: fmt.Sprintf("Test QR code %d", i+1),
			IsRedeemed:  false,
			CreatedAt:   time.Now(),
		}

		suite.Require().NoError(db.Create(creditCode).Error)
	}
}

// Test QR code generation limits
func (suite *QRCodeLimitsTestSuite) TestQRCodeGenerationLimits() {
	// Create tier with 3 QR codes per month limit
	tier := suite.createQRCodeLimitTier("Limited", 3, false)
	user, _, token := suite.createUserWithSubscription("<EMAIL>", "Limited User", tier)

	// Create a shop
	shop, err := testutils.CreateTestShop("Test Shop", "test-shop", user.ID, "retail")
	suite.Require().NoError(err)

	// Generate 3 QR codes (should all succeed)
	for i := 1; i <= 3; i++ {
		qrData := map[string]interface{}{
			"amount":      100,
			"description": fmt.Sprintf("Test QR Code %d", i),
		}

		body, _ := json.Marshal(qrData)
		req := httptest.NewRequest("POST", fmt.Sprintf("/api/v1/shops/%s/credit-codes/qr", shop.ID), bytes.NewBuffer(body))
		req.Header.Set("Content-Type", "application/json")
		req.Header.Set("Authorization", "Bearer "+token)

		w := httptest.NewRecorder()
		suite.router.ServeHTTP(w, req)

		suite.Equal(http.StatusCreated, w.Code, "Failed to create QR code %d", i)
	}

	// 4th QR code should fail
	qrData4 := map[string]interface{}{
		"amount":      100,
		"description": "Should fail",
	}

	body4, _ := json.Marshal(qrData4)
	req4 := httptest.NewRequest("POST", fmt.Sprintf("/api/v1/shops/%s/credit-codes/qr", shop.ID), bytes.NewBuffer(body4))
	req4.Header.Set("Content-Type", "application/json")
	req4.Header.Set("Authorization", "Bearer "+token)

	w4 := httptest.NewRecorder()
	suite.router.ServeHTTP(w4, req4)

	suite.Equal(http.StatusForbidden, w4.Code)

	var response map[string]interface{}
	json.Unmarshal(w4.Body.Bytes(), &response)
	suite.Contains(response["message"], "QR code limit reached")

	// Cleanup
	testutils.CleanupTestUser(suite.T(), user.ID)
}

// Test unlimited QR code generation
func (suite *QRCodeLimitsTestSuite) TestUnlimitedQRCodeGeneration() {
	// Create tier with unlimited QR codes
	tier := suite.createQRCodeLimitTier("Unlimited", 0, true)
	user, _, token := suite.createUserWithSubscription("<EMAIL>", "Unlimited User", tier)

	// Create a shop
	shop, err := testutils.CreateTestShop("Test Shop", "test-shop", user.ID, "retail")
	suite.Require().NoError(err)

	// Generate 100 QR codes (should all succeed)
	for i := 1; i <= 100; i++ {
		qrData := map[string]interface{}{
			"amount":      100,
			"description": fmt.Sprintf("Test QR Code %d", i),
		}

		body, _ := json.Marshal(qrData)
		req := httptest.NewRequest("POST", fmt.Sprintf("/api/v1/shops/%s/credit-codes/qr", shop.ID), bytes.NewBuffer(body))
		req.Header.Set("Content-Type", "application/json")
		req.Header.Set("Authorization", "Bearer "+token)

		w := httptest.NewRecorder()
		suite.router.ServeHTTP(w, req)

		suite.Equal(http.StatusCreated, w.Code, "Failed to create QR code %d", i)
	}

	// Cleanup
	testutils.CleanupTestUser(suite.T(), user.ID)
}

// Test QR code limit check API with existing QR codes
func (suite *QRCodeLimitsTestSuite) TestQRCodeLimitCheckWithExistingCodes() {
	// Create tier with 5 QR codes per month limit
	tier := suite.createQRCodeLimitTier("TestLimit", 5, false)
	user, _, token := suite.createUserWithSubscription("<EMAIL>", "Test User", tier)

	// Create a shop
	shop, err := testutils.CreateTestShop("Test Shop", "test-shop", user.ID, "retail")
	suite.Require().NoError(err)

	// Create 3 existing QR codes
	suite.createQRCodesForUser(user.ID, shop.ID, 3)

	// Check QR code limit
	req := httptest.NewRequest("GET", "/api/v1/subscription-limits/qr-codes/check", nil)
	req.Header.Set("Authorization", "Bearer "+token)

	w := httptest.NewRecorder()
	suite.router.ServeHTTP(w, req)

	suite.Equal(http.StatusOK, w.Code)

	var response map[string]interface{}
	err = json.Unmarshal(w.Body.Bytes(), &response)
	suite.Require().NoError(err)

	suite.Equal(true, response["allowed"])
	suite.Equal(float64(3), response["current_usage"])
	suite.Equal(float64(5), response["limit"])

	// Create 2 more QR codes to reach the limit
	suite.createQRCodesForUser(user.ID, shop.ID, 2)

	// Check QR code limit again
	req2 := httptest.NewRequest("GET", "/api/v1/subscription-limits/qr-codes/check", nil)
	req2.Header.Set("Authorization", "Bearer "+token)

	w2 := httptest.NewRecorder()
	suite.router.ServeHTTP(w2, req2)

	suite.Equal(http.StatusOK, w2.Code)

	var response2 map[string]interface{}
	err = json.Unmarshal(w2.Body.Bytes(), &response2)
	suite.Require().NoError(err)

	suite.Equal(false, response2["allowed"])
	suite.Equal(float64(5), response2["current_usage"])
	suite.Equal(float64(5), response2["limit"])

	// Cleanup
	testutils.CleanupTestUser(suite.T(), user.ID)
}

// Test QR code limits reset monthly
func (suite *QRCodeLimitsTestSuite) TestQRCodeLimitsResetMonthly() {
	// Create tier with 2 QR codes per month limit
	tier := suite.createQRCodeLimitTier("Monthly", 2, false)
	user, _, token := suite.createUserWithSubscription("<EMAIL>", "Monthly User", tier)

	// Create a shop
	shop, err := testutils.CreateTestShop("Test Shop", "test-shop", user.ID, "retail")
	suite.Require().NoError(err)

	// Create QR codes from last month (should not count towards current limit)
	db := database.DB
	lastMonth := time.Now().AddDate(0, -1, 0)

	for i := 0; i < 5; i++ {
		creditCode := &models.CreditCode{
			ID:          uuid.New(),
			ShopID:      shop.ID,
			Code:        fmt.Sprintf("LAST-MONTH-%d", i),
			Amount:      100,
			Description: fmt.Sprintf("Last month QR code %d", i+1),
			IsRedeemed:  false,
			CreatedAt:   lastMonth,
		}

		suite.Require().NoError(db.Create(creditCode).Error)
	}

	// Check QR code limit (should show 0 current usage)
	req := httptest.NewRequest("GET", "/api/v1/subscription-limits/qr-codes/check", nil)
	req.Header.Set("Authorization", "Bearer "+token)

	w := httptest.NewRecorder()
	suite.router.ServeHTTP(w, req)

	suite.Equal(http.StatusOK, w.Code)

	var response map[string]interface{}
	err = json.Unmarshal(w.Body.Bytes(), &response)
	suite.Require().NoError(err)

	suite.Equal(true, response["allowed"])
	suite.Equal(float64(0), response["current_usage"]) // Should be 0 since last month's codes don't count
	suite.Equal(float64(2), response["limit"])

	// Should be able to create 2 QR codes this month
	for i := 1; i <= 2; i++ {
		qrData := map[string]interface{}{
			"amount":      100,
			"description": fmt.Sprintf("This month QR Code %d", i),
		}

		body, _ := json.Marshal(qrData)
		req := httptest.NewRequest("POST", fmt.Sprintf("/api/v1/shops/%s/credit-codes/qr", shop.ID), bytes.NewBuffer(body))
		req.Header.Set("Content-Type", "application/json")
		req.Header.Set("Authorization", "Bearer "+token)

		w := httptest.NewRecorder()
		suite.router.ServeHTTP(w, req)

		suite.Equal(http.StatusCreated, w.Code, "Failed to create QR code %d", i)
	}

	// 3rd QR code should fail
	qrData3 := map[string]interface{}{
		"amount":      100,
		"description": "Should fail",
	}

	body3, _ := json.Marshal(qrData3)
	req3 := httptest.NewRequest("POST", fmt.Sprintf("/api/v1/shops/%s/credit-codes/qr", shop.ID), bytes.NewBuffer(body3))
	req3.Header.Set("Content-Type", "application/json")
	req3.Header.Set("Authorization", "Bearer "+token)

	w3 := httptest.NewRecorder()
	suite.router.ServeHTTP(w3, req3)

	suite.Equal(http.StatusForbidden, w3.Code)

	// Cleanup
	testutils.CleanupTestUser(suite.T(), user.ID)
}

func TestQRCodeLimitsTestSuite(t *testing.T) {
	suite.Run(t, new(QRCodeLimitsTestSuite))
}
