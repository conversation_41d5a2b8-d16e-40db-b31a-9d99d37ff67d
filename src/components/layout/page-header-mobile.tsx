"use client";

import { ReactNode } from "react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { Button } from "@/components/ui/button";
import { ArrowLeft } from "phosphor-react";
import { cn } from "@/lib/utils";

interface PageHeaderMobileProps {
  title: string;
  backHref?: string;
  backLabel?: string;
  rightContent?: ReactNode;
  className?: string;
  titleClassName?: string;
  showBackButton?: boolean;
  onBackClick?: () => void;
}

/**
 * A reusable mobile-friendly page header component with back button and title
 */
export function PageHeaderMobile({
  title,
  backHref,
  backLabel,
  rightContent,
  className,
  titleClassName,
  showBackButton = true,
  onBackClick,
}: PageHeaderMobileProps) {
  const router = useRouter();

  const handleBackClick = () => {
    if (onBackClick) {
      onBackClick();
    } else if (backHref) {
      router.push(backHref);
    } else {
      router.back();
    }
  };

  return (
    <div className={cn("flex items-center bg-[#fbfaf9] p-4 pb-2 justify-between", className)}>
      {showBackButton ? (
        <Link 
          href={backHref || "#"} 
          className="text-[#181510] flex size-12 shrink-0 items-center"
          onClick={(e) => {
            if (!backHref || onBackClick) {
              e.preventDefault();
              handleBackClick();
            }
          }}
        >
          <ArrowLeft size={24} weight="regular" />
          {backLabel && <span className="sr-only">{backLabel}</span>}
        </Link>
      ) : (
        <div className="size-12 shrink-0"></div>
      )}
      
      <h2 className={cn("text-[#181510] text-lg font-bold leading-tight tracking-[-0.015em] flex-1 text-center", titleClassName)}>
        {title}
      </h2>
      
      {rightContent ? (
        <div className="flex w-12 shrink-0 items-center justify-end">
          {rightContent}
        </div>
      ) : (
        <div className="size-12 shrink-0"></div>
      )}
    </div>
  );
}

/**
 * Merchant-specific header with custom styling
 */
export function MerchantPageHeader({
  title,
  backHref = "/dashboard/merchant",
  rightContent,
  showBackButton = true,
  ...props
}: PageHeaderMobileProps) {
  return (
    <PageHeaderMobile
      title={title}
      backHref={backHref}
      rightContent={rightContent}
      showBackButton={showBackButton}
      {...props}
    />
  );
}

/**
 * Customer-specific header with custom styling
 */
export function CustomerPageHeader({
  title,
  backHref = "/dashboard/customer",
  rightContent,
  showBackButton = true,
  ...props
}: PageHeaderMobileProps) {
  return (
    <PageHeaderMobile
      title={title}
      backHref={backHref}
      rightContent={rightContent}
      showBackButton={showBackButton}
      {...props}
    />
  );
}
