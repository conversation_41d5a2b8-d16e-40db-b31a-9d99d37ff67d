<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>QR Code Generator for Testing</title>
    <script src="https://cdn.jsdelivr.net/npm/qrcode@1.5.3/build/qrcode.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .qr-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background: #f9f9f9;
        }
        .qr-code {
            text-align: center;
            margin: 10px 0;
        }
        .qr-data {
            font-family: monospace;
            background: #e9e9e9;
            padding: 10px;
            border-radius: 3px;
            word-break: break-all;
            margin: 10px 0;
        }
        h1 {
            color: #333;
            text-align: center;
        }
        h2 {
            color: #555;
            border-bottom: 2px solid #eee;
            padding-bottom: 5px;
        }
        .shop-info {
            background: #e8f4f8;
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 10px;
        }
        .amount {
            font-weight: bold;
            color: #2c5aa0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>QR Code Generator for Credit Code Testing</h1>
        <p>These QR codes contain test credit codes that you can scan with your app.</p>
        
        <h2>Coffee Corner</h2>
        <div class="shop-info">
            <strong>Shop ID:</strong> 0d70e0d7-3dd4-4037-ab66-4b89da94aece<br>
            <strong>Shop Name:</strong> Coffee Corner
        </div>
        
        <div class="qr-section">
            <h3>$10 Credit Code</h3>
            <div class="qr-data">shop:0d70e0d7-3dd4-4037-ab66-4b89da94aece;code:TESTcoff01;amount:10</div>
            <div class="qr-code" id="qr-coffee-10"></div>
            <p><span class="amount">Amount: $10</span> | Code: TESTcoff01</p>
        </div>
        
        <div class="qr-section">
            <h3>$25 Credit Code</h3>
            <div class="qr-data">shop:0d70e0d7-3dd4-4037-ab66-4b89da94aece;code:TESTcoff02;amount:25</div>
            <div class="qr-code" id="qr-coffee-25"></div>
            <p><span class="amount">Amount: $25</span> | Code: TESTcoff02</p>
        </div>
        
        <div class="qr-section">
            <h3>$50 Credit Code</h3>
            <div class="qr-data">shop:0d70e0d7-3dd4-4037-ab66-4b89da94aece;code:TESTcoff03;amount:50</div>
            <div class="qr-code" id="qr-coffee-50"></div>
            <p><span class="amount">Amount: $50</span> | Code: TESTcoff03</p>
        </div>

        <h2>Tech Gadgets Store</h2>
        <div class="shop-info">
            <strong>Shop ID:</strong> 80ae8bd0-e5b0-4b81-84f9-317138c2c654<br>
            <strong>Shop Name:</strong> Tech Gadgets Store
        </div>
        
        <div class="qr-section">
            <h3>$10 Credit Code</h3>
            <div class="qr-data">shop:80ae8bd0-e5b0-4b81-84f9-317138c2c654;code:TESTtech01;amount:10</div>
            <div class="qr-code" id="qr-tech-10"></div>
            <p><span class="amount">Amount: $10</span> | Code: TESTtech01</p>
        </div>
        
        <div class="qr-section">
            <h3>$25 Credit Code</h3>
            <div class="qr-data">shop:80ae8bd0-e5b0-4b81-84f9-317138c2c654;code:TESTtech02;amount:25</div>
            <div class="qr-code" id="qr-tech-25"></div>
            <p><span class="amount">Amount: $25</span> | Code: TESTtech02</p>
        </div>

        <h2>Bookworm Paradise</h2>
        <div class="shop-info">
            <strong>Shop ID:</strong> ca0ac335-81ff-47f1-934e-6872951893d2<br>
            <strong>Shop Name:</strong> Bookworm Paradise
        </div>
        
        <div class="qr-section">
            <h3>$10 Credit Code</h3>
            <div class="qr-data">shop:ca0ac335-81ff-47f1-934e-6872951893d2;code:TESTbook01;amount:10</div>
            <div class="qr-code" id="qr-book-10"></div>
            <p><span class="amount">Amount: $10</span> | Code: TESTbook01</p>
        </div>
    </div>

    <script>
        // Generate QR codes
        const qrCodes = [
            {
                id: 'qr-coffee-10',
                data: 'shop:0d70e0d7-3dd4-4037-ab66-4b89da94aece;code:TESTcoff01;amount:10'
            },
            {
                id: 'qr-coffee-25',
                data: 'shop:0d70e0d7-3dd4-4037-ab66-4b89da94aece;code:TESTcoff02;amount:25'
            },
            {
                id: 'qr-coffee-50',
                data: 'shop:0d70e0d7-3dd4-4037-ab66-4b89da94aece;code:TESTcoff03;amount:50'
            },
            {
                id: 'qr-tech-10',
                data: 'shop:80ae8bd0-e5b0-4b81-84f9-317138c2c654;code:TESTtech01;amount:10'
            },
            {
                id: 'qr-tech-25',
                data: 'shop:80ae8bd0-e5b0-4b81-84f9-317138c2c654;code:TESTtech02;amount:25'
            },
            {
                id: 'qr-book-10',
                data: 'shop:ca0ac335-81ff-47f1-934e-6872951893d2;code:TESTbook01;amount:10'
            }
        ];

        // Generate all QR codes
        qrCodes.forEach(qr => {
            QRCode.toCanvas(document.getElementById(qr.id), qr.data, {
                width: 200,
                margin: 2,
                color: {
                    dark: '#000000',
                    light: '#FFFFFF'
                }
            }, function (error) {
                if (error) console.error('Error generating QR code:', error);
            });
        });
    </script>
</body>
</html>
