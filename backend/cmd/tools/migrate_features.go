package main

import (
	"encoding/json"
	"fmt"
	"log"

	"github.com/adc-credit/backend/internal/database"
	"github.com/joho/godotenv"
)

func main() {
	// Load environment variables
	if err := godotenv.Load(); err != nil {
		log.Println("No .env file found, using environment variables")
	}

	// Initialize database connection
	if err := database.InitDB(); err != nil {
		log.Fatalf("Failed to initialize database: %v", err)
	}

	// Migrate features column
	if err := migrateFeatures(); err != nil {
		log.Fatalf("Failed to migrate features column: %v", err)
	}

	log.Println("Migration completed successfully")
}

func migrateFeatures() error {
	// Get all subscription tiers
	type SubscriptionTier struct {
		ID       uint
		Features string
	}

	var tiers []SubscriptionTier
	if err := database.DB.Table("subscription_tiers").Select("id, features").Find(&tiers).Error; err != nil {
		return fmt.Errorf("failed to fetch subscription tiers: %w", err)
	}

	// Begin transaction
	tx := database.DB.Begin()
	if tx.Error != nil {
		return fmt.Errorf("failed to begin transaction: %w", tx.Error)
	}

	// Drop the existing features column
	if err := tx.Exec("ALTER TABLE subscription_tiers DROP COLUMN features").Error; err != nil {
		tx.Rollback()
		return fmt.Errorf("failed to drop features column: %w", err)
	}

	// Add the new features column as text
	if err := tx.Exec("ALTER TABLE subscription_tiers ADD COLUMN features TEXT").Error; err != nil {
		tx.Rollback()
		return fmt.Errorf("failed to add new features column: %w", err)
	}

	// Update each tier with JSON-encoded features
	for _, tier := range tiers {
		// Parse the features string
		var features []string
		if tier.Features != "" {
			// Try to parse as array
			if err := json.Unmarshal([]byte(tier.Features), &features); err != nil {
				// If not an array, treat as a single string
				features = []string{tier.Features}
			}
		}

		// Convert features to JSON
		featuresJSON, err := json.Marshal(features)
		if err != nil {
			tx.Rollback()
			return fmt.Errorf("failed to marshal features for tier %d: %w", tier.ID, err)
		}

		// Update the tier
		if err := tx.Exec("UPDATE subscription_tiers SET features = ? WHERE id = ?", string(featuresJSON), tier.ID).Error; err != nil {
			tx.Rollback()
			return fmt.Errorf("failed to update features for tier %d: %w", tier.ID, err)
		}
	}

	// Commit transaction
	if err := tx.Commit().Error; err != nil {
		return fmt.Errorf("failed to commit transaction: %w", err)
	}

	return nil
}
