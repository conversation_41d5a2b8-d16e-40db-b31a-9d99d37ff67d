'use client';

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { useSession } from 'next-auth/react';
import { 
  getAccessToken, 
  getStoredSessionToken, 
  storeSessionToken, 
  getStoredUser, 
  clearSessionData 
} from './sessionManager';

// Define the session context type
interface SessionContextType {
  isAuthenticated: boolean;
  accessToken: string | null;
  user: any | null;
  loading: boolean;
  error: string | null;
  setAccessToken: (token: string, refreshToken?: string, user?: any) => void;
  clearSession: () => void;
}

// Create the context with default values
const SessionContext = createContext<SessionContextType>({
  isAuthenticated: false,
  accessToken: null,
  user: null,
  loading: true,
  error: null,
  setAccessToken: () => {},
  clearSession: () => {},
});

// Hook to use the session context
export const useSessionContext = () => useContext(SessionContext);

// Provider component
export const SessionProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const { data: session, status } = useSession();
  const [accessToken, setAccessTokenState] = useState<string | null>(null);
  const [user, setUser] = useState<any | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Initialize session state from NextAuth and/or stored tokens
  useEffect(() => {
    const initializeSession = async () => {
      try {
        // First check if we have a NextAuth session
        if (session) {
          const token = getAccessToken(session);
          if (token) {
            setAccessTokenState(token);
            setUser(session.user);
            storeSessionToken(token);
            setLoading(false);
            return;
          }
        }

        // If no NextAuth session, check for stored token
        const storedToken = getStoredSessionToken();
        if (storedToken) {
          setAccessTokenState(storedToken);
          
          // Get stored user if available
          const storedUser = getStoredUser();
          if (storedUser) {
            setUser(storedUser);
          }
        }
      } catch (err: any) {
        console.error('Error initializing session:', err);
        setError(err.message || 'Failed to initialize session');
      } finally {
        setLoading(false);
      }
    };

    initializeSession();
  }, [session, status]);

  // Function to set a new access token
  const setAccessToken = (token: string, refreshToken?: string, userData?: any) => {
    setAccessTokenState(token);
    storeSessionToken(token, refreshToken, userData);
    
    if (userData) {
      setUser(userData);
    }
  };

  // Function to clear the session
  const clearSession = () => {
    setAccessTokenState(null);
    setUser(null);
    clearSessionData();
  };

  // Compute authentication status
  const isAuthenticated = !!accessToken;

  // Context value
  const value: SessionContextType = {
    isAuthenticated,
    accessToken,
    user,
    loading,
    error,
    setAccessToken,
    clearSession,
  };

  return (
    <SessionContext.Provider value={value}>
      {children}
    </SessionContext.Provider>
  );
};

export default SessionProvider;
