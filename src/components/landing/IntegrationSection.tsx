export const IntegrationSection = () => {
  return (
    <section className="flex flex-col gap-8 md:gap-12 items-center">
      <div className="text-center max-w-3xl">
        <h2 className="text-white text-3xl md:text-4xl font-bold leading-tight tracking-tight mb-3">Seamless Integration</h2>
        <p className="text-muted-foreground text-base md:text-lg leading-relaxed text-center">
          Integrate our API management tools into your existing workflows with ease using our straightforward code snippets.
        </p>
      </div>
      <div className="w-full max-w-2xl mx-auto">
        <div className="bg-[#161a1e] rounded-xl border border-[#283039] p-6 shadow-2xl">
          <div className="flex justify-between items-center mb-4">
            <span className="text-sm text-gray-400">Example: JavaScript (Node.js)</span>
            <div className="flex gap-2">
              <button className="text-gray-400 hover:text-white transition-colors">
                <svg fill="currentColor" height="20" viewBox="0 0 256 256" width="20" xmlns="http://www.w3.org/2000/svg">
                  <path d="M216,32H88a8,8,0,0,0-8,8V72H40a8,8,0,0,0-8,8V216a8,8,0,0,0,8,8H168a8,8,0,0,0,8-8V184h40a8,8,0,0,0,8-8V40A8,8,0,0,0,216,32ZM160,208H48V88H160Zm48-48H176V88a8,8,0,0,0-8-8H96V48H208Z"></path>
                </svg>
              </button>
            </div>
          </div>
          <pre className="bg-[#0e1114] p-4 rounded-md overflow-x-auto text-sm">
            <code className="language-javascript text-gray-300">
{`// Initialize API Client
const apiManager = require('your-api-manager-client');
const client = new apiManager.Client({ apiKey: 'YOUR_API_KEY' });

// Example: Get API Key Details
async function getKeyDetails(keyId) {
  try {
    const keyInfo = await client.keys.get(keyId);
    console.log('API Key Details:', keyInfo);
  } catch (error) {
    console.error('Error fetching key details:', error);
  }
}

getKeyDetails('sample-key-id');`}
            </code>
          </pre>
        </div>
      </div>
    </section>
  );
};
