package handlers

import (
	"fmt"
	"net/http"
	"os"
	"strconv"
	"strings"
	"time"

	"github.com/adc-credit/backend/internal/database"
	"github.com/adc-credit/backend/internal/models"
	"github.com/gin-gonic/gin"
	"github.com/golang-jwt/jwt/v4"
	"github.com/google/uuid"
	"github.com/stripe/stripe-go/v72"
	"github.com/stripe/stripe-go/v72/checkout/session"
)

// validateToken validates a JWT token and returns the user
func validateToken(tokenString string) (models.User, error) {
	// Parse the token
	token, err := jwt.Parse(tokenString, func(token *jwt.Token) (interface{}, error) {
		// Validate the signing method
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, fmt.Errorf("unexpected signing method: %v", token.Header["alg"])
		}

		// Return the secret key
		return []byte(os.Getenv("JWT_SECRET")), nil
	})

	if err != nil {
		return models.User{}, err
	}

	// Check if the token is valid
	if !token.Valid {
		return models.User{}, fmt.Errorf("invalid token")
	}

	// Get the claims
	claims, ok := token.Claims.(jwt.MapClaims)
	if !ok {
		return models.User{}, fmt.Errorf("invalid token claims")
	}

	// Check if the token is expired
	if exp, ok := claims["exp"].(float64); ok {
		if time.Now().Unix() > int64(exp) {
			return models.User{}, fmt.Errorf("token expired")
		}
	}

	// Get the user ID from the claims
	sub, ok := claims["sub"].(string)
	if !ok {
		return models.User{}, fmt.Errorf("invalid user ID in token")
	}

	// Parse the user ID
	userID, err := uuid.Parse(sub)
	if err != nil {
		return models.User{}, fmt.Errorf("invalid user ID format: %v", err)
	}

	// Get the user from the database
	var user models.User
	if err := database.DB.Where("id = ?", userID).First(&user).Error; err != nil {
		return models.User{}, fmt.Errorf("user not found: %v", err)
	}

	return user, nil
}

// Initialize Stripe
func init() {
	// Set the Stripe API key
	stripeKey := os.Getenv("STRIPE_SECRET_KEY")
	if stripeKey == "" {
		// Use a default test key if not set in environment
		stripeKey = "sk_test_51QtW5QCtNXkGk5bXQrIg3IdxRXAsB6a5XBKbpxQiTlgdU1GN3Pq5deYt0gtq6mX2GKOnjNMaQaq8nWHvhMTNv5sh00PnRaVKrg"
		fmt.Println("WARNING: Using default Stripe test key. Set STRIPE_SECRET_KEY environment variable for production.")
	} else {
		fmt.Println("Using Stripe API key from environment variables")
	}
	stripe.Key = stripeKey
}

// Stripe price IDs for each tier and subscription type
var STRIPE_PRICE_IDS = map[string]string{
	// Personal subscription price IDs
	"1_personal": "price_1RQR2ZCtNXkGk5bXcBExxwz6", // Standard Monthly - $19.99
	"2_personal": "price_1RQR2aCtNXkGk5bXmu3lnwHO", // Premium Monthly - $49.99
	"3_personal": "price_1RQR2bCtNXkGk5bXXRqRCtjm", // Enterprise Monthly - $99.99

	// Merchant subscription price IDs
	"1_merchant": "price_1RQR2ZCtNXkGk5bXcBExxwz6", // Standard Monthly - $19.99
	"2_merchant": "price_1RQR2aCtNXkGk5bXmu3lnwHO", // Premium Monthly - $49.99
	"3_merchant": "price_1RQR2bCtNXkGk5bXXRqRCtjm", // Enterprise Monthly - $99.99

	// Customer subscription price IDs
	"1_customer": "price_1RQR2ZCtNXkGk5bXcBExxwz6", // Standard Monthly - $19.99
	"2_customer": "price_1RQR2aCtNXkGk5bXmu3lnwHO", // Premium Monthly - $49.99
}

// CreateCheckoutSession creates a Stripe checkout session for subscription
func CreateCheckoutSession(c *gin.Context) {
	// Get the authorization header
	authHeader := c.GetHeader("Authorization")

	// Check if the authorization header is present
	var user models.User
	var err error

	if authHeader != "" && strings.HasPrefix(authHeader, "Bearer ") {
		// Extract the token
		token := strings.TrimPrefix(authHeader, "Bearer ")

		// Validate the token and get the user
		user, err = validateToken(token)
		if err != nil {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid token"})
			return
		}
	} else {
		// Try to get the user from the context (for internal API calls)
		userInterface, exists := c.Get("user")
		if !exists {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "Authentication required"})
			return
		}
		user = userInterface.(models.User)
	}

	type CreateCheckoutSessionRequest struct {
		TierID           int    `json:"tier_id" binding:"required"`
		SubscriptionType string `json:"subscription_type"`
		MerchantShopID   string `json:"merchant_shop_id"`
		ShopCustomerID   string `json:"shop_customer_id"`
	}

	var req CreateCheckoutSessionRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Validate subscription type
	subscriptionType := "personal"
	if req.SubscriptionType != "" {
		if req.SubscriptionType != "personal" && req.SubscriptionType != "merchant" && req.SubscriptionType != "customer" {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid subscription type. Must be 'personal', 'merchant', or 'customer'"})
			return
		}
		subscriptionType = req.SubscriptionType
	}

	// Validate merchant shop ID if subscription type is merchant
	if subscriptionType == "merchant" && req.MerchantShopID != "" {
		shopUUID, err := uuid.Parse(req.MerchantShopID)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid merchant shop ID"})
			return
		}

		// Check if shop exists and belongs to the user
		var shop models.MerchantShop
		if err := database.DB.Where("id = ? AND owner_user_id = ?", shopUUID, user.ID).First(&shop).Error; err != nil {
			c.JSON(http.StatusNotFound, gin.H{"error": "Merchant shop not found or not owned by user"})
			return
		}
	}

	// Validate shop customer ID if subscription type is customer
	if subscriptionType == "customer" && req.ShopCustomerID != "" {
		customerUUID, err := uuid.Parse(req.ShopCustomerID)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid shop customer ID"})
			return
		}

		// Check if customer exists and belongs to the user
		var customer models.ShopCustomer
		if err := database.DB.Where("id = ? AND user_id = ?", customerUUID, user.ID).First(&customer).Error; err != nil {
			c.JSON(http.StatusNotFound, gin.H{"error": "Shop customer not found or not associated with user"})
			return
		}
	}

	// Get the subscription tier
	var tier models.SubscriptionTier
	if err := database.DB.First(&tier, req.TierID).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Subscription tier not found"})
		return
	}

	// Skip Stripe checkout for free tiers
	if tier.Price == 0 {
		c.JSON(http.StatusOK, gin.H{
			"free_tier": true,
			"message":   "Free tier selected, no payment required",
		})
		return
	}

	// Get the Stripe price ID
	priceKey := fmt.Sprintf("%d_%s", tier.ID, subscriptionType)
	priceID, ok := STRIPE_PRICE_IDS[priceKey]
	if !ok || priceID == "" {
		// If no specific price ID is found, try to use a default one
		fmt.Printf("No price ID found for key: %s, using default price ID\n", priceKey)

		// Use a default price ID for testing
		priceID = "price_1RQR2ZCtNXkGk5bXcBExxwz6"
	}

	fmt.Printf("Using Stripe price ID: %s for tier %d and subscription type %s\n", priceID, tier.ID, subscriptionType)

	// Get the success and cancel URLs from the frontend
	origin := c.GetHeader("Origin")
	if origin == "" {
		origin = "https://localhost:3800"
	}

	successURL := fmt.Sprintf("%s/dashboard/subscriptions?success=true&session_id={CHECKOUT_SESSION_ID}&tier_id=%d&subscription_type=%s",
		origin,
		tier.ID,
		subscriptionType,
	)

	if req.MerchantShopID != "" {
		successURL += "&merchant_shop_id=" + req.MerchantShopID
	}

	if req.ShopCustomerID != "" {
		successURL += "&shop_customer_id=" + req.ShopCustomerID
	}

	cancelURL := fmt.Sprintf("%s/dashboard/subscriptions?canceled=true", origin)

	// Create the checkout session
	params := &stripe.CheckoutSessionParams{
		PaymentMethodTypes: stripe.StringSlice([]string{
			"card",
		}),
		LineItems: []*stripe.CheckoutSessionLineItemParams{
			{
				Price:    stripe.String(priceID),
				Quantity: stripe.Int64(1),
			},
		},
		Mode:          stripe.String(string(stripe.CheckoutSessionModeSubscription)),
		SuccessURL:    stripe.String(successURL),
		CancelURL:     stripe.String(cancelURL),
		CustomerEmail: stripe.String(user.Email),
	}

	// Add metadata
	params.AddMetadata("user_id", user.ID.String())
	params.AddMetadata("tier_id", strconv.Itoa(int(tier.ID)))
	params.AddMetadata("subscription_type", subscriptionType)

	if req.MerchantShopID != "" {
		params.AddMetadata("merchant_shop_id", req.MerchantShopID)
	}

	if req.ShopCustomerID != "" {
		params.AddMetadata("shop_customer_id", req.ShopCustomerID)
	}

	s, err := session.New(params)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"checkout_url": s.URL,
	})
}
