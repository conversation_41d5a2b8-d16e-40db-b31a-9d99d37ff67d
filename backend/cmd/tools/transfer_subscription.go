package main

import (
	"fmt"
	"log"

	"github.com/adc-credit/backend/internal/database"
	"github.com/adc-credit/backend/internal/models"
	"github.com/joho/godotenv"
)

func main() {
	// Load environment variables
	if err := godotenv.Load(); err != nil {
		log.Println("No .env file found, using environment variables")
	}

	// Initialize database connection
	if err := database.InitDB(); err != nil {
		log.Fatalf("Failed to connect to database: %v", err)
	}

	// Source and destination email addresses
	fromEmail := "<EMAIL>"
	toEmail := "<EMAIL>"

	// Transfer the subscription
	if err := transferSubscription(fromEmail, toEmail); err != nil {
		log.Fatalf("Failed to transfer subscription: %v", err)
	}

	log.Println("Subscription transferred successfully!")
}

// Transfer the subscription from one user to another
func transferSubscription(fromUserEmail, toUserEmail string) error {
	var fromUser, toUser models.User

	// Find the source user
	if err := database.DB.Where("email = ?", fromUserEmail).First(&fromUser).Error; err != nil {
		return fmt.Errorf("source user not found: %v", err)
	}

	fmt.Printf("Source user: %s (ID: %s)\n", fromUser.Email, fromUser.ID)

	// Find the destination user
	if err := database.DB.Where("email = ?", toUserEmail).First(&toUser).Error; err != nil {
		return fmt.Errorf("destination user not found: %v", err)
	}

	fmt.Printf("Destination user: %s (ID: %s)\n", toUser.Email, toUser.ID)

	// Find the subscription
	var subscription models.Subscription
	if err := database.DB.Where("user_id = ?", fromUser.ID).
		Preload("SubscriptionTier").
		First(&subscription).Error; err != nil {
		return fmt.Errorf("subscription not found: %v", err)
	}

	fmt.Printf("Found subscription: %s (Tier: %s)\n", subscription.ID, subscription.SubscriptionTier.Name)

	// Update the user ID
	oldUserID := subscription.UserID
	subscription.UserID = toUser.ID

	// Save the changes
	if err := database.DB.Save(&subscription).Error; err != nil {
		return fmt.Errorf("failed to update subscription: %v", err)
	}

	fmt.Printf("Updated subscription user ID from %s to %s\n", oldUserID, toUser.ID)

	// Also update any related transactions
	var count int64
	if err := database.DB.Model(&models.Transaction{}).
		Where("user_id = ? AND subscription_id = ?", oldUserID, subscription.ID).
		Count(&count).Error; err != nil {
		return fmt.Errorf("failed to count transactions: %v", err)
	}

	fmt.Printf("Found %d transactions to update\n", count)

	if count > 0 {
		if err := database.DB.Model(&models.Transaction{}).
			Where("user_id = ? AND subscription_id = ?", oldUserID, subscription.ID).
			Update("user_id", toUser.ID).Error; err != nil {
			return fmt.Errorf("failed to update transactions: %v", err)
		}
		fmt.Printf("Updated %d transactions\n", count)
	}

	return nil
}
