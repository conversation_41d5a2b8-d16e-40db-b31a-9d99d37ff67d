package handlers

import (
	"net/http"

	"github.com/adc-credit/backend/internal/database"
	"github.com/adc-credit/backend/internal/models"
	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

// GetCustomerShops returns all shops where the current user is a customer
func GetCustomerShops(c *gin.Context) {
	user := c.MustGet("user").(models.User)

	var customerRecords []models.ShopCustomer
	if err := database.DB.Where("user_id = ?", user.ID).Find(&customerRecords).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch customer records"})
		return
	}

	// If no shops found, return empty array
	if len(customerRecords) == 0 {
		c.JSON(http.StatusOK, []models.MerchantShop{})
		return
	}

	// Extract shop IDs
	var shopIDs []uuid.UUID
	for _, record := range customerRecords {
		shopIDs = append(shopIDs, record.ShopID)
	}

	// Fetch shop details
	var shops []models.MerchantShop
	if err := database.DB.Where("id IN ?", shopIDs).Find(&shops).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch shop details"})
		return
	}

	// Attach credit balance to each shop
	type ShopWithCredit struct {
		models.MerchantShop
		CreditBalance int `json:"credit_balance"`
	}

	var shopsWithCredit []ShopWithCredit
	for _, shop := range shops {
		// Find the customer record for this shop
		var creditBalance int
		for _, record := range customerRecords {
			if record.ShopID == shop.ID {
				creditBalance = record.CreditBalance
				break
			}
		}

		shopsWithCredit = append(shopsWithCredit, ShopWithCredit{
			MerchantShop:  shop,
			CreditBalance: creditBalance,
		})
	}

	c.JSON(http.StatusOK, shopsWithCredit)
}

// GetCustomerShop returns details of a specific shop for a customer
func GetCustomerShop(c *gin.Context) {
	user := c.MustGet("user").(models.User)
	shopID := c.Param("id")

	// Check if user is a customer of this shop
	var customer models.ShopCustomer
	if err := database.DB.Where("shop_id = ? AND user_id = ?", shopID, user.ID).First(&customer).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Shop not found or you are not a customer"})
		return
	}

	// Get shop details
	var shop models.MerchantShop
	if err := database.DB.First(&shop, "id = ?", shopID).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Shop not found"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"shop":           shop,
		"credit_balance": customer.CreditBalance,
	})
}

// GetCustomerTransactions returns all credit transactions for a customer in a specific shop
func GetCustomerTransactions(c *gin.Context) {
	user := c.MustGet("user").(models.User)
	shopID := c.Param("id")

	// Check if user is a customer of this shop
	var customer models.ShopCustomer
	if err := database.DB.Where("shop_id = ? AND user_id = ?", shopID, user.ID).First(&customer).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Shop not found or you are not a customer"})
		return
	}

	// Get transactions
	var transactions []models.ShopCreditTransaction
	if err := database.DB.Where("shop_id = ? AND customer_id = ?", shopID, customer.ID).
		Order("created_at DESC").
		Find(&transactions).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch transactions"})
		return
	}

	c.JSON(http.StatusOK, transactions)
}

// UseShopCredit allows a customer to use credits from their balance
func UseShopCredit(c *gin.Context) {
	user := c.MustGet("user").(models.User)
	shopID := c.Param("id")

	// Check if user is a customer of this shop
	var customer models.ShopCustomer
	if err := database.DB.Where("shop_id = ? AND user_id = ?", shopID, user.ID).First(&customer).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Shop not found or you are not a customer"})
		return
	}

	type UseCreditRequest struct {
		Amount      int    `json:"amount" binding:"required,min=1"`
		Description string `json:"description"`
	}

	var req UseCreditRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Check if customer has enough credits
	if customer.CreditBalance < req.Amount {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Insufficient credit balance"})
		return
	}

	// Start a transaction
	tx := database.DB.Begin()

	// Update credit balance
	customer.CreditBalance -= req.Amount
	if err := tx.Save(&customer).Error; err != nil {
		tx.Rollback()
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update credit balance"})
		return
	}

	// Create transaction record
	transaction := models.ShopCreditTransaction{
		ID:          uuid.New(),
		ShopID:      uuid.MustParse(shopID),
		CustomerID:  customer.ID,
		Type:        "credit_use",
		Amount:      -req.Amount, // Negative amount for usage
		Description: req.Description,
	}

	if err := tx.Create(&transaction).Error; err != nil {
		tx.Rollback()
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create transaction record"})
		return
	}

	// Commit transaction
	if err := tx.Commit().Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to commit transaction"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message":        "Credits used successfully",
		"credit_balance": customer.CreditBalance,
		"transaction":    transaction,
	})
}
