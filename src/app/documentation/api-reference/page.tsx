"use client";

import { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger } from "@/components/ui/tabs";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Navbar } from "@/components/layout/navbar";
import { Footer } from "@/components/layout/footer";
import Link from "next/link";
import { Copy, Check, ChevronRight, ChevronDown } from "lucide-react";
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion";

// Code block component
const CodeBlock = ({ language, code }: { language: string; code: string }) => {
  const [copied, setCopied] = useState(false);

  const copyToClipboard = () => {
    navigator.clipboard.writeText(code);
    setCopied(true);
    setTimeout(() => setCopied(false), 2000);
  };

  return (
    <div className="relative my-4">
      <div className="absolute right-2 top-2">
        <Button
          variant="ghost"
          size="icon"
          onClick={copyToClipboard}
          className="h-8 w-8 rounded-md"
        >
          {copied ? (
            <Check className="h-4 w-4 text-green-500" />
          ) : (
            <Copy className="h-4 w-4" />
          )}
          <span className="sr-only">Copy code</span>
        </Button>
      </div>
      <div className="flex items-center gap-2 bg-muted px-4 py-2 rounded-t-md border border-border">
        <div className="text-xs font-medium">{language}</div>
      </div>
      <pre className="overflow-x-auto rounded-b-md bg-muted p-4 text-sm border border-t-0 border-border">
        <code className="font-mono">{code}</code>
      </pre>
    </div>
  );
};

// API Endpoint component
const ApiEndpoint = ({
  method,
  endpoint,
  description,
  requestParams,
  responseExample,
}: {
  method: "GET" | "POST" | "PUT" | "DELETE";
  endpoint: string;
  description: string;
  requestParams?: { name: string; type: string; required: boolean; description: string }[];
  responseExample: string;
}) => {
  const methodColors = {
    GET: "bg-blue-100 text-blue-800",
    POST: "bg-green-100 text-green-800",
    PUT: "bg-yellow-100 text-yellow-800",
    DELETE: "bg-red-100 text-red-800",
  };

  return (
    <div className="border rounded-md mb-6">
      <div className="flex items-center p-4 border-b">
        <span
          className={`inline-flex items-center rounded-md px-2.5 py-0.5 text-xs font-medium ${methodColors[method]}`}
        >
          {method}
        </span>
        <span className="ml-2 font-mono text-sm">{endpoint}</span>
      </div>
      <div className="p-4">
        <p className="text-sm mb-4">{description}</p>
        
        {requestParams && requestParams.length > 0 && (
          <div className="mb-4">
            <h4 className="text-sm font-semibold mb-2">Request Parameters</h4>
            <div className="overflow-x-auto">
              <table className="w-full text-sm">
                <thead>
                  <tr className="border-b">
                    <th className="text-left py-2 px-4">Parameter</th>
                    <th className="text-left py-2 px-4">Type</th>
                    <th className="text-left py-2 px-4">Required</th>
                    <th className="text-left py-2 px-4">Description</th>
                  </tr>
                </thead>
                <tbody>
                  {requestParams.map((param, index) => (
                    <tr key={index} className="border-b">
                      <td className="py-2 px-4 font-mono">{param.name}</td>
                      <td className="py-2 px-4">{param.type}</td>
                      <td className="py-2 px-4">{param.required ? "Yes" : "No"}</td>
                      <td className="py-2 px-4">{param.description}</td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        )}
        
        <h4 className="text-sm font-semibold mb-2">Response Example</h4>
        <CodeBlock language="json" code={responseExample} />
      </div>
    </div>
  );
};

export default function ApiReferencePage() {
  return (
    <div className="flex min-h-screen flex-col">
      <Navbar />
      <main className="flex-1 container py-10">
        <div className="flex items-center mb-8">
          <Link href="/documentation" className="text-sm text-muted-foreground hover:text-foreground">
            Documentation
          </Link>
          <ChevronRight className="h-4 w-4 mx-2 text-muted-foreground" />
          <span className="text-sm font-medium">API Reference</span>
        </div>

        <h1 className="text-3xl font-bold mb-4">API Reference</h1>
        <p className="text-lg text-muted-foreground mb-8">
          Complete reference documentation for the ADC Credit API
        </p>

        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          <div className="md:col-span-1">
            <div className="sticky top-20">
              <h3 className="text-lg font-semibold mb-4">Endpoints</h3>
              <nav className="space-y-1">
                <Link
                  href="#authentication"
                  className="block px-3 py-2 text-sm rounded-md hover:bg-muted"
                >
                  Authentication
                </Link>
                <Link
                  href="#api-keys"
                  className="block px-3 py-2 text-sm rounded-md hover:bg-muted"
                >
                  API Keys
                </Link>
                <Link
                  href="#credits"
                  className="block px-3 py-2 text-sm rounded-md hover:bg-muted"
                >
                  Credits
                </Link>
                <Link
                  href="#usage"
                  className="block px-3 py-2 text-sm rounded-md hover:bg-muted"
                >
                  Usage
                </Link>
                <Link
                  href="#webhooks"
                  className="block px-3 py-2 text-sm rounded-md hover:bg-muted"
                >
                  Webhooks
                </Link>
              </nav>
            </div>
          </div>

          <div className="md:col-span-3">
            <section id="authentication" className="mb-12">
              <h2 className="text-2xl font-bold mb-4">Authentication</h2>
              <p className="mb-4">
                The ADC Credit API uses API keys for authentication. Include your API key in the Authorization header of your requests.
              </p>

              <ApiEndpoint
                method="POST"
                endpoint="/auth/login"
                description="Authenticate with email and password to get a JWT token."
                requestParams={[
                  { name: "email", type: "string", required: true, description: "User email address" },
                  { name: "password", type: "string", required: true, description: "User password" },
                ]}
                responseExample={`{
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "user": {
    "id": "user_123",
    "email": "<EMAIL>",
    "name": "John Doe",
    "role": "user"
  }
}`}
              />
            </section>

            <section id="api-keys" className="mb-12">
              <h2 className="text-2xl font-bold mb-4">API Keys</h2>
              <p className="mb-4">
                Manage API keys for authenticating with the ADC Credit API.
              </p>

              <ApiEndpoint
                method="GET"
                endpoint="/apikeys"
                description="Get all API keys for the current user."
                responseExample={`[
  {
    "id": "key_123",
    "user_id": "user_123",
    "name": "Production API Key",
    "key": "sk_prod_123...",
    "last_used": "2023-10-15T10:30:00Z",
    "enabled": true,
    "permissions": ["read", "write"],
    "rate_limit_max": 60,
    "rate_limit_rate": 1.0,
    "created_at": "2023-09-01T12:00:00Z",
    "updated_at": "2023-09-01T12:00:00Z"
  }
]`}
              />

              <ApiEndpoint
                method="POST"
                endpoint="/apikeys"
                description="Create a new API key."
                requestParams={[
                  { name: "name", type: "string", required: true, description: "Name of the API key" },
                  { name: "permissions", type: "string[]", required: false, description: "Array of permissions (read, write, delete, admin)" },
                ]}
                responseExample={`{
  "id": "key_123",
  "user_id": "user_123",
  "name": "Production API Key",
  "key": "sk_prod_123...",
  "enabled": true,
  "permissions": ["read", "write"],
  "rate_limit_max": 60,
  "rate_limit_rate": 1.0,
  "created_at": "2023-09-01T12:00:00Z",
  "updated_at": "2023-09-01T12:00:00Z"
}`}
              />
            </section>

            <section id="credits" className="mb-12">
              <h2 className="text-2xl font-bold mb-4">Credits</h2>
              <p className="mb-4">
                Manage and track credit usage for API requests.
              </p>

              <ApiEndpoint
                method="GET"
                endpoint="/credits"
                description="Get the current credit balance and subscription information."
                responseExample={`{
  "credit_balance": 8500,
  "credit_limit": 10000,
  "subscription": {
    "id": "sub_123",
    "subscription_tier_id": 2,
    "subscription_tier": {
      "name": "Pro",
      "credit_limit": 10000,
      "price": 29.99
    },
    "start_date": "2023-09-01T00:00:00Z",
    "end_date": null,
    "auto_renew": true,
    "status": "active"
  }
}`}
              />

              <ApiEndpoint
                method="POST"
                endpoint="/credits/add"
                description="Add credits to the current user's account."
                requestParams={[
                  { name: "amount", type: "number", required: true, description: "Number of credits to add" },
                  { name: "description", type: "string", required: false, description: "Description of the transaction" },
                ]}
                responseExample={`{
  "message": "Credits added successfully",
  "credit_balance": 9500,
  "transaction": {
    "id": "txn_123",
    "user_id": "user_123",
    "subscription_id": "sub_123",
    "type": "credit_add",
    "amount": 1000,
    "description": "Manual credit addition",
    "created_at": "2023-10-15T10:30:00Z"
  }
}`}
              />
            </section>

            <section id="usage" className="mb-12">
              <h2 className="text-2xl font-bold mb-4">Usage</h2>
              <p className="mb-4">
                Track and analyze API usage.
              </p>

              <ApiEndpoint
                method="GET"
                endpoint="/usage"
                description="Get detailed usage records."
                requestParams={[
                  { name: "start_date", type: "string", required: false, description: "Start date for filtering (ISO format)" },
                  { name: "end_date", type: "string", required: false, description: "End date for filtering (ISO format)" },
                  { name: "api_key_id", type: "string", required: false, description: "Filter by API key ID" },
                ]}
                responseExample={`[
  {
    "id": "usage_123",
    "api_key_id": "key_123",
    "endpoint": "/api/data",
    "method": "GET",
    "credits": 1,
    "timestamp": "2023-10-15T10:30:00Z",
    "success": true,
    "ip_address": "***********",
    "user_agent": "Mozilla/5.0...",
    "response_time": 120,
    "status_code": 200
  }
]`}
              />

              <ApiEndpoint
                method="GET"
                endpoint="/usage/summary"
                description="Get a summary of API usage."
                requestParams={[
                  { name: "period", type: "string", required: false, description: "Time period (day, week, month, year)" },
                  { name: "start_date", type: "string", required: false, description: "Start date for filtering (ISO format)" },
                  { name: "end_date", type: "string", required: false, description: "End date for filtering (ISO format)" },
                ]}
                responseExample={`{
  "total_usage": 1250,
  "total_credits": 1250,
  "api_keys": [
    {
      "id": "key_123",
      "name": "Production API Key",
      "total_usage": 1250,
      "total_credits": 1250,
      "endpoints": [
        {
          "endpoint": "/api/data",
          "total_usage": 1000,
          "total_credits": 1000
        },
        {
          "endpoint": "/api/users",
          "total_usage": 250,
          "total_credits": 250
        }
      ]
    }
  ],
  "time_series": [
    {
      "period": "2023-10-01",
      "total_usage": 450,
      "total_credits": 450
    },
    {
      "period": "2023-10-02",
      "total_usage": 800,
      "total_credits": 800
    }
  ]
}`}
              />
            </section>

            <section id="webhooks" className="mb-12">
              <h2 className="text-2xl font-bold mb-4">Webhooks</h2>
              <p className="mb-4">
                Manage webhooks for real-time notifications.
              </p>

              <ApiEndpoint
                method="GET"
                endpoint="/webhooks"
                description="Get all webhooks for the current user."
                responseExample={`[
  {
    "id": "webhook_123",
    "user_id": "user_123",
    "name": "Credit Consumption Webhook",
    "url": "https://example.com/webhooks/adc-credit",
    "secret": "whsec_123...",
    "events": ["credit.consumed", "credit.added"],
    "active": true,
    "last_called": "2023-10-15T10:30:00Z",
    "fail_count": 0,
    "created_at": "2023-09-01T12:00:00Z",
    "updated_at": "2023-09-01T12:00:00Z"
  }
]`}
              />

              <ApiEndpoint
                method="POST"
                endpoint="/webhooks"
                description="Create a new webhook."
                requestParams={[
                  { name: "name", type: "string", required: true, description: "Name of the webhook" },
                  { name: "url", type: "string", required: true, description: "URL to send webhook events to" },
                  { name: "secret", type: "string", required: false, description: "Secret for signing webhook payloads" },
                  { name: "events", type: "string[]", required: true, description: "Array of events to subscribe to" },
                ]}
                responseExample={`{
  "id": "webhook_123",
  "user_id": "user_123",
  "name": "Credit Consumption Webhook",
  "url": "https://example.com/webhooks/adc-credit",
  "secret": "whsec_123...",
  "events": ["credit.consumed", "credit.added"],
  "active": true,
  "created_at": "2023-09-01T12:00:00Z",
  "updated_at": "2023-09-01T12:00:00Z"
}`}
              />
            </section>
          </div>
        </div>
      </main>
      <Footer />
    </div>
  );
}
