package handlers

import (
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"os"
	"strconv"
	"time"

	"github.com/adc-credit/backend/internal/database"
	"github.com/adc-credit/backend/internal/models"
	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/stripe/stripe-go/v72"
	"github.com/stripe/stripe-go/v72/webhook"
)

// Initialize Stripe
func init() {
	// Set the Stripe API key
	stripeKey := os.Getenv("STRIPE_SECRET_KEY")
	if stripeKey == "" {
		// Use a default test key if not set in environment
		stripeKey = "sk_test_51QtW5QCtNXkGk5bXQrIg3IdxRXAsB6a5XBKbpxQiTlgdU1GN3Pq5deYt0gtq6mX2GKOnjNMaQaq8nWHvhMTNv5sh00PnRaVKrg"
		fmt.Println("WARNING: Using default Stripe test key. Set STRIPE_SECRET_KEY environment variable for production.")
	} else {
		fmt.Println("Using Stripe API key from environment variables")
	}
	stripe.Key = stripeKey
}

// HandleStripeWebhook processes Stripe webhook events
func HandleStripeWebhook(c *gin.Context) {
	// Read the request body
	body, err := io.ReadAll(c.Request.Body)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Failed to read request body"})
		return
	}

	// Get the webhook secret from environment variables
	endpointSecret := os.Getenv("STRIPE_WEBHOOK_SECRET")
	if endpointSecret == "" {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Stripe webhook secret not configured"})
		return
	}

	// Verify the webhook signature
	event, err := webhook.ConstructEvent(body, c.GetHeader("Stripe-Signature"), endpointSecret)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": fmt.Sprintf("Webhook signature verification failed: %v", err)})
		return
	}

	// Handle the event based on its type
	switch event.Type {
	case "checkout.session.completed":
		// Parse the checkout session
		var session stripe.CheckoutSession
		err := json.Unmarshal(event.Data.Raw, &session)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Failed to parse checkout session"})
			return
		}

		// Process the completed checkout session
		err = handleCheckoutSessionCompleted(session)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": fmt.Sprintf("Failed to process checkout session: %v", err)})
			return
		}

	case "customer.subscription.created":
		// Parse the subscription
		var subscription stripe.Subscription
		err := json.Unmarshal(event.Data.Raw, &subscription)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Failed to parse subscription"})
			return
		}

		// Process the created subscription
		err = handleSubscriptionCreated(subscription)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": fmt.Sprintf("Failed to process subscription: %v", err)})
			return
		}

	case "customer.subscription.updated":
		// Parse the subscription
		var subscription stripe.Subscription
		err := json.Unmarshal(event.Data.Raw, &subscription)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Failed to parse subscription"})
			return
		}

		// Process the updated subscription
		err = handleSubscriptionUpdated(subscription)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": fmt.Sprintf("Failed to process subscription update: %v", err)})
			return
		}

	case "customer.subscription.deleted":
		// Parse the subscription
		var subscription stripe.Subscription
		err := json.Unmarshal(event.Data.Raw, &subscription)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Failed to parse subscription"})
			return
		}

		// Process the deleted subscription
		err = handleSubscriptionDeleted(subscription)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": fmt.Sprintf("Failed to process subscription deletion: %v", err)})
			return
		}
	}

	c.JSON(http.StatusOK, gin.H{"received": true})
}

// handleCheckoutSessionCompleted processes a completed checkout session
func handleCheckoutSessionCompleted(session stripe.CheckoutSession) error {
	// Check if this is an upgrade/downgrade
	upgradeType := session.Metadata["upgrade_type"]

	if upgradeType == "tier_change" {
		return handleSubscriptionTierChange(session)
	}

	// Regular new subscription flow
	// Extract metadata from the session
	userIDStr := session.Metadata["user_id"]
	if userIDStr == "" {
		return fmt.Errorf("user_id not found in session metadata")
	}

	tierIDStr := session.Metadata["tier_id"]
	if tierIDStr == "" {
		return fmt.Errorf("tier_id not found in session metadata")
	}

	subscriptionType := session.Metadata["subscription_type"]
	if subscriptionType == "" {
		subscriptionType = "personal" // Default to personal if not specified
	}

	// Parse the user ID
	userID, err := uuid.Parse(userIDStr)
	if err != nil {
		return fmt.Errorf("invalid user ID: %v", err)
	}

	// Parse the tier ID
	tierID, err := strconv.Atoi(tierIDStr)
	if err != nil {
		return fmt.Errorf("invalid tier ID: %v", err)
	}

	// Get the user
	var user models.User
	if err := database.DB.Where("id = ?", userID).First(&user).Error; err != nil {
		return fmt.Errorf("user not found: %v", err)
	}

	// Get the subscription tier
	var tier models.SubscriptionTier
	if err := database.DB.First(&tier, tierID).Error; err != nil {
		return fmt.Errorf("subscription tier not found: %v", err)
	}

	// Start a transaction
	tx := database.DB.Begin()

	// Check if user already has active subscriptions of the same type
	var activeSubscriptions []models.Subscription
	query := database.DB.Where("user_id = ? AND status = ? AND subscription_type = ?", userID, "active", subscriptionType)

	// Add additional conditions for merchant and customer subscriptions
	merchantShopIDStr := session.Metadata["merchant_shop_id"]
	shopCustomerIDStr := session.Metadata["shop_customer_id"]

	if subscriptionType == "merchant" && merchantShopIDStr != "" {
		merchantShopID, err := uuid.Parse(merchantShopIDStr)
		if err != nil {
			tx.Rollback()
			return fmt.Errorf("invalid merchant shop ID: %v", err)
		}
		query = query.Where("merchant_shop_id = ?", merchantShopID)
	} else if subscriptionType == "customer" && shopCustomerIDStr != "" {
		shopCustomerID, err := uuid.Parse(shopCustomerIDStr)
		if err != nil {
			tx.Rollback()
			return fmt.Errorf("invalid shop customer ID: %v", err)
		}
		query = query.Where("shop_customer_id = ?", shopCustomerID)
	}

	if err := query.Find(&activeSubscriptions).Error; err != nil {
		tx.Rollback()
		return fmt.Errorf("failed to check existing subscriptions: %v", err)
	}

	// Cancel all active subscriptions of the same type
	if len(activeSubscriptions) > 0 {
		now := time.Now()
		for _, activeSubscription := range activeSubscriptions {
			activeSubscription.EndDate = &now
			activeSubscription.Status = "cancelled"

			if err := tx.Save(&activeSubscription).Error; err != nil {
				tx.Rollback()
				return fmt.Errorf("failed to update existing subscription: %v", err)
			}
		}
	}

	// Create a new subscription
	subscription := models.Subscription{
		ID:                uuid.New(),
		UserID:            userID,
		SubscriptionTierID: uint(tierID),
		StartDate:         time.Now(),
		AutoRenew:         true,
		Status:            "active",
		CreditBalance:     tier.CreditLimit,
		SubscriptionType:  subscriptionType,
		StripeSubscriptionID: session.Subscription.ID, // Store the Stripe subscription ID
	}

	// Add merchant shop ID if present
	if merchantShopIDStr != "" {
		merchantShopID, err := uuid.Parse(merchantShopIDStr)
		if err != nil {
			tx.Rollback()
			return fmt.Errorf("invalid merchant shop ID: %v", err)
		}
		subscription.MerchantShopID = &merchantShopID
	}

	// Add shop customer ID if present
	if shopCustomerIDStr != "" {
		shopCustomerID, err := uuid.Parse(shopCustomerIDStr)
		if err != nil {
			tx.Rollback()
			return fmt.Errorf("invalid shop customer ID: %v", err)
		}
		subscription.ShopCustomerID = &shopCustomerID
	}

	// Save the subscription to the database
	if err := tx.Create(&subscription).Error; err != nil {
		tx.Rollback()
		return fmt.Errorf("failed to create subscription: %v", err)
	}

	// Create transaction record for initial credits
	transaction := models.Transaction{
		ID:             uuid.New(),
		UserID:         userID,
		SubscriptionID: subscription.ID,
		Type:           "credit_add",
		Amount:         tier.CreditLimit,
		Description:    "Initial credits for " + tier.Name + " subscription",
	}

	if err := tx.Create(&transaction).Error; err != nil {
		tx.Rollback()
		return fmt.Errorf("failed to create transaction record: %v", err)
	}

	// Commit transaction
	if err := tx.Commit().Error; err != nil {
		return fmt.Errorf("failed to commit transaction: %v", err)
	}

	return nil
}

// handleSubscriptionTierChange processes a subscription tier change (upgrade/downgrade)
func handleSubscriptionTierChange(session stripe.CheckoutSession) error {
	// Extract metadata from the session
	userIDStr := session.Metadata["user_id"]
	if userIDStr == "" {
		return fmt.Errorf("user_id not found in session metadata")
	}

	subscriptionIDStr := session.Metadata["subscription_id"]
	if subscriptionIDStr == "" {
		return fmt.Errorf("subscription_id not found in session metadata")
	}

	oldTierIDStr := session.Metadata["old_tier_id"]
	if oldTierIDStr == "" {
		return fmt.Errorf("old_tier_id not found in session metadata")
	}

	newTierIDStr := session.Metadata["new_tier_id"]
	if newTierIDStr == "" {
		return fmt.Errorf("new_tier_id not found in session metadata")
	}

	// Parse IDs
	userID, err := uuid.Parse(userIDStr)
	if err != nil {
		return fmt.Errorf("invalid user ID: %v", err)
	}

	subscriptionID, err := uuid.Parse(subscriptionIDStr)
	if err != nil {
		return fmt.Errorf("invalid subscription ID: %v", err)
	}

	oldTierID, err := strconv.Atoi(oldTierIDStr)
	if err != nil {
		return fmt.Errorf("invalid old tier ID: %v", err)
	}

	newTierID, err := strconv.Atoi(newTierIDStr)
	if err != nil {
		return fmt.Errorf("invalid new tier ID: %v", err)
	}

	// Get the user
	var user models.User
	if err := database.DB.Where("id = ?", userID).First(&user).Error; err != nil {
		return fmt.Errorf("user not found: %v", err)
	}

	// Get the current subscription
	var subscription models.Subscription
	if err := database.DB.Where("id = ? AND user_id = ?", subscriptionID, userID).First(&subscription).Error; err != nil {
		return fmt.Errorf("subscription not found: %v", err)
	}

	// Get the old and new subscription tiers
	var oldTier models.SubscriptionTier
	if err := database.DB.First(&oldTier, oldTierID).Error; err != nil {
		return fmt.Errorf("old subscription tier not found: %v", err)
	}

	var newTier models.SubscriptionTier
	if err := database.DB.First(&newTier, newTierID).Error; err != nil {
		return fmt.Errorf("new subscription tier not found: %v", err)
	}

	// Start a transaction
	tx := database.DB.Begin()

	// Calculate prorated credits
	// Calculate the percentage of time remaining in the current subscription period
	var timeRemaining float64 = 1.0 // Default to 100% if no end date
	if subscription.EndDate != nil {
		totalPeriod := subscription.EndDate.Sub(subscription.StartDate)
		elapsed := time.Since(subscription.StartDate)
		timeRemaining = 1.0 - (elapsed.Seconds() / totalPeriod.Seconds())
		if timeRemaining < 0 {
			timeRemaining = 0
		}
	}

	// Calculate prorated credits based on remaining time and credit usage
	usedCredits := oldTier.CreditLimit - subscription.CreditBalance
	usedPercentage := float64(usedCredits) / float64(oldTier.CreditLimit)

	// Calculate new credit balance
	var newCreditBalance int
	if newTier.CreditLimit > oldTier.CreditLimit {
		// Upgrading: keep current balance plus prorated additional credits
		additionalCredits := newTier.CreditLimit - oldTier.CreditLimit
		newCreditBalance = subscription.CreditBalance + int(float64(additionalCredits)*timeRemaining)
	} else {
		// Downgrading: reduce credits proportionally
		newCreditBalance = int(float64(newTier.CreditLimit) * (1.0 - usedPercentage))
	}

	// Update the subscription
	now := time.Now()

	// Cancel the current subscription
	subscription.EndDate = &now
	subscription.Status = "cancelled"

	if err := tx.Save(&subscription).Error; err != nil {
		tx.Rollback()
		return fmt.Errorf("failed to update existing subscription: %v", err)
	}

	// Create a new subscription with the new tier
	newSubscription := models.Subscription{
		ID:                uuid.New(),
		UserID:            userID,
		SubscriptionTierID: uint(newTierID),
		StartDate:         now,
		AutoRenew:         subscription.AutoRenew,
		Status:            "active",
		CreditBalance:     newCreditBalance,
		SubscriptionType:  subscription.SubscriptionType,
		MerchantShopID:    subscription.MerchantShopID,
		ShopCustomerID:    subscription.ShopCustomerID,
		StripeSubscriptionID: session.Subscription.ID, // Store the new Stripe subscription ID
	}

	if err := tx.Create(&newSubscription).Error; err != nil {
		tx.Rollback()
		return fmt.Errorf("failed to create new subscription: %v", err)
	}

	// Create transaction record for the upgrade/downgrade
	var description string
	var amount int

	if newTier.CreditLimit > oldTier.CreditLimit {
		description = fmt.Sprintf("Upgrade from %s to %s tier", oldTier.Name, newTier.Name)
		amount = newCreditBalance - subscription.CreditBalance
	} else {
		description = fmt.Sprintf("Downgrade from %s to %s tier", oldTier.Name, newTier.Name)
		amount = newCreditBalance - subscription.CreditBalance
	}

	transaction := models.Transaction{
		ID:             uuid.New(),
		UserID:         userID,
		SubscriptionID: newSubscription.ID,
		Type:           "credit_add",
		Amount:         amount,
		Description:    description,
		Reference:      fmt.Sprintf("tier_change_%s_to_%s", oldTier.Name, newTier.Name),
	}

	if err := tx.Create(&transaction).Error; err != nil {
		tx.Rollback()
		return fmt.Errorf("failed to create transaction record: %v", err)
	}

	// Commit transaction
	if err := tx.Commit().Error; err != nil {
		return fmt.Errorf("failed to commit transaction: %v", err)
	}

	return nil
}

// handleSubscriptionCreated processes a created subscription
func handleSubscriptionCreated(subscription stripe.Subscription) error {
	// This is handled by the checkout.session.completed event
	return nil
}

// handleSubscriptionUpdated processes an updated subscription
func handleSubscriptionUpdated(subscription stripe.Subscription) error {
	// Extract the subscription ID from metadata
	subscriptionIDStr := subscription.Metadata["subscription_id"]
	if subscriptionIDStr == "" {
		return fmt.Errorf("subscription_id not found in subscription metadata")
	}

	// Parse the subscription ID
	subscriptionID, err := uuid.Parse(subscriptionIDStr)
	if err != nil {
		return fmt.Errorf("invalid subscription ID: %v", err)
	}

	// Get the subscription from the database
	var dbSubscription models.Subscription
	if err := database.DB.Where("id = ?", subscriptionID).First(&dbSubscription).Error; err != nil {
		return fmt.Errorf("subscription not found: %v", err)
	}

	// Update the subscription status based on the Stripe subscription status
	switch subscription.Status {
	case stripe.SubscriptionStatusActive:
		dbSubscription.Status = "active"
	case stripe.SubscriptionStatusPastDue:
		dbSubscription.Status = "past_due"
	case stripe.SubscriptionStatusUnpaid:
		dbSubscription.Status = "unpaid"
	case stripe.SubscriptionStatusCanceled:
		dbSubscription.Status = "cancelled"
		now := time.Now()
		dbSubscription.EndDate = &now
	case stripe.SubscriptionStatusIncomplete:
		dbSubscription.Status = "incomplete"
	case stripe.SubscriptionStatusIncompleteExpired:
		dbSubscription.Status = "expired"
	case stripe.SubscriptionStatusTrialing:
		dbSubscription.Status = "trialing"
	}

	// Save the updated subscription to the database
	if err := database.DB.Save(&dbSubscription).Error; err != nil {
		return fmt.Errorf("failed to update subscription: %v", err)
	}

	return nil
}

// handleSubscriptionDeleted processes a deleted subscription
func handleSubscriptionDeleted(subscription stripe.Subscription) error {
	// Extract the subscription ID from metadata
	subscriptionIDStr := subscription.Metadata["subscription_id"]
	if subscriptionIDStr == "" {
		return fmt.Errorf("subscription_id not found in subscription metadata")
	}

	// Parse the subscription ID
	subscriptionID, err := uuid.Parse(subscriptionIDStr)
	if err != nil {
		return fmt.Errorf("invalid subscription ID: %v", err)
	}

	// Get the subscription from the database
	var dbSubscription models.Subscription
	if err := database.DB.Where("id = ?", subscriptionID).First(&dbSubscription).Error; err != nil {
		return fmt.Errorf("subscription not found: %v", err)
	}

	// Update the subscription status to cancelled
	dbSubscription.Status = "cancelled"
	now := time.Now()
	dbSubscription.EndDate = &now
	dbSubscription.AutoRenew = false

	// Save the updated subscription to the database
	if err := database.DB.Save(&dbSubscription).Error; err != nil {
		return fmt.Errorf("failed to update subscription: %v", err)
	}

	return nil
}
