"use client";

import { useState, useEffect } from "react";
import { useSession } from "next-auth/react";
import { Card, CardContent, CardHeader, CardTitle, CardDescription, CardFooter } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { useToast } from "@/components/ui/use-toast";
import { useRouter } from "next/navigation";
import {
  Storefront,
  CurrencyDollar,
  User,
  Camera,
  EnvelopeSimple,
  Phone,
  MapPin,
  Users,
  ChartBar,
  Shield
} from "phosphor-react";
import {
  useGetCurrentUserQuery,
  useUpdateUserMutation,
  useGetMerchantShopsQuery,
  useGetMerchantCreditStatsQuery,
  useGetShopCustomersQuery
} from "@/lib/api/apiSlice";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Separator } from "@/components/ui/separator";
import { Skeleton } from "@/components/ui/skeleton";
import { MerchantBreadcrumbs } from "@/components/navigation/breadcrumbs";
import { MerchantPageHeader } from "@/components/layout/page-header-mobile";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";

export default function MerchantProfilePage() {
  const { update } = useSession();
  const { toast } = useToast();
  const router = useRouter();

  // State for edit dialog
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [profileData, setProfileData] = useState({
    name: "",
    picture: "",
    email: "",
    phone: "",
    address: "",
    bio: "",
    defaultMode: "merchant" // Default to merchant mode
  });

  // Fetch user data
  const {
    data: user,
    isLoading: isUserLoading,
    refetch: refetchUser
  } = useGetCurrentUserQuery();

  // Fetch merchant shops
  const {
    data: shops,
    isLoading: isShopsLoading
  } = useGetMerchantShopsQuery();

  // Fetch merchant credit stats
  const {
    data: creditStats,
    isLoading: isStatsLoading
  } = useGetMerchantCreditStatsQuery();

  // Calculate total customers across all shops
  const [totalCustomers, setTotalCustomers] = useState(0);
  const [isLoadingCustomers, setIsLoadingCustomers] = useState(true);

  // Update user mutation
  const [updateUserMutation, { isLoading: isUpdating }] = useUpdateUserMutation();

  // Initialize profile data when user data is loaded
  useEffect(() => {
    if (user) {
      // Try to get the default mode from localStorage
      let savedDefaultMode = null;
      if (typeof window !== 'undefined') {
        savedDefaultMode = localStorage.getItem('userDefaultMode');
      }

      setProfileData({
        name: user.name || "",
        picture: user.picture || "",
        email: user.email || "",
        phone: user.phone || "",
        address: user.address || "",
        bio: user.bio || "",
        // Use saved preference from localStorage, or default to merchant
        defaultMode: savedDefaultMode || "merchant"
      });
    }
  }, [user]);

  // Fetch customer counts for all shops
  useEffect(() => {
    const fetchCustomerCounts = async () => {
      if (!shops || shops.length === 0) {
        setIsLoadingCustomers(false);
        return;
      }

      try {
        let count = 0;
        for (const shop of shops) {
          // We're not actually using the hook here, just simulating the API call
          // In a real implementation, you would use a dedicated endpoint for this
          const response = await fetch(`/api/v1/merchant-shops/${shop.id}/customers`);
          const customers = await response.json();
          count += customers.length;
        }
        setTotalCustomers(count);
      } catch (error) {
        console.error("Error fetching customer counts:", error);
      } finally {
        setIsLoadingCustomers(false);
      }
    };

    if (shops) {
      fetchCustomerCounts();
    }
  }, [shops]);

  // Handle profile update
  const handleUpdateProfile = async () => {
    try {
      // Store default mode in localStorage since we don't have a real API endpoint for this yet
      localStorage.setItem('userDefaultMode', profileData.defaultMode);

      // Only update the name and picture fields to avoid conflicts with other fields
      // that might have unique constraints (like GoogleID)
      const updatedUser = await updateUserMutation({
        name: profileData.name,
        picture: profileData.picture
      }).unwrap();

      // Update the session with the new user data
      await update({
        user: {
          name: updatedUser.name,
          picture: updatedUser.picture,
        },
      });

      toast({
        title: "Profile updated",
        description: "Your profile has been updated successfully."
      });

      setIsEditDialogOpen(false);
      refetchUser();
    } catch (error) {
      console.error("Error updating profile:", error);
      toast({
        title: "Error",
        description: "Failed to update profile. Please try again.",
        variant: "destructive"
      });
    }
  };

  // Breadcrumb segments
  const breadcrumbSegments = [
    { label: "Dashboard", href: "/dashboard/merchant" },
    { label: "Profile", href: "/dashboard/merchant/profile" },
  ];

  return (
    <div className="space-y-6">
      {/* Breadcrumbs - Desktop only */}
      <div className="hidden md:block">
        <MerchantBreadcrumbs segments={breadcrumbSegments} />
      </div>

      {/* Mobile Header */}
      <div className="md:hidden">
        <MerchantPageHeader
          title="Profile"
          backHref="/dashboard/merchant"
        />
      </div>

      {/* Page Header - Desktop only */}
      <div className="hidden md:block">
        <h1 className="text-2xl font-bold tracking-tight md:text-3xl text-[#181510]">
          Merchant Profile
        </h1>
        <p className="text-[#8a745c] mt-1">
          View and manage your profile information
        </p>
      </div>

      {/* Profile Content */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Left Column - Profile Card */}
        <div className="lg:col-span-1">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <User className="mr-2 h-5 w-5" />
                Profile
              </CardTitle>
              <CardDescription>
                Your personal information
              </CardDescription>
            </CardHeader>
            <CardContent className="flex flex-col items-center space-y-4">
              <div className="relative">
                {isUserLoading ? (
                  <Skeleton className="h-24 w-24 rounded-full" />
                ) : (
                  <Avatar className="h-24 w-24">
                    <AvatarImage src={user?.picture} alt={user?.name} />
                    <AvatarFallback className="bg-[#e5ccb2] text-[#181510] text-2xl">
                      {user?.name?.charAt(0) || "U"}
                    </AvatarFallback>
                  </Avatar>
                )}
                <Button
                  variant="outline"
                  size="icon"
                  className="absolute bottom-0 right-0 rounded-full bg-white"
                  onClick={() => setIsEditDialogOpen(true)}
                >
                  <Camera size={16} />
                </Button>
              </div>

              <div className="text-center">
                {isUserLoading ? (
                  <>
                    <Skeleton className="h-6 w-32 mx-auto mb-2" />
                    <Skeleton className="h-4 w-24 mx-auto" />
                  </>
                ) : (
                  <>
                    <h2 className="text-xl font-bold">{user?.name}</h2>
                    <p className="text-[#8a745c]">
                      {user?.role === "admin" ? "Administrator" : "Merchant Account"}
                    </p>
                  </>
                )}
              </div>

              <Button
                onClick={() => setIsEditDialogOpen(true)}
                className="bg-[#e5ccb2] text-[#181510] hover:bg-[#d9b99a]"
              >
                Edit Profile
              </Button>
            </CardContent>

            <Separator className="my-2" />

            <CardContent className="space-y-4 pt-4">
              {isUserLoading ? (
                <div className="space-y-4">
                  <Skeleton className="h-5 w-full" />
                  <Skeleton className="h-5 w-full" />
                  <Skeleton className="h-5 w-full" />
                </div>
              ) : (
                <>
                  <div className="flex items-center gap-2">
                    <EnvelopeSimple size={20} className="text-[#8a745c]" />
                    <span>{user?.email}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Phone size={20} className="text-[#8a745c]" />
                    <span>{profileData.phone || "No phone number added"}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <MapPin size={20} className="text-[#8a745c]" />
                    <span>{profileData.address || "No address added"}</span>
                  </div>

                  <div className="flex items-center gap-2 mt-4 pt-4 border-t border-[#f1edea]">
                    <div className="flex-1">
                      <h3 className="text-sm font-medium">Default Dashboard</h3>
                      <p className="text-xs text-[#8a745c]">
                        {profileData.defaultMode === "merchant"
                          ? "Merchant Dashboard"
                          : "Customer Dashboard"}
                      </p>
                    </div>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setIsEditDialogOpen(true)}
                    >
                      Change
                    </Button>
                  </div>
                </>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Right Column - Stats Cards */}
        <div className="lg:col-span-2">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <ChartBar className="mr-2 h-5 w-5" />
                Merchant Statistics
              </CardTitle>
              <CardDescription>
                Overview of your merchant activity
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                {/* Shops Card */}
                <div className="bg-[#f1edea] p-4 rounded-lg">
                  <div className="flex items-center gap-3 mb-2">
                    <div className="bg-[#e5ccb2] rounded-full p-2">
                      <Storefront size={20} className="text-[#181510]" />
                    </div>
                    <h3 className="font-medium">Total Shops</h3>
                  </div>
                  {isShopsLoading ? (
                    <Skeleton className="h-8 w-16" />
                  ) : (
                    <p className="text-2xl font-bold text-[#181510]">
                      {shops?.length || 0}
                    </p>
                  )}
                </div>

                {/* Customers Card */}
                <div className="bg-[#f1edea] p-4 rounded-lg">
                  <div className="flex items-center gap-3 mb-2">
                    <div className="bg-[#e5ccb2] rounded-full p-2">
                      <Users size={20} className="text-[#181510]" />
                    </div>
                    <h3 className="font-medium">Total Customers</h3>
                  </div>
                  {isLoadingCustomers ? (
                    <Skeleton className="h-8 w-16" />
                  ) : (
                    <p className="text-2xl font-bold text-[#181510]">
                      {totalCustomers}
                    </p>
                  )}
                </div>

                {/* Credits Issued Card */}
                <div className="bg-[#f1edea] p-4 rounded-lg">
                  <div className="flex items-center gap-3 mb-2">
                    <div className="bg-[#e5ccb2] rounded-full p-2">
                      <CurrencyDollar size={20} className="text-[#181510]" />
                    </div>
                    <h3 className="font-medium">Credits Issued</h3>
                  </div>
                  {isStatsLoading ? (
                    <Skeleton className="h-8 w-24" />
                  ) : (
                    <p className="text-2xl font-bold text-[#181510]">
                      ${creditStats?.total_credits_issued.toLocaleString() || "0"}
                    </p>
                  )}
                </div>

                {/* Credits Redeemed Card */}
                <div className="bg-[#f1edea] p-4 rounded-lg">
                  <div className="flex items-center gap-3 mb-2">
                    <div className="bg-[#e5ccb2] rounded-full p-2">
                      <CurrencyDollar size={20} className="text-[#181510]" />
                    </div>
                    <h3 className="font-medium">Credits Redeemed</h3>
                  </div>
                  {isStatsLoading ? (
                    <Skeleton className="h-8 w-24" />
                  ) : (
                    <p className="text-2xl font-bold text-[#181510]">
                      ${creditStats?.total_credits_redeemed.toLocaleString() || "0"}
                    </p>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>

          <div className="mt-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Shield className="mr-2 h-5 w-5" />
                  Account Security
                </CardTitle>
                <CardDescription>
                  Manage your account security settings
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <h3 className="font-medium">Password</h3>
                    <p className="text-sm text-[#8a745c]">Change your account password</p>
                  </div>
                  <Button variant="outline" disabled>
                    Change
                  </Button>
                </div>

                <Separator />

                <div className="flex items-center justify-between">
                  <div>
                    <h3 className="font-medium">Account Privacy</h3>
                    <p className="text-sm text-[#8a745c]">Manage your account privacy settings</p>
                  </div>
                  <Button variant="outline" disabled>
                    Manage
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>

      {/* Edit Profile Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Edit Profile</DialogTitle>
            <DialogDescription>
              Update your profile information
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid gap-2">
              <Label htmlFor="name">Name</Label>
              <Input
                id="name"
                value={profileData.name}
                onChange={(e) => setProfileData({ ...profileData, name: e.target.value })}
              />
            </div>
            <div className="grid gap-2">
              <Label htmlFor="picture">Profile Picture URL</Label>
              <Input
                id="picture"
                value={profileData.picture}
                onChange={(e) => setProfileData({ ...profileData, picture: e.target.value })}
                placeholder="https://example.com/your-image.jpg"
              />
              <p className="text-xs text-[#8a745c]">
                Enter a URL for your profile picture
              </p>
            </div>
            <div className="grid gap-2">
              <Label htmlFor="email">Email</Label>
              <Input
                id="email"
                type="email"
                value={profileData.email}
                disabled
                className="bg-[#f1edea]"
              />
              <p className="text-xs text-[#8a745c]">Email cannot be changed</p>
            </div>

            <div className="grid gap-2">
              <Label htmlFor="defaultMode">Default Dashboard Mode</Label>
              <select
                id="defaultMode"
                value={profileData.defaultMode}
                onChange={(e) => setProfileData({ ...profileData, defaultMode: e.target.value })}
                className="w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
              >
                <option value="merchant">Merchant Dashboard</option>
                <option value="customer">Customer Dashboard</option>
              </select>
              <p className="text-xs text-[#8a745c]">
                Choose which dashboard you'll see first when you log in
              </p>
            </div>
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setIsEditDialogOpen(false)}
            >
              Cancel
            </Button>
            <Button
              onClick={handleUpdateProfile}
              className="bg-[#e5ccb2] text-[#181510] hover:bg-[#d9b99a]"
              disabled={isUpdating}
            >
              {isUpdating ? "Saving..." : "Save Changes"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}