"use client";

import dynamic from 'next/dynamic';
import React from 'react';

export default function ShopDetailPage({ params }: { params: { id: string } }) {
  // Import the ShopDetail component
  const ShopDetail = dynamic(() => import('./shop-detail'), { ssr: false });

  // Unwrap params using React.use() as recommended by Next.js
  const unwrappedParams = React.use(params as any) as { id: string };

  return <ShopDetail shopId={unwrappedParams.id} />;
}
