package main

import (
	"fmt"
	"log"

	"github.com/adc-credit/backend/internal/database"
	"github.com/adc-credit/backend/internal/models"
	"github.com/google/uuid"
	"github.com/joho/godotenv"
)

func main() {
	// Load environment variables
	if err := godotenv.Load(); err != nil {
		// Try to load from the project root directory
		if err := godotenv.Load("../../.env"); err != nil {
			log.Println("No .env file found, using environment variables")
		}
	}

	// Initialize database connection
	if err := database.InitDB(); err != nil {
		log.Fatalf("Failed to initialize database: %v", err)
	}

	fmt.Println("Testing unified Shop model...")

	if err := testUnifiedShopModel(); err != nil {
		log.Fatalf("Test failed: %v", err)
	}

	fmt.Println("All tests passed!")
}

func testUnifiedShopModel() error {
	// Create a test user
	testUser := models.User{
		ID:    uuid.New(),
		Email: "<EMAIL>",
		Name:  "Test User",
		Role:  "user",
	}

	if err := database.DB.Create(&testUser).Error; err != nil {
		return fmt.Errorf("failed to create test user: %w", err)
	}

	// Test 1: Create a retail shop
	retailShop := models.Shop{
		ID:           uuid.New(),
		Slug:         "test-coffee-shop",
		Name:         "Test Coffee Shop",
		Description:  "A test coffee shop",
		ShopType:     "retail",
		ContactEmail: "<EMAIL>",
		ContactPhone: "+1234567890",
		OwnerUserID:  testUser.ID,
	}

	if err := database.DB.Create(&retailShop).Error; err != nil {
		return fmt.Errorf("failed to create retail shop: %w", err)
	}
	fmt.Println("✓ Created retail shop successfully")

	// Test 2: Create an API service shop
	apiShop := models.Shop{
		ID:          uuid.New(),
		Slug:        "test-api-service",
		Name:        "Test API Service",
		Description: "A test API service company",
		ShopType:    "api_service",
		OwnerUserID: testUser.ID,
	}

	if err := database.DB.Create(&apiShop).Error; err != nil {
		return fmt.Errorf("failed to create API service shop: %w", err)
	}
	fmt.Println("✓ Created API service shop successfully")

	// Test 3: Create an enterprise shop
	enterpriseShop := models.Shop{
		ID:           uuid.New(),
		Slug:         "test-enterprise",
		Name:         "Test Enterprise",
		Description:  "A test enterprise company",
		ShopType:     "enterprise",
		ContactEmail: "<EMAIL>",
		OwnerUserID:  testUser.ID,
	}

	if err := database.DB.Create(&enterpriseShop).Error; err != nil {
		return fmt.Errorf("failed to create enterprise shop: %w", err)
	}
	fmt.Println("✓ Created enterprise shop successfully")

	// Test 4: Create shop branches
	// Location branch for retail shop
	locationBranch := models.ShopBranch{
		ID:           uuid.New(),
		ShopID:       retailShop.ID,
		Name:         "Downtown Location",
		Description:  "Downtown coffee shop location",
		BranchType:   "location",
		ContactEmail: "<EMAIL>",
		Address:      "123 Main St, Downtown",
	}

	if err := database.DB.Create(&locationBranch).Error; err != nil {
		return fmt.Errorf("failed to create location branch: %w", err)
	}
	fmt.Println("✓ Created location branch successfully")

	// Department branch for API service shop
	departmentBranch := models.ShopBranch{
		ID:          uuid.New(),
		ShopID:      apiShop.ID,
		Name:        "Engineering Department",
		Description: "Engineering team department",
		BranchType:  "department",
	}

	if err := database.DB.Create(&departmentBranch).Error; err != nil {
		return fmt.Errorf("failed to create department branch: %w", err)
	}
	fmt.Println("✓ Created department branch successfully")

	// Test 5: Create API keys for shops and branches
	// Shop-level API key
	shopAPIKey := models.APIKey{
		ID:      uuid.New(),
		UserID:  testUser.ID,
		ShopID:  &retailShop.ID,
		Name:    "Retail Shop API Key",
		Key:     "test-shop-api-key-" + uuid.New().String(),
		Enabled: true,
	}

	if err := database.DB.Create(&shopAPIKey).Error; err != nil {
		return fmt.Errorf("failed to create shop API key: %w", err)
	}
	fmt.Println("✓ Created shop-level API key successfully")

	// Branch-level API key
	branchAPIKey := models.APIKey{
		ID:           uuid.New(),
		UserID:       testUser.ID,
		ShopBranchID: &locationBranch.ID,
		Name:         "Location Branch API Key",
		Key:          "test-branch-api-key-" + uuid.New().String(),
		Enabled:      true,
	}

	if err := database.DB.Create(&branchAPIKey).Error; err != nil {
		return fmt.Errorf("failed to create branch API key: %w", err)
	}
	fmt.Println("✓ Created branch-level API key successfully")

	// Test 6: Query shops with relationships
	var shops []models.Shop
	if err := database.DB.Preload("Branches").Preload("APIKeys").Find(&shops).Error; err != nil {
		return fmt.Errorf("failed to query shops with relationships: %w", err)
	}

	fmt.Printf("✓ Found %d shops with relationships\n", len(shops))

	// Test 7: Query by shop type
	var retailShops []models.Shop
	if err := database.DB.Where("shop_type = ?", "retail").Find(&retailShops).Error; err != nil {
		return fmt.Errorf("failed to query retail shops: %w", err)
	}

	fmt.Printf("✓ Found %d retail shops\n", len(retailShops))

	var apiShops []models.Shop
	if err := database.DB.Where("shop_type = ?", "api_service").Find(&apiShops).Error; err != nil {
		return fmt.Errorf("failed to query API service shops: %w", err)
	}

	fmt.Printf("✓ Found %d API service shops\n", len(apiShops))

	// Cleanup test data
	database.DB.Delete(&branchAPIKey)
	database.DB.Delete(&shopAPIKey)
	database.DB.Delete(&departmentBranch)
	database.DB.Delete(&locationBranch)
	database.DB.Delete(&enterpriseShop)
	database.DB.Delete(&apiShop)
	database.DB.Delete(&retailShop)
	database.DB.Delete(&testUser)

	fmt.Println("✓ Cleaned up test data")

	return nil
}
