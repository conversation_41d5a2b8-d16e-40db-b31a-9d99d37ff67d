"use client";

import { useSession } from "next-auth/react";
import { useEffect, useState } from "react";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { APIKey } from "@/types";
import { toast } from "sonner";
import Link from "next/link";
import {
  useGetAPIKeysQuery,
  useCreateAPIKeyMutation,
  useUpdateAPIKeyMutation,
  useDeleteAPIKeyMutation
} from "@/lib/api/apiSlice";

// Define available permissions
const availablePermissions = [
  { id: "read", label: "Read" },
  { id: "write", label: "Write" },
  { id: "delete", label: "Delete" },
  { id: "admin", label: "Admin" },
];

export default function APIKeysPage() {
  const { data: session } = useSession();
  const [newKeyName, setNewKeyName] = useState("");
  const [newKeyDialogOpen, setNewKeyDialogOpen] = useState(false);
  const [newKey, setNewKey] = useState<APIKey | null>(null);
  const [selectedPermissions, setSelectedPermissions] = useState<string[]>(["read"]);

  // Check for updated query parameter
  const [showUpdateSuccess, setShowUpdateSuccess] = useState(false);

  useEffect(() => {
    // Check if we have the updated query parameter
    const urlParams = new URLSearchParams(window.location.search);
    if (urlParams.get('updated') === 'true') {
      setShowUpdateSuccess(true);

      // Remove the query parameter from the URL without refreshing the page
      const newUrl = window.location.pathname;
      window.history.replaceState({}, document.title, newUrl);

      // Hide the success message after 5 seconds
      const timer = setTimeout(() => {
        setShowUpdateSuccess(false);
      }, 5000);

      return () => clearTimeout(timer);
    }
  }, []);

  // Store access token in session storage if available
  useEffect(() => {
    // Use type assertion to access accessToken
    const userAccessToken = (session?.user as any)?.accessToken;
    const sessionAccessToken = (session as any)?.accessToken;

    if (userAccessToken) {
      console.log('Setting access token from user:', userAccessToken.substring(0, 10) + '...');
      sessionStorage.setItem('accessToken', userAccessToken);
    } else if (sessionAccessToken) {
      console.log('Setting access token from session:', sessionAccessToken.substring(0, 10) + '...');
      sessionStorage.setItem('accessToken', sessionAccessToken);
    } else {
      console.warn('No access token found in session');
    }
  }, [session]);

  // Use RTK Query hooks
  const {
    data: apiKeys = [],
    isLoading,
    refetch
  } = useGetAPIKeysQuery();

  const [createAPIKey, { isLoading: isCreating }] = useCreateAPIKeyMutation();
  const [updateAPIKey, { isLoading: isUpdating }] = useUpdateAPIKeyMutation();
  const [deleteAPIKey, { isLoading: isDeleting }] = useDeleteAPIKeyMutation();

  // Create a new API key
  const handleCreateAPIKey = async () => {
    if (!newKeyName.trim()) return;

    try {
      const result = await createAPIKey({
        name: newKeyName,
        permissions: selectedPermissions
      }).unwrap();
      setNewKey(result);
      setNewKeyName("");
      setSelectedPermissions(["read"]); // Reset to default
      setNewKeyDialogOpen(false);
      toast.success("API key created successfully");
    } catch (error) {
      console.error("Error creating API key:", error);
      toast.error("Failed to create API key");
    }
  };

  // Toggle API key enabled/disabled status
  const handleToggleAPIKey = async (id: string, enabled: boolean) => {
    try {
      await updateAPIKey({ id, enabled }).unwrap();
      toast.success(`API key ${enabled ? "enabled" : "disabled"}`);
    } catch (error) {
      console.error("Error updating API key:", error);
      toast.error("Failed to update API key");
    }
  };

  // Delete an API key
  const handleDeleteAPIKey = async (id: string) => {
    try {
      await deleteAPIKey(id).unwrap();
      toast.success("API key deleted");
    } catch (error) {
      console.error("Error deleting API key:", error);
      toast.error("Failed to delete API key");
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  if (isLoading || isCreating || isUpdating || isDeleting) {
    return (
      <div className="flex h-[calc(100vh-4rem)] items-center justify-center">
        <div className="h-8 w-8 animate-spin rounded-full border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className="container py-10">
      {showUpdateSuccess && (
        <Alert className="mb-6 bg-green-50 border-green-200">
          <AlertDescription className="text-green-800">
            API key permissions updated successfully.
          </AlertDescription>
        </Alert>
      )}

      <div className="flex justify-between items-center mb-8">
        <h1 className="text-3xl font-bold">API Keys</h1>
        <Dialog open={newKeyDialogOpen} onOpenChange={setNewKeyDialogOpen}>
          <DialogTrigger asChild>
            <Button>Create New API Key</Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Create New API Key</DialogTitle>
              <DialogDescription>
                Enter a name for your new API key and select permissions. You will only be able to view the key once after creation.
              </DialogDescription>
            </DialogHeader>
            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="name" className="text-right">
                  Name
                </Label>
                <Input
                  id="name"
                  value={newKeyName}
                  onChange={(e) => setNewKeyName(e.target.value)}
                  className="col-span-3"
                  placeholder="My API Key"
                />
              </div>

              <div className="mt-4">
                <Label className="text-sm font-medium">Permissions</Label>
                <div className="grid grid-cols-2 gap-2 mt-2">
                  {availablePermissions.map((permission) => (
                    <div key={permission.id} className="flex items-center space-x-2">
                      <Checkbox
                        id={`permission-${permission.id}`}
                        checked={selectedPermissions.includes(permission.id)}
                        onCheckedChange={(checked: boolean) => {
                          if (checked) {
                            setSelectedPermissions([...selectedPermissions, permission.id]);
                          } else {
                            setSelectedPermissions(
                              selectedPermissions.filter((p) => p !== permission.id)
                            );
                          }
                        }}
                      />
                      <Label
                        htmlFor={`permission-${permission.id}`}
                        className="text-sm font-normal"
                      >
                        {permission.label}
                      </Label>
                    </div>
                  ))}
                </div>
              </div>
            </div>
            <DialogFooter>
              <Button
                onClick={() => {
                  handleCreateAPIKey();
                }}
                disabled={!newKeyName.trim()}
              >
                Create
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>

      {newKey && (
        <Alert className="mb-6">
          <AlertDescription className="flex flex-col gap-2">
            <p>
              <strong>Important:</strong> Copy your new API key now. You won&apos;t be able to see it again!
            </p>
            <div className="flex items-center gap-2">
              <code className="relative rounded bg-muted px-[0.3rem] py-[0.2rem] font-mono text-sm">
                {newKey.key}
              </code>
              <Button
                variant="outline"
                size="sm"
                onClick={() => {
                  navigator.clipboard.writeText(newKey.key);
                  toast.success("API key copied to clipboard");
                }}
              >
                Copy
              </Button>
            </div>
            <Button
              variant="outline"
              size="sm"
              className="mt-2 w-fit"
              onClick={() => setNewKey(null)}
            >
              Dismiss
            </Button>
          </AlertDescription>
        </Alert>
      )}

      <Card>
        <CardHeader>
          <CardTitle>Your API Keys</CardTitle>
          <CardDescription>
            Manage your API keys for integrating with external systems
          </CardDescription>
        </CardHeader>
        <CardContent>
          {apiKeys.length > 0 ? (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Name</TableHead>
                  <TableHead>Created</TableHead>
                  <TableHead>Last Used</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {apiKeys.map((key) => (
                  <TableRow key={key.id}>
                    <TableCell className="font-medium">{key.name}</TableCell>
                    <TableCell>{formatDate(key.created_at)}</TableCell>
                    <TableCell>
                      {key.last_used ? formatDate(key.last_used) : "Never"}
                    </TableCell>
                    <TableCell>
                      <span
                        className={`inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium ${
                          key.enabled
                            ? "bg-green-100 text-green-800"
                            : "bg-red-100 text-red-800"
                        }`}
                      >
                        {key.enabled ? "Active" : "Disabled"}
                      </span>
                    </TableCell>
                    <TableCell className="text-right">
                      <div className="flex justify-end gap-2">
                        <Link href={`/dashboard/api-keys/${key.id}`}>
                          <Button
                            variant="outline"
                            size="sm"
                          >
                            View
                          </Button>
                        </Link>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() =>
                            handleToggleAPIKey(key.id, !key.enabled)
                          }
                        >
                          {key.enabled ? "Disable" : "Enable"}
                        </Button>
                        <Button
                          variant="destructive"
                          size="sm"
                          onClick={() => handleDeleteAPIKey(key.id)}
                        >
                          Delete
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          ) : (
            <div className="py-8 text-center text-muted-foreground">
              No API keys found. Create your first API key to get started.
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
