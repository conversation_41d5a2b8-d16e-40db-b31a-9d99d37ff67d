"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import Link from "next/link";
import Image from "next/image";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import * as z from "zod";
import { Copy, Download, Share, QrCode } from "phosphor-react";
import { MerchantBreadcrumbs } from "@/components/navigation/breadcrumbs";
import { MerchantPageHeader } from "@/components/layout/page-header-mobile";
import { useToast } from "@/components/ui/use-toast";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Separator } from "@/components/ui/separator";
import { useGetMerchantShopQuery, useGenerateCreditCodeMutation } from "@/lib/api/apiSlice";
import { CreditCode } from "@/types";

// Define form schema with Zod
const formSchema = z.object({
  amount: z.coerce.number().positive({ message: "Amount must be greater than 0" }),
  description: z.string().optional(),
  expiresIn: z.coerce.number().min(0, { message: "Expiration days must be 0 or greater" }),
});

type FormValues = z.infer<typeof formSchema>;

export default function GenerateCodePage({ params }: { params: { id: string } }) {
  const router = useRouter();
  const { toast } = useToast();
  const [isGenerating, setIsGenerating] = useState(false);
  const [generatedCode, setGeneratedCode] = useState<{ credit_code: CreditCode; qr_code: string } | null>(null);
  const [isDownloading, setIsDownloading] = useState(false);

  // Fetch shop data
  const { data: shop, isLoading: isShopLoading } = useGetMerchantShopQuery(params.id);

  // Generate code mutation
  const [generateCode] = useGenerateCreditCodeMutation();

  // Initialize React Hook Form
  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      amount: 0,
      description: "",
      expiresIn: 30,
    },
  });

  // Handle form submission
  const onSubmit = async (data: FormValues) => {
    setIsGenerating(true);

    try {
      const result = await generateCode({
        shopId: params.id,
        amount: data.amount,
        description: data.description,
        expiresIn: data.expiresIn
      }).unwrap();

      setGeneratedCode(result);

      toast({
        title: "Success",
        description: "Credit code generated successfully",
      });
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.data?.message || "Failed to generate code",
        variant: "destructive",
      });
    } finally {
      setIsGenerating(false);
    }
  };

  // Copy code to clipboard
  const copyToClipboard = () => {
    if (!generatedCode) return;

    navigator.clipboard.writeText(generatedCode.credit_code.code);
    toast({
      title: "Copied",
      description: "Credit code copied to clipboard",
    });
  };

  // Download QR code
  const downloadQRCode = () => {
    if (!generatedCode) return;

    setIsDownloading(true);

    const link = document.createElement("a");
    link.href = generatedCode.qr_code;
    link.download = `credit-code-${generatedCode.credit_code.code}.png`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    setTimeout(() => {
      setIsDownloading(false);
    }, 1000);

    toast({
      title: "Downloaded",
      description: "QR code has been downloaded",
    });
  };

  // Share code
  const shareCode = async () => {
    if (!generatedCode) return;

    if (navigator.share) {
      try {
        await navigator.share({
          title: "Credit Code",
          text: `Here's your credit code: ${generatedCode.credit_code.code} for ${generatedCode.credit_code.amount} credits`,
          url: window.location.href,
        });

        toast({
          title: "Shared",
          description: "Credit code has been shared",
        });
      } catch (error) {
        console.error("Error sharing:", error);
      }
    } else {
      copyToClipboard();
    }
  };

  // Loading state
  if (isShopLoading) {
    return (
      <div className="relative flex size-full min-h-screen flex-col bg-[#fbfaf9] justify-between overflow-x-hidden">
        <div className="flex items-center bg-[#fbfaf9] p-4 pb-2 justify-between">
          <div className="size-12 shrink-0"></div>
          <h2 className="text-[#181510] text-lg font-bold leading-tight tracking-[-0.015em] flex-1 text-center pr-12">Generate Code</h2>
        </div>
        <div className="p-4 flex justify-center items-center h-full">
          <div className="animate-pulse flex flex-col items-center space-y-4 w-full max-w-md">
            <div className="h-8 bg-gray-200 rounded w-3/4"></div>
            <div className="h-32 bg-gray-200 rounded w-full"></div>
            <div className="h-10 bg-gray-200 rounded w-1/2"></div>
          </div>
        </div>
      </div>
    );
  }

  // Error state
  if (!shop) {
    return (
      <div className="relative flex size-full min-h-screen flex-col bg-[#fbfaf9] justify-between overflow-x-hidden">
        <div className="flex items-center bg-[#fbfaf9] p-4 pb-2 justify-between">
          <Link href="/dashboard/merchant" className="text-[#181510] flex size-12 shrink-0 items-center">
            <ArrowLeft size={24} weight="regular" />
          </Link>
          <h2 className="text-[#181510] text-lg font-bold leading-tight tracking-[-0.015em] flex-1 text-center pr-12">Generate Code</h2>
        </div>
        <div className="p-4 text-center">
          <p className="text-[#8a745c] text-lg">Shop not found</p>
          <Button
            className="mt-4 bg-[#e5ccb2] text-[#181510] hover:bg-[#d9b99a]"
            onClick={() => router.push('/dashboard/merchant')}
          >
            Return to Shops
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="relative flex size-full min-h-screen flex-col bg-[#fbfaf9] justify-between overflow-x-hidden">
      {/* Header */}
      <MerchantPageHeader
        title="Generate Code"
        backHref={`/dashboard/merchant/${params.id}`}
      />

      {/* Breadcrumbs */}
      <MerchantBreadcrumbs
        segments={[
          { label: "Shops", href: "/dashboard/merchant" },
          { label: shop?.name || "Shop", href: `/dashboard/merchant/${params.id}` },
          { label: "Generate Code", href: `/dashboard/merchant/${params.id}/generate-code`, isCurrentPage: true }
        ]}
      />

      <div className="flex-1 p-4">
        <div className="w-full max-w-md mx-auto">
          <div className="mb-6 text-center">
            <h3 className="text-[#181510] text-xl font-bold mb-2">{shop.name}</h3>
            <p className="text-[#8a745c] text-sm">{shop.description}</p>
          </div>

          {!generatedCode ? (
            <Card>
              <CardHeader>
                <CardTitle className="text-center">Create Credit Code</CardTitle>
              </CardHeader>
              <CardContent>
                <Form {...form}>
                  <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                    <FormField
                      control={form.control}
                      name="amount"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="text-[#181510] text-base font-medium">Credit Amount</FormLabel>
                          <FormControl>
                            <Input
                              type="number"
                              placeholder="Enter amount"
                              className="form-input flex w-full min-w-0 flex-1 resize-none overflow-hidden rounded-lg text-[#181510] focus:outline-0 focus:ring-0 border-none bg-[#f1edea] focus:border-none h-14 placeholder:text-[#8a745c] p-4 text-base font-normal leading-normal"
                              {...field}
                            />
                          </FormControl>
                          <FormDescription>
                            The amount of credits this code will provide
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="description"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="text-[#181510] text-base font-medium">Description (Optional)</FormLabel>
                          <FormControl>
                            <Textarea
                              placeholder="Enter description"
                              className="form-input flex w-full min-w-0 flex-1 resize-none overflow-hidden rounded-lg text-[#181510] focus:outline-0 focus:ring-0 border-none bg-[#f1edea] focus:border-none min-h-[80px] placeholder:text-[#8a745c] p-4 text-base font-normal leading-normal"
                              {...field}
                            />
                          </FormControl>
                          <FormDescription>
                            A description to help identify this code
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="expiresIn"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="text-[#181510] text-base font-medium">Expires In (Days)</FormLabel>
                          <FormControl>
                            <Input
                              type="number"
                              placeholder="Enter days"
                              className="form-input flex w-full min-w-0 flex-1 resize-none overflow-hidden rounded-lg text-[#181510] focus:outline-0 focus:ring-0 border-none bg-[#f1edea] focus:border-none h-14 placeholder:text-[#8a745c] p-4 text-base font-normal leading-normal"
                              {...field}
                            />
                          </FormControl>
                          <FormDescription>
                            Number of days until expiration (0 for no expiration)
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <Button
                      type="submit"
                      className="w-full bg-[#e5ccb2] text-[#181510] hover:bg-[#d9b99a] h-14 text-base font-bold"
                      disabled={isGenerating}
                    >
                      {isGenerating ? "Generating..." : "Generate Code"}
                    </Button>
                  </form>
                </Form>
              </CardContent>
            </Card>
          ) : (
            <Card>
              <CardHeader>
                <CardTitle className="text-center">Credit Code: ${generatedCode.credit_code.amount}</CardTitle>
              </CardHeader>
              <CardContent className="flex flex-col items-center">
                <div className="relative w-64 h-64 mb-4">
                  <Image
                    src={generatedCode.qr_code}
                    alt={`QR Code for ${generatedCode.credit_code.code}`}
                    fill
                    className="object-contain"
                    priority
                  />
                </div>

                <div className="w-full p-3 bg-muted rounded-md text-center mb-4">
                  <p className="text-lg font-mono font-bold break-all">{generatedCode.credit_code.code}</p>
                </div>

                {generatedCode.credit_code.description && (
                  <p className="text-muted-foreground text-center mb-2">{generatedCode.credit_code.description}</p>
                )}

                {generatedCode.credit_code.expires_at && (
                  <p className="text-sm text-muted-foreground text-center">
                    Expires: {new Date(generatedCode.credit_code.expires_at).toLocaleDateString()}
                  </p>
                )}
              </CardContent>
              <CardFooter className="flex flex-wrap justify-between gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={copyToClipboard}
                  className="flex-1 min-w-[100px]"
                >
                  <Copy size={16} className="mr-2" />
                  Copy Code
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={downloadQRCode}
                  disabled={isDownloading}
                  className="flex-1 min-w-[100px]"
                >
                  <Download size={16} className="mr-2" />
                  {isDownloading ? "Downloading..." : "Download QR"}
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={shareCode}
                  className="flex-1 min-w-[100px]"
                >
                  <Share size={16} className="mr-2" />
                  Share
                </Button>
              </CardFooter>
            </Card>
          )}
        </div>
      </div>

      {/* Footer */}
      <div className="mt-auto">
        <div className="flex justify-stretch">
          <div className="flex flex-1 gap-3 flex-wrap px-4 py-3 justify-between">
            <Button
              className="flex w-full cursor-pointer items-center justify-center overflow-hidden rounded-lg h-12 px-5 bg-[#f1edea] text-[#181510] text-base font-bold leading-normal tracking-[0.015em] hover:bg-[#e5e0dc]"
              onClick={() => generatedCode ? setGeneratedCode(null) : router.push(`/dashboard/merchant/${params.id}`)}
            >
              {generatedCode ? "Generate Another Code" : "Back to Shop"}
            </Button>
          </div>
        </div>
        <div className="h-5 bg-[#fbfaf9]"></div>
      </div>
    </div>
  );
}
