package handlers

import (
	"net/http"

	"github.com/adc-credit/backend/internal/database"
	"github.com/gin-gonic/gin"
)

// HealthCheck returns the health status of the API
func HealthCheck(c *gin.Context) {
	// Check database connection
	sqlDB, err := database.DB.DB()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"status":  "error",
			"message": "Database connection error",
			"error":   err.Error(),
		})
		return
	}

	if err := sqlDB.Ping(); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"status":  "error",
			"message": "Database ping failed",
			"error":   err.Error(),
		})
		return
	}

	c.<PERSON>(http.StatusOK, gin.H{
		"status":  "ok",
		"message": "API is running",
	})
}
