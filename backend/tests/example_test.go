package tests

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

// TestExample demonstrates the testing framework without database dependency
func TestExample(t *testing.T) {
	// Simple test to verify the testing framework is working
	assert.Equal(t, 2, 1+1, "Basic math should work")
	assert.True(t, true, "True should be true")
	assert.NotEmpty(t, "hello", "String should not be empty")
}

// TestExampleWithSubtests demonstrates subtests
func TestExampleWithSubtests(t *testing.T) {
	t.Run("Addition", func(t *testing.T) {
		result := 2 + 3
		assert.Equal(t, 5, result)
	})

	t.Run("Subtraction", func(t *testing.T) {
		result := 10 - 4
		assert.Equal(t, 6, result)
	})

	t.Run("String Operations", func(t *testing.T) {
		str := "Hello, World!"
		assert.Contains(t, str, "World")
		assert.Len(t, str, 13)
	})
}
