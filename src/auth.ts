import type { NextAuthOptions } from "next-auth";
import GoogleProvider from "next-auth/providers/google";
import CredentialsProvider from "next-auth/providers/credentials";
import { getServerSession } from "next-auth";

// Extend the default session type
declare module "next-auth" {
  interface Session {
    accessToken?: string;
    backendToken?: string; // Add backendToken to session
    error?: string;
    user: {
      id?: string;
      name?: string;
      email?: string;
      image?: string;
      picture?: string;
      role?: string;
      provider?: string;
      accessToken?: string; // Add accessToken to user object
    } & DefaultSession["user"];
  }

  interface JWT {
    accessToken?: string;
    backendToken?: string; // Add backendToken to JWT
    provider?: string;
    userId?: string;
    exchangeError?: string;
  }
}

import { DefaultSession } from "next-auth";

export const authOptions: NextAuthOptions = {
  // Enable debug mode in development
  debug: process.env.NODE_ENV === "development",

  // Configure providers
  providers: [
    GoogleProvider({
      clientId: process.env.GOOGLE_CLIENT_ID!,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET!,
    }),
    CredentialsProvider({
      name: "Email and Password",
      credentials: {
        email: { label: "Email", type: "email", placeholder: "<EMAIL>" },
        password: { label: "Password", type: "password" }
      },
      async authorize(credentials) {
        if (!credentials?.email || !credentials?.password) {
          return null;
        }

        try {
          console.log("Authorizing with credentials:", credentials.email);

          // Connect to the backend API
          const response = await fetch(`${process.env.NEXT_PUBLIC_BACKEND_URL}/api/v1/auth/login`, {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify({
              email: credentials.email,
              password: credentials.password,
            }),
          });

          const data = await response.json();
          console.log("Login response:", data);

          if (!response.ok) {
            console.error("Authentication failed:", data.error || "Unknown error");
            return null;
          }

          const accessToken = data.token || data.access_token;
          console.log("Got access token:", accessToken);

          if (!accessToken) {
            console.error("No access token in response");
            return null;
          }

          // Return user data with access token
          return {
            id: data.user.id,
            name: data.user.name,
            email: data.user.email,
            image: data.user.picture,
            picture: data.user.picture,
            role: data.user.role,
            accessToken: accessToken, // Include the access token from the API response
          };
        } catch (error) {
          console.error("Error during authentication:", error);
          return null;
        }
      },
    }),
  ],
  callbacks: {
    async jwt({ token, user, account }) {
      console.log("JWT callback called with token:", token, "and user:", user, "and account:", account);

      // Initial sign in
      if (user) {
        console.log("User object in JWT callback:", user);

        // For credentials provider, the accessToken is directly on the user object
        if (user.accessToken) {
          console.log("Found accessToken in user object:", user.accessToken);
          return {
            ...token,
            accessToken: user.accessToken,
            provider: "credentials",
            userId: user.id,
          };
        }
        // For OAuth providers, the access token is in the account object
        else if (account) {
          console.log("Found access_token in account object:", account.access_token);

          // For Google provider, exchange the token for a backend JWT token
          if (account.provider === "google" && account.access_token) {
            try {
              console.log("Exchanging Google token for backend JWT token");

              // Exchange the Google token for a backend JWT token
              const response = await fetch(`${process.env.NEXT_PUBLIC_BACKEND_URL}/api/v1/auth/google`, {
                method: "POST",
                headers: {
                  "Content-Type": "application/json",
                },
                body: JSON.stringify({ token: account.access_token }),
              });

              if (!response.ok) {
                console.error("Failed to exchange Google token:", response.status);
                // Still return the Google token so we can try again later
                return {
                  ...token,
                  accessToken: account.access_token,
                  provider: account.provider,
                  userId: user.id,
                  exchangeError: "Failed to exchange token",
                };
              }

              const data = await response.json();
              console.log("Successfully exchanged Google token for backend JWT token");

              // Store the backend JWT token in localStorage for persistence
              if (typeof window !== "undefined") {
                localStorage.setItem("accessToken", data.token);
                localStorage.setItem("refreshToken", data.refresh_token);
                localStorage.setItem("currentUser", JSON.stringify(data.user));

                // Also store in sessionStorage for immediate use
                sessionStorage.setItem("accessToken", data.token);
                sessionStorage.setItem("refreshToken", data.refresh_token);
                sessionStorage.setItem("currentUser", JSON.stringify(data.user));
              }

              return {
                ...token,
                accessToken: data.token, // Use the backend JWT token as the primary token
                provider: account.provider,
                userId: user.id,
              };
            } catch (error) {
              console.error("Error exchanging Google token:", error);
              // Still return the Google token so we can try again later
              return {
                ...token,
                accessToken: account.access_token,
                provider: account.provider,
                userId: user.id,
                exchangeError: error instanceof Error ? error.message : "Unknown error",
              };
            }
          }

          // For other providers, just return the token
          return {
            ...token,
            accessToken: account.access_token,
            provider: account.provider,
            userId: user.id,
          };
        }
      }

      // Check if we already have an accessToken in the token
      if (token.accessToken) {
        console.log("Using existing accessToken from token:", token.accessToken);
        return token;
      }

      console.warn("No accessToken found in user, account, or token objects");
      return token;
    },

    async session({ session, token }) {
      console.log("Session callback called with token:", token);

      // Add the access token to the session
      if (token.accessToken) {
        console.log("Adding accessToken to session:", token.accessToken);
        session.accessToken = token.accessToken as string;

        // Also add it to the user object for easier access
        session.user.accessToken = token.accessToken as string;
      } else {
        console.warn("No accessToken found in token object during session callback");
      }

      // Store the token in sessionStorage and localStorage for API calls
      if (token.accessToken) {
        console.log("Storing accessToken in storage for API calls");

        if (typeof window !== "undefined") {
          sessionStorage.setItem("accessToken", token.accessToken as string);
          localStorage.setItem("accessToken", token.accessToken as string);
        }
      }

      // Add provider information if available
      if (token.provider) {
        session.user.provider = token.provider as string;
      }

      // Add user ID if available
      if (token.userId) {
        session.user.id = token.userId as string;
      }

      console.log("Final session object:", session);
      return session;
    },

    // Simple redirect callback
    async redirect({ url, baseUrl }) {
      // Always redirect to the dashboard after sign in
      if (url.startsWith(baseUrl)) {
        return `${baseUrl}/dashboard`;
      }

      // Default NextAuth redirect handling
      if (url.startsWith("/")) return `${baseUrl}${url}`;
      return baseUrl;
    },
  },
  pages: {
    signIn: "/auth/signin",
    error: "/auth/error",
  },
  secret: process.env.NEXTAUTH_SECRET,
};

// Helper function to get the session on the server side
export const getAuthSession = () => getServerSession(authOptions);
