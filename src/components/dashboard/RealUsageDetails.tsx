"use client";

import { useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import { useGetUsageSummaryQuery, useGetUsageQuery, useGetAPIKeysQuery } from "@/lib/api/apiSlice";
import { APIKeySummary, EndpointSummary, TimePeriodSummary, Usage } from "@/types";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { RealUsageCard } from "./RealUsageCard";

export function RealUsageDetails() {
  const [period, setPeriod] = useState<string>("month");
  const [selectedAPIKey, setSelectedAPIKey] = useState<string>("all");

  // Fetch API keys
  const {
    data: apiKeys = [],
    isLoading: isLoadingApiKeys
  } = useGetAPIKeysQuery();

  // Fetch usage summary with the selected period
  const {
    data: usageSummary,
    isLoading: isLoadingUsageSummary
  } = useGetUsageSummaryQuery({ period });

  // Fetch detailed usage with the selected API key filter
  const params: { api_key_id?: string } = {};
  if (selectedAPIKey !== "all") {
    params.api_key_id = selectedAPIKey;
  }

  const {
    data: usage = [],
    isLoading: isLoadingUsage
  } = useGetUsageQuery(params);

  // Format date for display
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString();
  };

  // Determine if we're in a loading state
  const loading = isLoadingApiKeys || isLoadingUsageSummary || isLoadingUsage;

  return (
    <div className="space-y-6">
      <div className="grid gap-6 md:grid-cols-4">
        <div className="md:col-span-1">
          <RealUsageCard period={period} showDetailedView={true} />
        </div>
        
        <div className="md:col-span-3">
          <Card>
            <CardHeader>
              <CardTitle>Usage Analytics</CardTitle>
              <CardDescription>
                View your API usage statistics and trends
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Tabs defaultValue="summary" className="w-full">
                <TabsList className="grid w-full grid-cols-2 mb-6">
                  <TabsTrigger value="summary">Summary</TabsTrigger>
                  <TabsTrigger value="details">Detailed Usage</TabsTrigger>
                </TabsList>

                <TabsContent value="summary">
                  <div className="mb-6">
                    <div className="grid grid-cols-3 gap-2">
                      <button
                        className={`px-3 py-2 text-sm rounded-md ${
                          period === "day"
                            ? "bg-primary text-primary-foreground"
                            : "bg-muted hover:bg-muted/80"
                        }`}
                        onClick={() => setPeriod("day")}
                      >
                        Day
                      </button>
                      <button
                        className={`px-3 py-2 text-sm rounded-md ${
                          period === "week"
                            ? "bg-primary text-primary-foreground"
                            : "bg-muted hover:bg-muted/80"
                        }`}
                        onClick={() => setPeriod("week")}
                      >
                        Week
                      </button>
                      <button
                        className={`px-3 py-2 text-sm rounded-md ${
                          period === "month"
                            ? "bg-primary text-primary-foreground"
                            : "bg-muted hover:bg-muted/80"
                        }`}
                        onClick={() => setPeriod("month")}
                      >
                        Month
                      </button>
                    </div>
                  </div>

                  {loading ? (
                    <div className="space-y-4">
                      <Skeleton className="h-[200px] w-full" />
                      <Skeleton className="h-4 w-3/4" />
                      <Skeleton className="h-4 w-1/2" />
                    </div>
                  ) : (
                    <>
                      {usageSummary && usageSummary.api_keys.length > 0 ? (
                        <div className="space-y-8">
                          {usageSummary.api_keys.map((apiKey: APIKeySummary) => (
                            <div key={apiKey.id} className="space-y-4">
                              <div className="flex items-center justify-between">
                                <h3 className="font-medium">{apiKey.name}</h3>
                                <Badge variant="outline">
                                  {apiKey.total_credits.toLocaleString()} credits
                                </Badge>
                              </div>
                              <div className="space-y-3">
                                {apiKey.endpoints.map((endpoint: EndpointSummary, i: number) => {
                                  // Calculate percentage of this endpoint's usage compared to total for this key
                                  const percentage = apiKey.total_credits > 0
                                    ? (endpoint.total_credits / apiKey.total_credits) * 100
                                    : 0;

                                  return (
                                    <div key={i} className="space-y-1">
                                      <div className="flex items-center justify-between text-sm">
                                        <span className="truncate max-w-[300px] font-mono">
                                          {endpoint.endpoint}
                                        </span>
                                        <span className="text-muted-foreground">
                                          {endpoint.total_credits.toLocaleString()} credits
                                        </span>
                                      </div>
                                      <Progress value={percentage} className="h-1" />
                                    </div>
                                  );
                                })}
                              </div>
                            </div>
                          ))}
                        </div>
                      ) : (
                        <div className="py-8 text-center text-muted-foreground">
                          No usage data available
                        </div>
                      )}
                    </>
                  )}
                </TabsContent>

                <TabsContent value="details">
                  <div className="mb-4">
                    <div className="flex flex-col space-y-2">
                      <label className="text-sm font-medium">Filter by API Key:</label>
                      <Select value={selectedAPIKey} onValueChange={setSelectedAPIKey}>
                        <SelectTrigger className="w-full">
                          <SelectValue placeholder="Select API Key" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="all">All API Keys</SelectItem>
                          {apiKeys.map((key) => (
                            <SelectItem key={key.id} value={key.id}>
                              {key.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  {loading ? (
                    <Skeleton className="h-[300px] w-full" />
                  ) : (
                    <>
                      {usage.length > 0 ? (
                        <div className="rounded-md border">
                          <Table>
                            <TableHeader>
                              <TableRow>
                                <TableHead>Timestamp</TableHead>
                                <TableHead>Endpoint</TableHead>
                                <TableHead>Method</TableHead>
                                <TableHead>Credits</TableHead>
                                <TableHead>Status</TableHead>
                              </TableRow>
                            </TableHeader>
                            <TableBody>
                              {usage.slice(0, 10).map((item: Usage) => (
                                <TableRow key={item.id}>
                                  <TableCell>{formatDate(item.timestamp)}</TableCell>
                                  <TableCell className="font-mono text-xs">
                                    {item.endpoint}
                                  </TableCell>
                                  <TableCell>{item.method}</TableCell>
                                  <TableCell>{item.credits}</TableCell>
                                  <TableCell>
                                    <span
                                      className={`inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium ${
                                        item.success
                                          ? "bg-green-100 text-green-800"
                                          : "bg-red-100 text-red-800"
                                      }`}
                                    >
                                      {item.success ? "Success" : "Failed"}
                                    </span>
                                  </TableCell>
                                </TableRow>
                              ))}
                            </TableBody>
                          </Table>
                          {usage.length > 10 && (
                            <div className="py-2 text-center text-sm text-muted-foreground">
                              Showing 10 of {usage.length} records
                            </div>
                          )}
                        </div>
                      ) : (
                        <div className="py-8 text-center text-muted-foreground">
                          No usage data available
                        </div>
                      )}
                    </>
                  )}
                </TabsContent>
              </Tabs>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
