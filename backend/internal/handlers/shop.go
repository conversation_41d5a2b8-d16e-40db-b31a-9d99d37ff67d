package handlers

import (
	"net/http"
	"regexp"
	"strings"
	"time"

	"github.com/adc-credit/backend/internal/database"
	"github.com/adc-credit/backend/internal/models"
	"github.com/adc-credit/backend/internal/utils"
	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

// GetShops returns all shops for the current user
func GetShops(c *gin.Context) {
	user := c.MustGet("user").(models.User)

	var shops []models.Shop
	if user.Role == "admin" {
		// Admin can see all shops
		if err := database.DB.Find(&shops).Error; err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch shops"})
			return
		}
	} else {
		// Regular users can only see shops they own
		if err := database.DB.Where("owner_user_id = ?", user.ID).Find(&shops).Error; err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch shops"})
			return
		}
	}

	c.<PERSON>(http.StatusOK, shops)
}

// GetShop returns a specific shop by ID
func GetShop(c *gin.Context) {
	user := c.MustGet("user").(models.User)
	shopID := c.Param("id")

	var shop models.Shop
	if user.Role == "admin" {
		// Admin can see any shop
		if err := database.DB.First(&shop, "id = ?", shopID).Error; err != nil {
			c.JSON(http.StatusNotFound, gin.H{"error": "Shop not found"})
			return
		}
	} else {
		// Regular users can only see shops they own
		if err := database.DB.First(&shop, "id = ? AND owner_user_id = ?", shopID, user.ID).Error; err != nil {
			c.JSON(http.StatusNotFound, gin.H{"error": "Shop not found"})
			return
		}
	}

	c.JSON(http.StatusOK, shop)
}

// GetShopBySlug returns a specific shop by slug
func GetShopBySlug(c *gin.Context) {
	user := c.MustGet("user").(models.User)
	shopSlug := c.Param("slug")

	var shop models.Shop
	if user.Role == "admin" {
		// Admin can see any shop
		if err := database.DB.First(&shop, "slug = ?", shopSlug).Error; err != nil {
			c.JSON(http.StatusNotFound, gin.H{"error": "Shop not found"})
			return
		}
	} else {
		// Regular users can only see shops they own
		if err := database.DB.First(&shop, "slug = ? AND owner_user_id = ?", shopSlug, user.ID).Error; err != nil {
			c.JSON(http.StatusNotFound, gin.H{"error": "Shop not found"})
			return
		}
	}

	c.JSON(http.StatusOK, shop)
}

// CreateShop creates a new shop
func CreateShop(c *gin.Context) {
	user := c.MustGet("user").(models.User)

	type CreateShopRequest struct {
		Name         string `json:"name" binding:"required"`
		Description  string `json:"description"`
		ShopType     string `json:"shop_type"` // "retail", "api_service", "enterprise"
		ContactEmail string `json:"contact_email"`
		ContactPhone string `json:"contact_phone"`
	}

	var req CreateShopRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Validate shop type
	validShopTypes := []string{"retail", "api_service", "enterprise"}
	if req.ShopType == "" {
		req.ShopType = "retail" // Default to retail
	}

	isValidType := false
	for _, validType := range validShopTypes {
		if req.ShopType == validType {
			isValidType = true
			break
		}
	}
	if !isValidType {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid shop type. Must be one of: retail, api_service, enterprise"})
		return
	}

	shopID := uuid.New()
	slug := generateShopSlug(req.Name)
	slug = ensureUniqueShopSlug(slug, shopID)

	shop := models.Shop{
		ID:           shopID,
		Slug:         slug,
		Name:         req.Name,
		Description:  req.Description,
		ShopType:     req.ShopType,
		ContactEmail: req.ContactEmail,
		ContactPhone: req.ContactPhone,
		OwnerUserID:  user.ID,
	}

	if err := database.DB.Create(&shop).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create shop"})
		return
	}

	c.JSON(http.StatusCreated, shop)
}

// UpdateShop updates an existing shop
func UpdateShop(c *gin.Context) {
	user := c.MustGet("user").(models.User)
	shopID := c.Param("id")

	var shop models.Shop
	if err := database.DB.First(&shop, "id = ? AND owner_user_id = ?", shopID, user.ID).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Shop not found"})
		return
	}

	type UpdateShopRequest struct {
		Name         string `json:"name"`
		Description  string `json:"description"`
		ShopType     string `json:"shop_type"`
		ContactEmail string `json:"contact_email"`
		ContactPhone string `json:"contact_phone"`
	}

	var req UpdateShopRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Update fields if provided
	if req.Name != "" {
		shop.Name = req.Name
		// Regenerate slug if name changed
		newSlug := generateShopSlug(req.Name)
		if newSlug != shop.Slug {
			shop.Slug = ensureUniqueShopSlug(newSlug, shop.ID)
		}
	}
	if req.Description != "" {
		shop.Description = req.Description
	}
	if req.ShopType != "" {
		// Validate shop type
		validShopTypes := []string{"retail", "api_service", "enterprise"}
		isValidType := false
		for _, validType := range validShopTypes {
			if req.ShopType == validType {
				isValidType = true
				break
			}
		}
		if !isValidType {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid shop type. Must be one of: retail, api_service, enterprise"})
			return
		}
		shop.ShopType = req.ShopType
	}
	if req.ContactEmail != "" {
		shop.ContactEmail = req.ContactEmail
	}
	if req.ContactPhone != "" {
		shop.ContactPhone = req.ContactPhone
	}

	if err := database.DB.Save(&shop).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update shop"})
		return
	}

	c.JSON(http.StatusOK, shop)
}

// DeleteShop deletes a shop
func DeleteShop(c *gin.Context) {
	user := c.MustGet("user").(models.User)
	shopID := c.Param("id")

	var shop models.Shop
	if err := database.DB.First(&shop, "id = ? AND owner_user_id = ?", shopID, user.ID).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Shop not found"})
		return
	}

	if err := database.DB.Delete(&shop).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete shop"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Shop deleted successfully"})
}

// GetShopStats returns statistics for a specific shop
func GetShopStats(c *gin.Context) {
	user := c.MustGet("user").(models.User)
	shopID := c.Param("id")

	// Verify shop ownership
	var shop models.Shop
	if user.Role == "admin" {
		// Admin can see any shop
		if err := database.DB.First(&shop, "id = ?", shopID).Error; err != nil {
			c.JSON(http.StatusNotFound, gin.H{"error": "Shop not found"})
			return
		}
	} else {
		// Regular users can only see shops they own
		if err := database.DB.First(&shop, "id = ? AND owner_user_id = ?", shopID, user.ID).Error; err != nil {
			c.JSON(http.StatusNotFound, gin.H{"error": "Shop not found"})
			return
		}
	}

	// Get total customers
	var totalCustomers int64
	database.DB.Model(&models.ShopCustomer{}).Where("shop_id = ?", shop.ID).Count(&totalCustomers)

	// Get total credit codes generated
	var totalCreditCodes int64
	database.DB.Model(&models.CreditCode{}).Where("shop_id = ?", shop.ID).Count(&totalCreditCodes)

	// Get total credits issued (sum of all credit codes)
	var totalCreditsIssued int64
	database.DB.Model(&models.CreditCode{}).Where("shop_id = ?", shop.ID).Select("COALESCE(SUM(amount), 0)").Scan(&totalCreditsIssued)

	// Get total credits redeemed
	var totalCreditsRedeemed int64
	database.DB.Model(&models.CreditCode{}).Where("shop_id = ? AND is_redeemed = ?", shop.ID, true).Select("COALESCE(SUM(amount), 0)").Scan(&totalCreditsRedeemed)

	// Get active credit codes (not redeemed and not expired)
	var activeCreditCodes int64
	now := time.Now()
	database.DB.Model(&models.CreditCode{}).Where("shop_id = ? AND is_redeemed = ? AND (expires_at IS NULL OR expires_at > ?)", shop.ID, false, now).Count(&activeCreditCodes)

	// Get recent transactions (last 30 days)
	thirtyDaysAgo := now.AddDate(0, 0, -30)
	var recentTransactions int64
	database.DB.Model(&models.ShopCreditTransaction{}).Where("shop_id = ? AND created_at >= ?", shop.ID, thirtyDaysAgo).Count(&recentTransactions)

	// Get total customer credit balance
	var totalCustomerBalance int64
	database.DB.Model(&models.ShopCustomer{}).Where("shop_id = ?", shop.ID).Select("COALESCE(SUM(credit_balance), 0)").Scan(&totalCustomerBalance)

	stats := gin.H{
		"shop_id":                shop.ID,
		"shop_name":              shop.Name,
		"shop_type":              shop.ShopType,
		"total_customers":        totalCustomers,
		"total_credit_codes":     totalCreditCodes,
		"total_credits_issued":   totalCreditsIssued,
		"total_credits_redeemed": totalCreditsRedeemed,
		"active_credit_codes":    activeCreditCodes,
		"recent_transactions":    recentTransactions,
		"total_customer_balance": totalCustomerBalance,
		"redemption_rate":        calculateRedemptionRate(totalCreditsIssued, totalCreditsRedeemed),
	}

	c.JSON(http.StatusOK, stats)
}

// Helper functions

func calculateRedemptionRate(issued, redeemed int64) float64 {
	if issued == 0 {
		return 0.0
	}
	return float64(redeemed) / float64(issued) * 100
}

func generateShopSlug(name string) string {
	// Convert to lowercase
	slug := strings.ToLower(name)

	// Replace spaces and special characters with hyphens
	reg := regexp.MustCompile(`[^a-z0-9]+`)
	slug = reg.ReplaceAllString(slug, "-")

	// Remove leading and trailing hyphens
	slug = strings.Trim(slug, "-")

	// If empty, use a default
	if slug == "" {
		slug = "shop"
	}

	return slug
}

func ensureUniqueShopSlug(baseSlug string, excludeID uuid.UUID) string {
	return utils.EnsureUniqueSlug(baseSlug, func(s string) bool {
		var count int64
		database.DB.Model(&models.Shop{}).Where("slug = ? AND id != ?", s, excludeID).Count(&count)
		return count > 0
	})
}
