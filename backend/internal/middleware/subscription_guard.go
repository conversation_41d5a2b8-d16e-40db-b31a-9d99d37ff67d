package middleware

import (
	"net/http"
	"strconv"

	"github.com/adc-credit/backend/internal/services"
	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

// SubscriptionGuardMiddleware provides middleware for subscription limit enforcement
type SubscriptionGuardMiddleware struct {
	guard *services.SubscriptionGuard
}

// NewSubscriptionGuardMiddleware creates a new subscription guard middleware
func NewSubscriptionGuardMiddleware() *SubscriptionGuardMiddleware {
	return &SubscriptionGuardMiddleware{
		guard: services.NewSubscriptionGuard(),
	}
}

// RequireShopLimit middleware to check shop creation limits
func (sgm *SubscriptionGuardMiddleware) RequireShopLimit() gin.HandlerFunc {
	return func(c *gin.Context) {
		userID, exists := c.Get("user_id")
		if !exists {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
			c.Abort()
			return
		}

		userUUID, ok := userID.(uuid.UUID)
		if !ok {
			c.JSO<PERSON>(http.StatusBadRequest, gin.H{"error": "Invalid user ID"})
			c.Abort()
			return
		}

		result, err := sgm.guard.CheckShopLimit(userUUID)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to check shop limit"})
			c.Abort()
			return
		}

		if !result.Allowed {
			c.JSON(http.StatusForbidden, gin.H{
				"error":   "Shop limit exceeded",
				"message": result.Message,
				"limit":   result.Limit,
				"current": result.CurrentUsage,
			})
			c.Abort()
			return
		}

		c.Next()
	}
}

// RequireCustomerLimit middleware to check customer creation limits
func (sgm *SubscriptionGuardMiddleware) RequireCustomerLimit() gin.HandlerFunc {
	return func(c *gin.Context) {
		shopIDStr := c.Param("shopId")
		if shopIDStr == "" {
			shopIDStr = c.Query("shop_id")
		}

		if shopIDStr == "" {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Shop ID is required"})
			c.Abort()
			return
		}

		shopID, err := uuid.Parse(shopIDStr)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid shop ID"})
			c.Abort()
			return
		}

		result, err := sgm.guard.CheckCustomerLimit(shopID)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to check customer limit"})
			c.Abort()
			return
		}

		if !result.Allowed {
			c.JSON(http.StatusForbidden, gin.H{
				"error":   "Customer limit exceeded",
				"message": result.Message,
				"limit":   result.Limit,
				"current": result.CurrentUsage,
			})
			c.Abort()
			return
		}

		c.Next()
	}
}

// RequireAPIKeyLimit middleware to check API key creation limits
func (sgm *SubscriptionGuardMiddleware) RequireAPIKeyLimit() gin.HandlerFunc {
	return func(c *gin.Context) {
		shopIDStr := c.Param("shopId")
		if shopIDStr == "" {
			shopIDStr = c.Query("shop_id")
		}

		if shopIDStr == "" {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Shop ID is required"})
			c.Abort()
			return
		}

		shopID, err := uuid.Parse(shopIDStr)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid shop ID"})
			c.Abort()
			return
		}

		result, err := sgm.guard.CheckAPIKeyLimit(shopID)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to check API key limit"})
			c.Abort()
			return
		}

		if !result.Allowed {
			c.JSON(http.StatusForbidden, gin.H{
				"error":   "API key limit exceeded",
				"message": result.Message,
				"limit":   result.Limit,
				"current": result.CurrentUsage,
			})
			c.Abort()
			return
		}

		c.Next()
	}
}

// RequireBranchLimit middleware to check branch creation limits
func (sgm *SubscriptionGuardMiddleware) RequireBranchLimit() gin.HandlerFunc {
	return func(c *gin.Context) {
		shopIDStr := c.Param("shopId")
		if shopIDStr == "" {
			shopIDStr = c.Query("shop_id")
		}

		if shopIDStr == "" {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Shop ID is required"})
			c.Abort()
			return
		}

		shopID, err := uuid.Parse(shopIDStr)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid shop ID"})
			c.Abort()
			return
		}

		result, err := sgm.guard.CheckBranchLimit(shopID)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to check branch limit"})
			c.Abort()
			return
		}

		if !result.Allowed {
			c.JSON(http.StatusForbidden, gin.H{
				"error":   "Branch limit exceeded",
				"message": result.Message,
				"limit":   result.Limit,
				"current": result.CurrentUsage,
			})
			c.Abort()
			return
		}

		c.Next()
	}
}

// RequireQRCodeLimit middleware to check QR code generation limits
func (sgm *SubscriptionGuardMiddleware) RequireQRCodeLimit() gin.HandlerFunc {
	return func(c *gin.Context) {
		userID, exists := c.Get("user_id")
		if !exists {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
			c.Abort()
			return
		}

		userUUID, ok := userID.(uuid.UUID)
		if !ok {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid user ID"})
			c.Abort()
			return
		}

		result, err := sgm.guard.CheckQRCodeLimit(userUUID)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to check QR code limit"})
			c.Abort()
			return
		}

		if !result.Allowed {
			c.JSON(http.StatusForbidden, gin.H{
				"error":   "QR code limit exceeded",
				"message": result.Message,
				"limit":   result.Limit,
				"current": result.CurrentUsage,
			})
			c.Abort()
			return
		}

		c.Next()
	}
}

// RequireWebhookLimit middleware to check webhook creation limits
func (sgm *SubscriptionGuardMiddleware) RequireWebhookLimit() gin.HandlerFunc {
	return func(c *gin.Context) {
		userID, exists := c.Get("user_id")
		if !exists {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
			c.Abort()
			return
		}

		userUUID, ok := userID.(uuid.UUID)
		if !ok {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid user ID"})
			c.Abort()
			return
		}

		result, err := sgm.guard.CheckWebhookLimit(userUUID)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to check webhook limit"})
			c.Abort()
			return
		}

		if !result.Allowed {
			c.JSON(http.StatusForbidden, gin.H{
				"error":   "Webhook limit exceeded",
				"message": result.Message,
				"limit":   result.Limit,
				"current": result.CurrentUsage,
			})
			c.Abort()
			return
		}

		c.Next()
	}
}

// RequireShopType middleware to check if shop type is allowed
func (sgm *SubscriptionGuardMiddleware) RequireShopType() gin.HandlerFunc {
	return func(c *gin.Context) {
		userID, exists := c.Get("user_id")
		if !exists {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
			c.Abort()
			return
		}

		userUUID, ok := userID.(uuid.UUID)
		if !ok {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid user ID"})
			c.Abort()
			return
		}

		// Get shop type from request body or query
		var shopType string
		if c.Request.Method == "POST" || c.Request.Method == "PUT" {
			var body map[string]interface{}
			if err := c.ShouldBindJSON(&body); err == nil {
				if st, ok := body["shop_type"].(string); ok {
					shopType = st
				}
			}
		} else {
			shopType = c.Query("shop_type")
		}

		if shopType == "" {
			shopType = "retail" // Default to retail if not specified
		}

		result, err := sgm.guard.CheckShopTypeAllowed(userUUID, shopType)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to check shop type permission"})
			c.Abort()
			return
		}

		if !result.Allowed {
			c.JSON(http.StatusForbidden, gin.H{
				"error":   "Shop type not allowed",
				"message": result.Message,
			})
			c.Abort()
			return
		}

		c.Next()
	}
}

// RequireCredits middleware to check if user has enough credits
func (sgm *SubscriptionGuardMiddleware) RequireCredits(requiredCredits int) gin.HandlerFunc {
	return func(c *gin.Context) {
		userID, exists := c.Get("user_id")
		if !exists {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
			c.Abort()
			return
		}

		userUUID, ok := userID.(uuid.UUID)
		if !ok {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid user ID"})
			c.Abort()
			return
		}

		// Allow dynamic credit requirement from request
		credits := requiredCredits
		if creditsStr := c.Query("credits"); creditsStr != "" {
			if parsedCredits, err := strconv.Atoi(creditsStr); err == nil {
				credits = parsedCredits
			}
		}

		result, err := sgm.guard.CheckCreditBalance(userUUID, credits)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to check credit balance"})
			c.Abort()
			return
		}

		if !result.Allowed {
			c.JSON(http.StatusForbidden, gin.H{
				"error":   "Insufficient credits",
				"message": result.Message,
			})
			c.Abort()
			return
		}

		c.Next()
	}
}
