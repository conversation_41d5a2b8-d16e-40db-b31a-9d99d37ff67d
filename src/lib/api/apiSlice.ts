import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react';
import {
  APIKey,
  Subscription,
  Usage,
  UsageSummary,
  Webhook,
  WebhookDelivery,
  AnalyticsSummary,
  AnalyticsTrend,
  EndpointAnalytics,
  PerformanceMetrics,
  MerchantShop,
  ShopCustomer,
  CreditCode,
  SubscriptionTier,
} from '@/types';
import { User } from 'next-auth';

// Ensure API_URL always has the /api/v1 prefix
const BASE_URL = process.env.NEXT_PUBLIC_BACKEND_URL || 'http://localhost:8400';
const API_URL = BASE_URL.endsWith('/api/v1') ? BASE_URL : `${BASE_URL}/api/v1`;

// Custom fetch function that includes credentials and handles CORS
const customFetch = async (url: string, options: RequestInit = {}) => {
  // Get token from session storage
  const token = sessionStorage.getItem('accessToken');

  console.log(`API Fetch: Getting token from sessionStorage:`, token ? "Token found" : "No token found");

  // If no token in sessionStorage, try to get it from localStorage as fallback
  const localStorageToken = localStorage.getItem('accessToken');
  if (!token && localStorageToken) {
    console.log(`API Fetch: Using token from localStorage instead`);
    sessionStorage.setItem('accessToken', localStorageToken);
  }

  // Final token to use
  const finalToken = token || localStorageToken;

  // If we still don't have a token, try to exchange the Google token
  if (!finalToken) {
    console.log(`API Fetch: No token found, trying to exchange Google token`);
    try {
      // Get the Google token from session
      const session = await fetch('/api/auth/session').then(res => res.json());
      if (session?.accessToken) {
        console.log(`API Fetch: Found token in session:`, session.accessToken.substring(0, 20) + '...');

        // Check if this is already a JWT token (starts with "eyJ")
        if (session.accessToken.startsWith('eyJ')) {
          console.log(`API Fetch: Token appears to be a JWT token, using directly`);

          // Store the JWT token
          sessionStorage.setItem('accessToken', session.accessToken);
          localStorage.setItem('accessToken', session.accessToken);

          // Use this token for the current request
          return customFetch(url, options);
        }

        // If it's a Google token, exchange it
        console.log(`API Fetch: Token appears to be a Google token, exchanging it`);

        // Exchange the Google token for a backend JWT token
        const response = await fetch(`${process.env.NEXT_PUBLIC_BACKEND_URL}/api/v1/auth/google`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ token: session.accessToken }),
        });

        if (response.ok) {
          const data = await response.json();
          console.log(`API Fetch: Successfully exchanged Google token`);

          // Store the backend JWT token
          sessionStorage.setItem('accessToken', data.token);
          localStorage.setItem('accessToken', data.token);

          // Use this token for the current request
          return customFetch(url, options);
        }
      }
    } catch (error) {
      console.error(`API Fetch: Error exchanging Google token:`, error);
    }
  }

  // Ensure credentials are included
  const fetchOptions: RequestInit = {
    ...options,
    credentials: 'include', // Always include cookies
    headers: {
      'Content-Type': 'application/json',
      ...(finalToken ? { 'Authorization': `Bearer ${finalToken}` } : {}),
      ...options.headers,
    },
  };

  console.log(`API Fetch: Fetching ${url} with options:`, {
    method: fetchOptions.method || 'GET',
    headers: fetchOptions.headers,
    credentials: fetchOptions.credentials,
  });

  try {
    // Use the native fetch with our enhanced options
    const response = await fetch(url, fetchOptions);

    // Log response status
    console.log(`API Fetch: Response from ${url}:`, {
      status: response.status,
      statusText: response.statusText,
    });

    // Clone the response to log its content without consuming it
    const clonedResponse = response.clone();
    try {
      const data = await clonedResponse.text();
      console.log(`API Fetch: Response data from ${url}:`, data.substring(0, 200) + (data.length > 200 ? '...' : ''));
    } catch (error) {
      console.error(`API Fetch: Error reading response from ${url}:`, error);
    }

    return response;
  } catch (error) {
    console.error(`API Fetch: Error for ${url}:`, error);
    throw error;
  }
};

export const apiSlice = createApi({
  reducerPath: 'api',
  baseQuery: fetchBaseQuery({
    baseUrl: API_URL,
    fetchFn: customFetch, // Use our custom fetch function
    prepareHeaders: async (headers) => {
      // Get token from session storage
      const token = sessionStorage.getItem('accessToken');

      // If no token in sessionStorage, try to get it from localStorage as fallback
      const localStorageToken = localStorage.getItem('accessToken');
      if (!token && localStorageToken) {
        console.log('API Slice: Using token from localStorage instead');
        sessionStorage.setItem('accessToken', localStorageToken);
      }

      // Final token to use
      const finalToken = token || localStorageToken;

      if (finalToken) {
        headers.set('Authorization', `Bearer ${finalToken}`);
        console.log('API Slice: Added Authorization header with token');
      } else {
        console.log('API Slice: No token found in storage');
      }

      // Log all headers for debugging
      console.log('API Slice: All headers:', Object.fromEntries(headers.entries()));

      return headers;
    },
    credentials: 'include', // Belt and suspenders - ensure cookies are included
  }),
  tagTypes: [
    'Credits',
    'APIKeys',
    'Usage',
    'Webhooks',
    'WebhookDeliveries',
    'Analytics',
    'AnalyticsSummary',
    'AnalyticsTrends',
    'EndpointAnalytics',
    'PerformanceMetrics',
    'User',
    'Organizations',
    'Branches',
    'ExternalUsers',
    'Subscriptions',
    'Shops',
    'ShopCustomers',
    'CreditCodes',
    'ShopCreditTransactions',
    'ShopStats',
    'ShopAPIKeys',
    'ShopBranchAPIKeys'
  ],
  endpoints: (builder) => ({
    // Subscription endpoints
    getSubscriptionTiers: builder.query<SubscriptionTier[], void>({
      query: () => '/subscriptions/tiers',
      providesTags: ['Subscriptions'],
    }),

    getSubscriptions: builder.query<Subscription[], void>({
      query: () => '/subscriptions',
      providesTags: ['Subscriptions'],
    }),

    getActiveSubscription: builder.query<Subscription, { type?: string; shop_id?: string; shop_customer_id?: string }>({
      query: (params) => {
        const queryParams = new URLSearchParams();
        if (params?.type) queryParams.append('type', params.type);
        if (params?.shop_id) queryParams.append('shop_id', params.shop_id);
        if (params?.shop_customer_id) queryParams.append('shop_customer_id', params.shop_customer_id);

        const query = queryParams.toString() ? `?${queryParams.toString()}` : '';
        return `/subscriptions/active${query}`;
      },
      providesTags: ['Subscriptions'],
    }),

    createSubscription: builder.mutation<
      Subscription,
      {
        subscription_tier_id: number;
        auto_renew?: boolean;
        subscription_type?: string;
        shop_id?: string;
        shop_customer_id?: string;
      }
    >({
      query: (data) => ({
        url: '/subscriptions',
        method: 'POST',
        body: data,
      }),
      invalidatesTags: ['Subscriptions', 'Credits'],
    }),

    updateSubscription: builder.mutation<Subscription, { id: string; auto_renew: boolean }>({
      query: ({ id, ...data }) => ({
        url: `/subscriptions/${id}`,
        method: 'PUT',
        body: data,
      }),
      invalidatesTags: ['Subscriptions'],
    }),

    cancelSubscription: builder.mutation<{ message: string }, string>({
      query: (id) => ({
        url: `/subscriptions/${id}`,
        method: 'DELETE',
      }),
      invalidatesTags: ['Subscriptions', 'Credits'],
    }),
    getCreditBalance: builder.query<{
      credit_balance: number;
      credit_limit: number;
      subscription: Subscription;
    }, void>({
      query: () => '/credits',
      providesTags: ['Credits'],
    }),

    getTransactions: builder.query<Transaction[], void>({
      query: () => '/credits/transactions',
      providesTags: ['Credits'],
    }),

    getNextScheduledCreditDate: builder.query<{ next_scheduled_credit_date: string }, void>({
      query: () => '/credits/scheduled/next',
      providesTags: ['Credits'],
    }),

    getScheduledCreditHistory: builder.query<Transaction[], void>({
      query: () => '/credits/scheduled/history',
      providesTags: ['Credits'],
    }),

    processScheduledCredits: builder.mutation<{ message: string; count: number }, void>({
      query: () => ({
        url: '/admin/credits/scheduled/process',
        method: 'POST',
      }),
      invalidatesTags: ['Credits'],
    }),

    manuallyAddScheduledCredits: builder.mutation<
      { message: string; user_id: string; credit_balance: number },
      string
    >({
      query: (userId) => ({
        url: '/admin/credits/scheduled/manual',
        method: 'POST',
        body: { user_id: userId },
      }),
      invalidatesTags: ['Credits'],
    }),

    getAPIKeys: builder.query<APIKey[], void>({
      query: () => '/apikeys',
      providesTags: ['APIKeys'],
    }),

    createAPIKey: builder.mutation<APIKey, { name: string; permissions?: string[] }>({
      query: (data) => ({
        url: '/apikeys',
        method: 'POST',
        body: data,
      }),
      invalidatesTags: ['APIKeys'],
    }),

    updateAPIKey: builder.mutation<APIKey, { id: string; name?: string; enabled?: boolean; permissions?: string[] }>({
      query: ({ id, ...data }) => ({
        url: `/apikeys/${id}`,
        method: 'PUT',
        body: data,
      }),
      invalidatesTags: ['APIKeys'],
    }),

    deleteAPIKey: builder.mutation<{ message: string }, string>({
      query: (id) => ({
        url: `/apikeys/${id}`,
        method: 'DELETE',
      }),
      invalidatesTags: ['APIKeys'],
    }),

    getUsageSummary: builder.query<UsageSummary, { period?: string; start_date?: string; end_date?: string }>({
      query: (params) => {
        const queryParams = new URLSearchParams();
        if (params?.period) queryParams.append('period', params.period);
        if (params?.start_date) queryParams.append('start_date', params.start_date);
        if (params?.end_date) queryParams.append('end_date', params.end_date);

        const query = queryParams.toString() ? `?${queryParams.toString()}` : '';
        return `/usage/summary${query}`;
      },
      providesTags: ['Usage'],
    }),

    getUsage: builder.query<Usage[], { start_date?: string; end_date?: string; api_key_id?: string }>({
      query: (params) => {
        const queryParams = new URLSearchParams();
        if (params?.start_date) queryParams.append('start_date', params.start_date);
        if (params?.end_date) queryParams.append('end_date', params.end_date);
        if (params?.api_key_id) queryParams.append('api_key_id', params.api_key_id);

        const query = queryParams.toString() ? `?${queryParams.toString()}` : '';
        return `/usage${query}`;
      },
      providesTags: ['Usage'],
    }),

    // Webhook endpoints
    getWebhooks: builder.query<Webhook[], void>({
      query: () => '/webhooks',
      providesTags: ['Webhooks'],
    }),

    getWebhook: builder.query<Webhook, string>({
      query: (id) => `/webhooks/${id}`,
      providesTags: (result, error, id) => [{ type: 'Webhooks', id }],
    }),

    createWebhook: builder.mutation<
      Webhook,
      { name: string; url: string; secret?: string; events: string[] }
    >({
      query: (data) => ({
        url: '/webhooks',
        method: 'POST',
        body: data,
      }),
      invalidatesTags: ['Webhooks'],
    }),

    updateWebhook: builder.mutation<
      Webhook,
      { id: string; data: { name?: string; url?: string; secret?: string; events?: string[]; active?: boolean } }
    >({
      query: ({ id, data }) => ({
        url: `/webhooks/${id}`,
        method: 'PUT',
        body: data,
      }),
      invalidatesTags: (result, error, { id }) => [
        'Webhooks',
        { type: 'Webhooks', id }
      ],
    }),

    deleteWebhook: builder.mutation<{ message: string }, string>({
      query: (id) => ({
        url: `/webhooks/${id}`,
        method: 'DELETE',
      }),
      invalidatesTags: ['Webhooks'],
    }),

    getWebhookDeliveries: builder.query<WebhookDelivery[], string>({
      query: (id) => `/webhooks/${id}/deliveries`,
      providesTags: (result, error, id) => [{ type: 'WebhookDeliveries', id }],
    }),

    // Analytics endpoints
    getAnalyticsSummary: builder.query<AnalyticsSummary, { start_date?: string; end_date?: string }>({
      query: (params) => {
        const queryParams = new URLSearchParams();
        if (params?.start_date) queryParams.append('start_date', params.start_date);
        if (params?.end_date) queryParams.append('end_date', params.end_date);

        const query = queryParams.toString() ? `?${queryParams.toString()}` : '';
        return `/analytics/summary${query}`;
      },
      providesTags: ['AnalyticsSummary'],
    }),

    getAnalyticsTrends: builder.query<AnalyticsTrend[], { interval?: string; start_date?: string; end_date?: string }>({
      query: (params) => {
        const queryParams = new URLSearchParams();
        if (params?.interval) queryParams.append('interval', params.interval);
        if (params?.start_date) queryParams.append('start_date', params.start_date);
        if (params?.end_date) queryParams.append('end_date', params.end_date);

        const query = queryParams.toString() ? `?${queryParams.toString()}` : '';
        return `/analytics/trends${query}`;
      },
      providesTags: ['AnalyticsTrends'],
    }),

    getEndpointAnalytics: builder.query<EndpointAnalytics[], { start_date?: string; end_date?: string }>({
      query: (params) => {
        const queryParams = new URLSearchParams();
        if (params?.start_date) queryParams.append('start_date', params.start_date);
        if (params?.end_date) queryParams.append('end_date', params.end_date);

        const query = queryParams.toString() ? `?${queryParams.toString()}` : '';
        return `/analytics/endpoints${query}`;
      },
      providesTags: ['EndpointAnalytics'],
    }),

    getPerformanceMetrics: builder.query<PerformanceMetrics, { metric?: string; start_date?: string; end_date?: string }>({
      query: (params) => {
        const queryParams = new URLSearchParams();
        if (params?.metric) queryParams.append('metric', params.metric);
        if (params?.start_date) queryParams.append('start_date', params.start_date);
        if (params?.end_date) queryParams.append('end_date', params.end_date);

        const query = queryParams.toString() ? `?${queryParams.toString()}` : '';
        return `/analytics/performance${query}`;
      },
      providesTags: ['PerformanceMetrics'],
    }),

    // User endpoints
    getCurrentUser: builder.query<User, void>({
      query: () => `/users/me`,
      providesTags: ['User'],
    }),

    updateUser: builder.mutation<User, { name?: string; picture?: string }>({
      query: (data) => ({
        url: `/users/me`,
        method: 'PUT',
        body: data,
      }),
      invalidatesTags: ['User'],
    }),

    // Organization endpoints
    getOrganizations: builder.query<any[], void>({
      query: () => `/organizations`,
      providesTags: ['Organizations'],
    }),

    getOrganization: builder.query<any, string>({
      query: (slug) => `/organizations/${slug}`,
      providesTags: (result, error, slug) => [{ type: 'Organizations', id: result?.id }],
    }),

    // Legacy endpoint for backward compatibility
    getOrganizationById: builder.query<any, string>({
      query: (id) => `/organizations/id/${id}`,
      providesTags: (result, error, id) => [{ type: 'Organizations', id }],
    }),

    createOrganization: builder.mutation<any, { name: string; description?: string }>({
      query: (data) => ({
        url: `/organizations`,
        method: 'POST',
        body: data,
      }),
      invalidatesTags: ['Organizations'],
    }),

    updateOrganization: builder.mutation<any, { slug: string; name?: string; description?: string }>({
      query: ({ slug, ...data }) => ({
        url: `/organizations/${slug}`,
        method: 'PUT',
        body: data,
      }),
      invalidatesTags: (result, error, { slug }) => [
        'Organizations',
        { type: 'Organizations', id: result?.id }
      ],
    }),

    deleteOrganization: builder.mutation<{ message: string }, string>({
      query: (slug) => ({
        url: `/organizations/${slug}`,
        method: 'DELETE',
      }),
      invalidatesTags: ['Organizations'],
    }),

    // Branch endpoints
    getBranches: builder.query<any[], string>({
      query: (organizationId) => `/org-branches?organization_id=${organizationId}`,
      providesTags: ['Branches'],
    }),

    getBranch: builder.query<any, { id: string; organizationId: string }>({
      query: ({ id, organizationId }) => `/org-branches/${id}?organization_id=${organizationId}`,
      providesTags: (result, error, { id }) => [{ type: 'Branches', id }],
    }),

    createBranch: builder.mutation<any, { organizationId: string; name: string; description?: string }>({
      query: ({ organizationId, ...data }) => ({
        url: `/org-branches?organization_id=${organizationId}`,
        method: 'POST',
        body: data,
      }),
      invalidatesTags: ['Branches'],
    }),

    updateBranch: builder.mutation<any, { id: string; organizationId: string; name?: string; description?: string }>({
      query: ({ id, organizationId, ...data }) => ({
        url: `/org-branches/${id}?organization_id=${organizationId}`,
        method: 'PUT',
        body: data,
      }),
      invalidatesTags: (result, error, { id }) => [
        'Branches',
        { type: 'Branches', id }
      ],
    }),

    deleteBranch: builder.mutation<{ message: string }, { id: string; organizationId: string }>({
      query: ({ id, organizationId }) => ({
        url: `/org-branches/${id}?organization_id=${organizationId}`,
        method: 'DELETE',
      }),
      invalidatesTags: ['Branches'],
    }),

    // External User endpoints
    getExternalUsers: builder.query<any[], { organizationId: string; branchId: string }>({
      query: ({ organizationId, branchId }) =>
        `/org-users?organization_id=${organizationId}&branch_id=${branchId}`,
      providesTags: ['ExternalUsers'],
    }),

    // Unified Shop endpoints
    getShops: builder.query<Shop[], void>({
      query: () => '/shops',
      providesTags: ['Shops'],
    }),

    getShop: builder.query<Shop, string>({
      query: (id) => `/shops/${id}`,
      providesTags: (result, error, id) => [{ type: 'Shops', id }],
    }),

    getShopBySlug: builder.query<Shop, string>({
      query: (slug) => `/shops/slug/${slug}`,
      providesTags: (result, error, slug) => [{ type: 'Shops', id: result?.id }],
    }),

    createShop: builder.mutation<Shop, {
      name: string;
      description?: string;
      shop_type?: 'retail' | 'api_service' | 'enterprise';
      contact_email?: string;
      contact_phone?: string;
    }>({
      query: (data) => ({
        url: '/shops',
        method: 'POST',
        body: data,
      }),
      invalidatesTags: ['Shops'],
    }),

    updateShop: builder.mutation<Shop, {
      id: string;
      name?: string;
      description?: string;
      shop_type?: 'retail' | 'api_service' | 'enterprise';
      contact_email?: string;
      contact_phone?: string;
    }>({
      query: ({ id, ...data }) => ({
        url: `/shops/${id}`,
        method: 'PUT',
        body: data,
      }),
      invalidatesTags: (result, error, { id }) => [
        'Shops',
        { type: 'Shops', id }
      ],
    }),

    deleteShop: builder.mutation<{ message: string }, string>({
      query: (id) => ({
        url: `/shops/${id}`,
        method: 'DELETE',
      }),
      invalidatesTags: ['Shops'],
    }),



    // Shop Customer endpoints
    getShopCustomers: builder.query<{
      customers: ShopCustomer[];
      total: number;
      page: number;
      limit: number;
      totalPages: number;
    }, {
      shopId: string;
      search?: string;
      page?: number;
      limit?: number;
      sortBy?: string;
      sortOrder?: 'asc' | 'desc';
      minCreditBalance?: number;
      maxCreditBalance?: number;
    }>({
      query: ({ shopId, search, page = 1, limit = 10, sortBy, sortOrder, minCreditBalance, maxCreditBalance }) => {
        const params = new URLSearchParams();
        if (search) params.append('search', search);
        if (page) params.append('page', page.toString());
        if (limit) params.append('limit', limit.toString());
        if (sortBy) params.append('sortBy', sortBy);
        if (sortOrder) params.append('sortOrder', sortOrder);
        if (minCreditBalance !== undefined) params.append('minCreditBalance', minCreditBalance.toString());
        if (maxCreditBalance !== undefined) params.append('maxCreditBalance', maxCreditBalance.toString());

        const queryString = params.toString();
        return `/shops/${shopId}/customers${queryString ? `?${queryString}` : ''}`;
      },
      providesTags: (result, error, { shopId }) => [{ type: 'ShopCustomers', id: shopId }],
    }),

    addShopCustomer: builder.mutation<ShopCustomer, {
      shopId: string;
      email: string;
      name: string;
      phone?: string;
    }>({
      query: ({ shopId, ...data }) => ({
        url: `/shops/${shopId}/customers`,
        method: 'POST',
        body: data,
      }),
      invalidatesTags: (result, error, { shopId }) => [{ type: 'ShopCustomers', id: shopId }],
    }),

    addShopCredit: builder.mutation<
      { message: string; credit_balance: number; transaction: any },
      { shopId: string; customerId: string; amount: number; description?: string }
    >({
      query: ({ shopId, customerId, ...data }) => ({
        url: `/shops/${shopId}/customers/${customerId}/credits`,
        method: 'POST',
        body: data,
      }),
      invalidatesTags: (result, error, { shopId }) => [{ type: 'ShopCustomers', id: shopId }],
    }),

    // Generate QR code for an existing credit code
    generateQRCode: builder.mutation<{ qr_code: string }, { shopId: string; code: string; amount: number }>({
      query: ({ shopId, code, amount }) => ({
        url: `/shops/${shopId}/credit-codes/qr`,
        method: 'POST',
        body: { code, amount },
      }),
    }),

    // Credit Code endpoints
    getShopTransactions: builder.query<any[], string>({
      query: (shopId) => `/shops/${shopId}/transactions`,
      providesTags: ['ShopCreditTransactions'],
    }),

    getCreditCodes: builder.query<CreditCode[], string>({
      query: (shopId) => `/shops/${shopId}/credit-codes`,
      providesTags: ['CreditCodes'],
    }),

    generateCreditCode: builder.mutation<
      { credit_code: CreditCode; qr_code: string },
      { shopId: string; amount: number; description?: string; expiresIn?: number }
    >({
      query: ({ shopId, ...data }) => ({
        url: `/shops/${shopId}/credit-codes`,
        method: 'POST',
        body: data,
      }),
      invalidatesTags: ['CreditCodes'],
    }),

    // Customer endpoints
    getCustomerShops: builder.query<Shop[], void>({
      query: () => '/customer/shops',
      providesTags: ['Shops'],
    }),

    getCustomerShop: builder.query<{ shop: Shop; credit_balance: number }, string>({
      query: (shopId) => `/customer/shops/${shopId}`,
      providesTags: (result, error, id) => [{ type: 'Shops', id }],
    }),

    getCustomerTransactions: builder.query<any[], string>({
      query: (shopId) => `/customer/shops/${shopId}/transactions`,
      providesTags: ['ShopCreditTransactions'],
    }),

    useShopCredit: builder.mutation<
      { message: string; credit_balance: number; transaction: any },
      { shopId: string; amount: number; description?: string }
    >({
      query: ({ shopId, ...data }) => ({
        url: `/customer/shops/${shopId}/use-credit`,
        method: 'POST',
        body: data,
      }),
      invalidatesTags: ['ShopCreditTransactions', 'Shops'],
    }),

    redeemCreditCode: builder.mutation<
      { message: string; credit_balance: number; shop_name: string; transaction: any },
      { code: string }
    >({
      query: (data) => ({
        url: '/customer/redeem-code',
        method: 'POST',
        body: data,
      }),
      invalidatesTags: ['ShopCreditTransactions', 'Shops'],
    }),





    getExternalUser: builder.query<any, { id: string; organizationId: string; branchId: string }>({
      query: ({ id, organizationId, branchId }) =>
        `/org-users/${id}?organization_id=${organizationId}&branch_id=${branchId}`,
      providesTags: (result, error, { id }) => [{ type: 'ExternalUsers', id }],
    }),

    createExternalUser: builder.mutation<any, {
      organizationId: string;
      branchId: string;
      name: string;
      email: string;
      external_user_identifier: string;
      monthly_credits: number;
    }>({
      query: ({ organizationId, branchId, ...data }) => ({
        url: `/org-users?organization_id=${organizationId}&branch_id=${branchId}`,
        method: 'POST',
        body: data,
      }),
      invalidatesTags: ['ExternalUsers'],
    }),

    updateExternalUser: builder.mutation<any, {
      id: string;
      organizationId: string;
      branchId: string;
      name?: string;
      monthly_credits?: number;
    }>({
      query: ({ id, organizationId, branchId, ...data }) => ({
        url: `/org-users/${id}?organization_id=${organizationId}&branch_id=${branchId}`,
        method: 'PUT',
        body: data,
      }),
      invalidatesTags: (result, error, { id }) => [
        'ExternalUsers',
        { type: 'ExternalUsers', id }
      ],
    }),

    deleteExternalUser: builder.mutation<{ message: string }, { id: string; organizationId: string; branchId: string }>({
      query: ({ id, organizationId, branchId }) => ({
        url: `/org-users/${id}?organization_id=${organizationId}&branch_id=${branchId}`,
        method: 'DELETE',
      }),
      invalidatesTags: ['ExternalUsers'],
    }),

    addCreditsToExternalUser: builder.mutation<any, {
      id: string;
      organizationId: string;
      branchId: string;
      amount: number;
      description?: string;
    }>({
      query: ({ id, organizationId, branchId, ...data }) => ({
        url: `/org-users/${id}/credits/add?organization_id=${organizationId}&branch_id=${branchId}`,
        method: 'POST',
        body: data,
      }),
      invalidatesTags: (result, error, { id }) => [
        'ExternalUsers',
        { type: 'ExternalUsers', id }
      ],
    }),

    reduceCreditsFromExternalUser: builder.mutation<any, {
      id: string;
      organizationId: string;
      branchId: string;
      amount: number;
      description?: string;
    }>({
      query: ({ id, organizationId, branchId, ...data }) => ({
        url: `/org-users/${id}/credits/reduce?organization_id=${organizationId}&branch_id=${branchId}`,
        method: 'POST',
        body: data,
      }),
      invalidatesTags: (result, error, { id }) => [
        'ExternalUsers',
        { type: 'ExternalUsers', id }
      ],
    }),

    // Shop Statistics endpoints
    getShopStats: builder.query<{
      shop_id: string;
      shop_name: string;
      shop_type: string;
      total_customers: number;
      total_credit_codes: number;
      total_credits_issued: number;
      total_credits_redeemed: number;
      active_credit_codes: number;
      recent_transactions: number;
      total_customer_balance: number;
      redemption_rate: number;
    }, string>({
      query: (shopId) => `/shops/${shopId}/stats`,
      providesTags: (result, error, shopId) => [
        { type: 'ShopStats', id: shopId },
        'ShopStats'
      ],
    }),

    // Shop API Key Management endpoints
    getShopAPIKeys: builder.query<APIKey[], string>({
      query: (shopId) => `/shops/${shopId}/apikeys`,
      providesTags: (result, error, shopId) => [
        { type: 'ShopAPIKeys', id: shopId },
        'ShopAPIKeys'
      ],
    }),

    createShopAPIKey: builder.mutation<APIKey, {
      shopId: string;
      name: string;
      permissions?: string[];
    }>({
      query: ({ shopId, ...data }) => ({
        url: `/shops/${shopId}/apikeys`,
        method: 'POST',
        body: data,
      }),
      invalidatesTags: (result, error, { shopId }) => [
        { type: 'ShopAPIKeys', id: shopId },
        'ShopAPIKeys'
      ],
    }),

    getShopAPIKey: builder.query<APIKey, { shopId: string; keyId: string }>({
      query: ({ shopId, keyId }) => `/shops/${shopId}/apikeys/${keyId}`,
      providesTags: (result, error, { shopId, keyId }) => [
        { type: 'ShopAPIKeys', id: `${shopId}-${keyId}` }
      ],
    }),

    updateShopAPIKey: builder.mutation<APIKey, {
      shopId: string;
      keyId: string;
      name?: string;
      permissions?: string[];
      enabled?: boolean;
    }>({
      query: ({ shopId, keyId, ...data }) => ({
        url: `/shops/${shopId}/apikeys/${keyId}`,
        method: 'PUT',
        body: data,
      }),
      invalidatesTags: (result, error, { shopId, keyId }) => [
        { type: 'ShopAPIKeys', id: shopId },
        { type: 'ShopAPIKeys', id: `${shopId}-${keyId}` },
        'ShopAPIKeys'
      ],
    }),

    deleteShopAPIKey: builder.mutation<{ message: string }, {
      shopId: string;
      keyId: string;
    }>({
      query: ({ shopId, keyId }) => ({
        url: `/shops/${shopId}/apikeys/${keyId}`,
        method: 'DELETE',
      }),
      invalidatesTags: (result, error, { shopId }) => [
        { type: 'ShopAPIKeys', id: shopId },
        'ShopAPIKeys'
      ],
    }),

    // Shop Branch API Key Management endpoints
    getShopBranchAPIKeys: builder.query<APIKey[], string>({
      query: (branchId) => `/shops/branches/${branchId}/apikeys`,
      providesTags: (result, error, branchId) => [
        { type: 'ShopBranchAPIKeys', id: branchId },
        'ShopBranchAPIKeys'
      ],
    }),

    createShopBranchAPIKey: builder.mutation<APIKey, {
      branchId: string;
      name: string;
      permissions?: string[];
    }>({
      query: ({ branchId, ...data }) => ({
        url: `/shops/branches/${branchId}/apikeys`,
        method: 'POST',
        body: data,
      }),
      invalidatesTags: (result, error, { branchId }) => [
        { type: 'ShopBranchAPIKeys', id: branchId },
        'ShopBranchAPIKeys'
      ],
    }),

    getShopBranchAPIKey: builder.query<APIKey, { branchId: string; keyId: string }>({
      query: ({ branchId, keyId }) => `/shops/branches/${branchId}/apikeys/${keyId}`,
      providesTags: (result, error, { branchId, keyId }) => [
        { type: 'ShopBranchAPIKeys', id: `${branchId}-${keyId}` }
      ],
    }),

    updateShopBranchAPIKey: builder.mutation<APIKey, {
      branchId: string;
      keyId: string;
      name?: string;
      permissions?: string[];
      enabled?: boolean;
    }>({
      query: ({ branchId, keyId, ...data }) => ({
        url: `/shops/branches/${branchId}/apikeys/${keyId}`,
        method: 'PUT',
        body: data,
      }),
      invalidatesTags: (result, error, { branchId, keyId }) => [
        { type: 'ShopBranchAPIKeys', id: branchId },
        { type: 'ShopBranchAPIKeys', id: `${branchId}-${keyId}` },
        'ShopBranchAPIKeys'
      ],
    }),

    deleteShopBranchAPIKey: builder.mutation<{ message: string }, {
      branchId: string;
      keyId: string;
    }>({
      query: ({ branchId, keyId }) => ({
        url: `/shops/branches/${branchId}/apikeys/${keyId}`,
        method: 'DELETE',
      }),
      invalidatesTags: (result, error, { branchId }) => [
        { type: 'ShopBranchAPIKeys', id: branchId },
        'ShopBranchAPIKeys'
      ],
    }),

  }),
});

// Export the auto-generated hooks
export const {
  // Subscription hooks
  useGetSubscriptionTiersQuery,
  useGetSubscriptionsQuery,
  useGetActiveSubscriptionQuery,
  useCreateSubscriptionMutation,
  useUpdateSubscriptionMutation,
  useCancelSubscriptionMutation,
  // Credit hooks
  useGetCreditBalanceQuery,
  useGetTransactionsQuery,
  useGetNextScheduledCreditDateQuery,
  useGetScheduledCreditHistoryQuery,
  useProcessScheduledCreditsMutation,
  useManuallyAddScheduledCreditsMutation,
  // API Key hooks
  useGetAPIKeysQuery,
  useCreateAPIKeyMutation,
  useUpdateAPIKeyMutation,
  useDeleteAPIKeyMutation,
  // Usage hooks
  useGetUsageSummaryQuery,
  useGetUsageQuery,
  // Webhook hooks
  useGetWebhooksQuery,
  useGetWebhookQuery,
  useCreateWebhookMutation,
  useUpdateWebhookMutation,
  useDeleteWebhookMutation,
  useGetWebhookDeliveriesQuery,
  // Analytics hooks
  useGetAnalyticsSummaryQuery,
  useGetAnalyticsTrendsQuery,
  useGetEndpointAnalyticsQuery,
  useGetPerformanceMetricsQuery,
  // User hooks
  useGetCurrentUserQuery,
  useUpdateUserMutation,
  // Organization hooks
  useGetOrganizationsQuery,
  useGetOrganizationQuery,
  useGetOrganizationByIdQuery,
  useCreateOrganizationMutation,
  useUpdateOrganizationMutation,
  useDeleteOrganizationMutation,
  // Branch hooks
  useGetBranchesQuery,
  useGetBranchQuery,
  useCreateBranchMutation,
  useUpdateBranchMutation,
  useDeleteBranchMutation,
  // External User hooks
  useGetExternalUsersQuery,
  useGetExternalUserQuery,
  useCreateExternalUserMutation,
  useUpdateExternalUserMutation,
  useDeleteExternalUserMutation,
  useAddCreditsToExternalUserMutation,
  useReduceCreditsFromExternalUserMutation,
  // Shop hooks
  useGetShopsQuery,
  useGetShopQuery,
  useGetShopBySlugQuery,
  useCreateShopMutation,
  useUpdateShopMutation,
  useDeleteShopMutation,
  useGetShopCustomersQuery,
  useAddShopCustomerMutation,
  useAddShopCreditMutation,
  useGetCreditCodesQuery,
  useGetShopTransactionsQuery,
  useGenerateCreditCodeMutation,
  useGenerateQRCodeMutation,
  // Customer hooks
  useGetCustomerShopsQuery,
  useGetCustomerShopQuery,
  useGetCustomerTransactionsQuery,
  useUseShopCreditMutation,
  useRedeemCreditCodeMutation,



  // Shop Statistics hooks
  useGetShopStatsQuery,

  // Shop API Key Management hooks
  useGetShopAPIKeysQuery,
  useCreateShopAPIKeyMutation,
  useGetShopAPIKeyQuery,
  useUpdateShopAPIKeyMutation,
  useDeleteShopAPIKeyMutation,

  // Shop Branch API Key Management hooks
  useGetShopBranchAPIKeysQuery,
  useCreateShopBranchAPIKeyMutation,
  useGetShopBranchAPIKeyQuery,
  useUpdateShopBranchAPIKeyMutation,
  useDeleteShopBranchAPIKeyMutation,

} = apiSlice;
