package main

import (
	"log"

	"github.com/adc-credit/backend/internal/database"
	"github.com/joho/godotenv"
)

func main() {
	// Load environment variables
	if err := godotenv.Load(); err != nil {
		log.Println("No .env file found, using environment variables")
	}

	// Initialize database connection
	if err := database.InitDB(); err != nil {
		log.Fatalf("Failed to connect to database: %v", err)
	}

	// Fix string slice columns
	if err := fixStringSliceColumns(); err != nil {
		log.Fatalf("Failed to fix string slice columns: %v", err)
	}

	log.Println("String slice columns fixed successfully!")
}

func fixStringSliceColumns() error {
	// Begin transaction
	tx := database.DB.Begin()
	if tx.Error != nil {
		return tx.Error
	}

	// Fix API keys permissions column
	if err := tx.Exec(`
		ALTER TABLE api_keys
		ALTER COLUMN permissions TYPE TEXT[]
	`).Error; err != nil {
		tx.Rollback()
		log.Printf("Error fixing api_keys.permissions: %v", err)
		// Continue with other columns even if this one fails
	}

	// Fix subscription tiers features column
	if err := tx.Exec(`
		ALTER TABLE subscription_tiers
		ALTER COLUMN features TYPE TEXT[]
	`).Error; err != nil {
		tx.Rollback()
		log.Printf("Error fixing subscription_tiers.features: %v", err)
		// Continue with other columns even if this one fails
	}

	// Fix webhooks events column
	if err := tx.Exec(`
		ALTER TABLE webhooks
		ALTER COLUMN events TYPE TEXT[]
	`).Error; err != nil {
		tx.Rollback()
		log.Printf("Error fixing webhooks.events: %v", err)
		// Continue with other columns even if this one fails
	}

	// Commit transaction
	return tx.Commit().Error
}
