package handlers

import (
	"fmt"
	"net/http"
	"strconv"
	"time"

	"github.com/adc-credit/backend/internal/database"
	"github.com/adc-credit/backend/internal/models"
	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/stripe/stripe-go/v72"
	"github.com/stripe/stripe-go/v72/checkout/session"
)

// GetSubscriptionTiers returns all available subscription tiers
func GetSubscriptionTiers(c *gin.Context) {
	var tiers []models.SubscriptionTier
	if err := database.DB.Find(&tiers).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch subscription tiers"})
		return
	}

	c.JSON(http.StatusOK, tiers)
}

// GetSubscriptions returns all subscriptions for the current user
func GetSubscriptions(c *gin.Context) {
	user := c.MustGet("user").(models.User)

	// Get query parameters
	subscriptionType := c.Query("type")
	merchantShopID := c.Query("merchant_shop_id")
	shopCustomerID := c.Query("shop_customer_id")

	// Build query
	query := database.DB.Where("user_id = ?", user.ID)

	// Filter by subscription type if provided
	if subscriptionType != "" {
		if subscriptionType != "personal" && subscriptionType != "merchant" && subscriptionType != "customer" {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid subscription type. Must be 'personal', 'merchant', or 'customer'"})
			return
		}
		query = query.Where("subscription_type = ?", subscriptionType)
	}

	// Filter by merchant shop ID if provided
	if merchantShopID != "" {
		shopUUID, err := uuid.Parse(merchantShopID)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid merchant shop ID"})
			return
		}
		query = query.Where("merchant_shop_id = ?", shopUUID)
	}

	// Filter by shop customer ID if provided
	if shopCustomerID != "" {
		customerUUID, err := uuid.Parse(shopCustomerID)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid shop customer ID"})
			return
		}
		query = query.Where("shop_customer_id = ?", customerUUID)
	}

	// Execute query with preloads
	var subscriptions []models.Subscription
	if err := query.Preload("SubscriptionTier").
		Preload("MerchantShop").
		Preload("ShopCustomer").
		Preload("ShopCustomer.Shop").
		Find(&subscriptions).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch subscriptions"})
		return
	}

	c.JSON(http.StatusOK, subscriptions)
}

// CreateSubscription creates a new subscription for the user
func CreateSubscription(c *gin.Context) {
	user := c.MustGet("user").(models.User)

	type CreateSubscriptionRequest struct {
		SubscriptionTierID uint     `json:"subscription_tier_id" binding:"required"`
		AutoRenew          bool     `json:"auto_renew"`
		SubscriptionType   string   `json:"subscription_type"`
		MerchantShopID     string   `json:"merchant_shop_id"`
		ShopCustomerID     string   `json:"shop_customer_id"`
	}

	var req CreateSubscriptionRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Check if subscription tier exists
	var tier models.SubscriptionTier
	if err := database.DB.First(&tier, req.SubscriptionTierID).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Subscription tier not found"})
		return
	}

	// Validate subscription type
	subscriptionType := "personal"
	if req.SubscriptionType != "" {
		if req.SubscriptionType != "personal" && req.SubscriptionType != "merchant" && req.SubscriptionType != "customer" {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid subscription type. Must be 'personal', 'merchant', or 'customer'"})
			return
		}
		subscriptionType = req.SubscriptionType
	}

	// Validate merchant shop ID if subscription type is merchant
	var merchantShopID *uuid.UUID
	if subscriptionType == "merchant" && req.MerchantShopID != "" {
		shopUUID, err := uuid.Parse(req.MerchantShopID)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid merchant shop ID"})
			return
		}

		// Check if shop exists and belongs to the user
		var shop models.MerchantShop
		if err := database.DB.Where("id = ? AND owner_user_id = ?", shopUUID, user.ID).First(&shop).Error; err != nil {
			c.JSON(http.StatusNotFound, gin.H{"error": "Merchant shop not found or not owned by user"})
			return
		}

		merchantShopID = &shopUUID
	}

	// Validate shop customer ID if subscription type is customer
	var shopCustomerID *uuid.UUID
	if subscriptionType == "customer" && req.ShopCustomerID != "" {
		customerUUID, err := uuid.Parse(req.ShopCustomerID)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid shop customer ID"})
			return
		}

		// Check if customer exists and belongs to the user
		var customer models.ShopCustomer
		if err := database.DB.Where("id = ? AND user_id = ?", customerUUID, user.ID).First(&customer).Error; err != nil {
			c.JSON(http.StatusNotFound, gin.H{"error": "Shop customer not found or not associated with user"})
			return
		}

		shopCustomerID = &customerUUID
	}

	// Check if user already has active subscriptions of the same type
	var activeSubscriptions []models.Subscription
	query := database.DB.Where("user_id = ? AND status = ? AND subscription_type = ?", user.ID, "active", subscriptionType)

	// Add additional conditions for merchant and customer subscriptions
	if subscriptionType == "merchant" && merchantShopID != nil {
		query = query.Where("merchant_shop_id = ?", merchantShopID)
	} else if subscriptionType == "customer" && shopCustomerID != nil {
		query = query.Where("shop_customer_id = ?", shopCustomerID)
	}

	if err := query.Find(&activeSubscriptions).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to check existing subscriptions"})
		return
	}

	// Start a transaction
	tx := database.DB.Begin()

	// Cancel all active subscriptions of the same type
	if len(activeSubscriptions) > 0 {
		now := time.Now()
		for _, activeSubscription := range activeSubscriptions {
			activeSubscription.EndDate = &now
			activeSubscription.Status = "cancelled"

			if err := tx.Save(&activeSubscription).Error; err != nil {
				tx.Rollback()
				c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update existing subscription"})
				return
			}
		}
	}

	// Create new subscription
	subscription := models.Subscription{
		ID:                uuid.New(),
		UserID:            user.ID,
		SubscriptionTierID: tier.ID,
		StartDate:         time.Now(),
		AutoRenew:         req.AutoRenew,
		Status:            "active",
		CreditBalance:     tier.CreditLimit,
		SubscriptionType:  subscriptionType,
		MerchantShopID:    merchantShopID,
		ShopCustomerID:    shopCustomerID,
	}

	if err := tx.Create(&subscription).Error; err != nil {
		tx.Rollback()
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create subscription"})
		return
	}

	// Create transaction record for initial credits
	transaction := models.Transaction{
		ID:            uuid.New(),
		UserID:        user.ID,
		SubscriptionID: subscription.ID,
		Type:          "credit_add",
		Amount:        tier.CreditLimit,
		Description:   "Initial credits for " + tier.Name + " subscription",
	}

	if err := tx.Create(&transaction).Error; err != nil {
		tx.Rollback()
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create transaction record"})
		return
	}

	// Commit transaction
	if err := tx.Commit().Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to commit transaction"})
		return
	}

	// Load subscription tier for response
	database.DB.Model(&subscription).Association("SubscriptionTier").Find(&subscription.SubscriptionTier)

	c.JSON(http.StatusCreated, subscription)
}

// UpdateSubscription updates an existing subscription
func UpdateSubscription(c *gin.Context) {
	user := c.MustGet("user").(models.User)
	id := c.Param("id")

	subscriptionID, err := uuid.Parse(id)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid subscription ID"})
		return
	}

	type UpdateSubscriptionRequest struct {
		AutoRenew bool `json:"auto_renew"`
	}

	var req UpdateSubscriptionRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	var subscription models.Subscription
	if err := database.DB.Where("id = ? AND user_id = ?", subscriptionID, user.ID).First(&subscription).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Subscription not found"})
		return
	}

	subscription.AutoRenew = req.AutoRenew

	if err := database.DB.Save(&subscription).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update subscription"})
		return
	}

	// Load subscription tier for response
	database.DB.Model(&subscription).Association("SubscriptionTier").Find(&subscription.SubscriptionTier)

	c.JSON(http.StatusOK, subscription)
}

// GetActiveSubscription returns the active subscription for the current user
func GetActiveSubscription(c *gin.Context) {
	user := c.MustGet("user").(models.User)

	// Get query parameters
	subscriptionType := c.Query("type")
	merchantShopID := c.Query("merchant_shop_id")
	shopCustomerID := c.Query("shop_customer_id")

	// Build query
	query := database.DB.Where("user_id = ? AND status = ?", user.ID, "active")

	// Filter by subscription type if provided
	if subscriptionType != "" {
		if subscriptionType != "personal" && subscriptionType != "merchant" && subscriptionType != "customer" {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid subscription type. Must be 'personal', 'merchant', or 'customer'"})
			return
		}
		query = query.Where("subscription_type = ?", subscriptionType)
	} else {
		// Default to personal subscription if no type is specified
		query = query.Where("subscription_type = ?", "personal")
	}

	// Filter by merchant shop ID if provided
	if merchantShopID != "" {
		shopUUID, err := uuid.Parse(merchantShopID)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid merchant shop ID"})
			return
		}
		query = query.Where("merchant_shop_id = ?", shopUUID)
	}

	// Filter by shop customer ID if provided
	if shopCustomerID != "" {
		customerUUID, err := uuid.Parse(shopCustomerID)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid shop customer ID"})
			return
		}
		query = query.Where("shop_customer_id = ?", customerUUID)
	}

	var subscription models.Subscription
	if err := query.Preload("SubscriptionTier").
		Preload("MerchantShop").
		Preload("ShopCustomer").
		First(&subscription).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "No active subscription found"})
		return
	}

	c.JSON(http.StatusOK, subscription)
}

// CancelSubscription cancels an active subscription
func CancelSubscription(c *gin.Context) {
	user := c.MustGet("user").(models.User)
	id := c.Param("id")

	subscriptionID, err := uuid.Parse(id)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid subscription ID"})
		return
	}

	var subscription models.Subscription
	if err := database.DB.Where("id = ? AND user_id = ? AND status = ?", subscriptionID, user.ID, "active").First(&subscription).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Active subscription not found"})
		return
	}

	// Start a transaction
	tx := database.DB.Begin()

	now := time.Now()
	subscription.EndDate = &now
	subscription.Status = "cancelled"
	subscription.AutoRenew = false

	if err := tx.Save(&subscription).Error; err != nil {
		tx.Rollback()
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to cancel subscription"})
		return
	}

	// Commit transaction
	if err := tx.Commit().Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to commit transaction"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Subscription cancelled successfully"})
}

// Admin handlers

// GetAllSubscriptions returns all subscriptions (admin only)
func GetAllSubscriptions(c *gin.Context) {
	var subscriptions []models.Subscription
	if err := database.DB.Preload("SubscriptionTier").Find(&subscriptions).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch subscriptions"})
		return
	}

	c.JSON(http.StatusOK, subscriptions)
}

// CreateSubscriptionTier creates a new subscription tier (admin only)
func CreateSubscriptionTier(c *gin.Context) {
	var tier models.SubscriptionTier
	if err := c.ShouldBindJSON(&tier); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	if err := database.DB.Create(&tier).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create subscription tier"})
		return
	}

	c.JSON(http.StatusCreated, tier)
}

// UpdateSubscriptionTier updates an existing subscription tier (admin only)
func UpdateSubscriptionTier(c *gin.Context) {
	id := c.Param("id")
	tierID, err := strconv.ParseUint(id, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid tier ID"})
		return
	}

	var tier models.SubscriptionTier
	if err := database.DB.First(&tier, uint(tierID)).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Subscription tier not found"})
		return
	}

	if err := c.ShouldBindJSON(&tier); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	if err := database.DB.Save(&tier).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update subscription tier"})
		return
	}

	c.JSON(http.StatusOK, tier)
}

// DeleteSubscriptionTier deletes a subscription tier (admin only)
func DeleteSubscriptionTier(c *gin.Context) {
	id := c.Param("id")
	tierID, err := strconv.ParseUint(id, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid tier ID"})
		return
	}

	// Check if tier is in use
	var count int64
	database.DB.Model(&models.Subscription{}).Where("subscription_tier_id = ?", uint(tierID)).Count(&count)
	if count > 0 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Cannot delete tier that is in use by subscriptions"})
		return
	}

	if err := database.DB.Delete(&models.SubscriptionTier{}, uint(tierID)).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete subscription tier"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Subscription tier deleted successfully"})
}

// GetStripePriceID returns the Stripe price ID for a given tier and subscription type
func GetStripePriceID(tierId int, subscriptionType string) (string, bool) {
	// Create the key for the price ID map
	priceKey := fmt.Sprintf("%d_%s", tierId, subscriptionType)

	// Get the price ID from the map in stripe.go
	priceID, ok := STRIPE_PRICE_IDS[priceKey]

	// If not found, try a fallback key format
	if !ok || priceID == "" {
		// Try alternative key format
		altKey := fmt.Sprintf("price_%d_%s", tierId, subscriptionType)
		priceID, ok = STRIPE_PRICE_IDS[altKey]
	}

	return priceID, ok
}

// UpgradeSubscription upgrades or downgrades an existing subscription to a new tier
func UpgradeSubscription(c *gin.Context) {
	user := c.MustGet("user").(models.User)
	id := c.Param("id")

	subscriptionID, err := uuid.Parse(id)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid subscription ID"})
		return
	}

	// Define request structure
	type UpgradeSubscriptionRequest struct {
		NewTierID int  `json:"new_tier_id" binding:"required"`
		Prorate   bool `json:"prorate" default:"true"`
	}

	var req UpgradeSubscriptionRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Get the current subscription
	var subscription models.Subscription
	if err := database.DB.Where("id = ? AND user_id = ? AND status = ?", subscriptionID, user.ID, "active").First(&subscription).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Active subscription not found"})
		return
	}

	// Get the new subscription tier
	var newTier models.SubscriptionTier
	if err := database.DB.First(&newTier, req.NewTierID).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "New subscription tier not found"})
		return
	}

	// Get the current subscription tier
	var currentTier models.SubscriptionTier
	if err := database.DB.First(&currentTier, subscription.SubscriptionTierID).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get current subscription tier"})
		return
	}

	// If the subscription has a Stripe subscription ID, we need to update it in Stripe
	if subscription.StripeSubscriptionID != "" {
		// For paid plans, create a checkout session for the upgrade
		// Get the Stripe price ID for the new tier
		priceKey := fmt.Sprintf("%d_%s", newTier.ID, subscription.SubscriptionType)

		// Import the STRIPE_PRICE_IDS from stripe.go
		priceID, ok := GetStripePriceID(int(newTier.ID), subscription.SubscriptionType)
		if !ok || priceID == "" {
			// If no specific price ID is found, try to use a default one
			fmt.Printf("No price ID found for key: %s, using default price ID\n", priceKey)
			priceID = "price_1RQR2ZCtNXkGk5bXcBExxwz6" // Default price ID
		}

		// Get the success and cancel URLs from the frontend
		origin := c.GetHeader("Origin")
		if origin == "" {
			origin = "https://localhost:3800"
		}

		successURL := fmt.Sprintf("%s/dashboard/subscriptions?success=true&upgrade=true&session_id={CHECKOUT_SESSION_ID}&subscription_id=%s",
			origin,
			subscription.ID,
		)

		cancelURL := fmt.Sprintf("%s/dashboard/subscriptions?canceled=true", origin)

		// Create the checkout session parameters
		params := &stripe.CheckoutSessionParams{
			PaymentMethodTypes: stripe.StringSlice([]string{
				"card",
			}),
			LineItems: []*stripe.CheckoutSessionLineItemParams{
				{
					Price:    stripe.String(priceID),
					Quantity: stripe.Int64(1),
				},
			},
			Mode:          stripe.String(string(stripe.CheckoutSessionModeSubscription)),
			SuccessURL:    stripe.String(successURL),
			CancelURL:     stripe.String(cancelURL),
			CustomerEmail: stripe.String(user.Email),
		}

		// Add metadata
		params.AddMetadata("user_id", user.ID.String())
		params.AddMetadata("subscription_id", subscription.ID.String())
		params.AddMetadata("old_tier_id", strconv.Itoa(int(subscription.SubscriptionTierID)))
		params.AddMetadata("new_tier_id", strconv.Itoa(req.NewTierID))
		params.AddMetadata("subscription_type", subscription.SubscriptionType)
		params.AddMetadata("upgrade_type", "tier_change")

		// Add merchant shop ID if present
		if subscription.MerchantShopID != nil {
			params.AddMetadata("merchant_shop_id", subscription.MerchantShopID.String())
		}

		// Add shop customer ID if present
		if subscription.ShopCustomerID != nil {
			params.AddMetadata("shop_customer_id", subscription.ShopCustomerID.String())
		}

		// Create the checkout session
		s, err := session.New(params)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}

		c.JSON(http.StatusOK, gin.H{
			"checkout_url": s.URL,
			"message":      "Please complete the checkout process to upgrade your subscription",
		})
		return
	}

	// For free plans or if no Stripe subscription exists, handle the upgrade directly
	// Start a transaction
	tx := database.DB.Begin()

	// Calculate prorated credits if requested
	var newCreditBalance int
	if req.Prorate {
		// Calculate the percentage of time remaining in the current subscription period
		var timeRemaining float64 = 1.0 // Default to 100% if no end date
		if subscription.EndDate != nil {
			totalPeriod := subscription.EndDate.Sub(subscription.StartDate)
			elapsed := time.Since(subscription.StartDate)
			timeRemaining = 1.0 - (elapsed.Seconds() / totalPeriod.Seconds())
			if timeRemaining < 0 {
				timeRemaining = 0
			}
		}

		// Calculate prorated credits based on remaining time and credit usage
		usedCredits := currentTier.CreditLimit - subscription.CreditBalance
		usedPercentage := float64(usedCredits) / float64(currentTier.CreditLimit)

		// If upgrading to a higher tier, add the difference in credits prorated by time remaining
		// If downgrading, reduce credits proportionally
		if newTier.CreditLimit > currentTier.CreditLimit {
			// Upgrading: keep current balance plus prorated additional credits
			additionalCredits := newTier.CreditLimit - currentTier.CreditLimit
			newCreditBalance = subscription.CreditBalance + int(float64(additionalCredits)*timeRemaining)
		} else {
			// Downgrading: reduce credits proportionally
			newCreditBalance = int(float64(newTier.CreditLimit) * (1.0 - usedPercentage))
		}
	} else {
		// No proration, just set to the new tier's credit limit
		newCreditBalance = newTier.CreditLimit
	}

	// Update the subscription
	now := time.Now()

	// Cancel the current subscription
	subscription.EndDate = &now
	subscription.Status = "cancelled"

	if err := tx.Save(&subscription).Error; err != nil {
		tx.Rollback()
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update existing subscription"})
		return
	}

	// Create a new subscription with the new tier
	newSubscription := models.Subscription{
		ID:                uuid.New(),
		UserID:            user.ID,
		SubscriptionTierID: newTier.ID,
		StartDate:         now,
		AutoRenew:         subscription.AutoRenew,
		Status:            "active",
		CreditBalance:     newCreditBalance,
		SubscriptionType:  subscription.SubscriptionType,
		MerchantShopID:    subscription.MerchantShopID,
		ShopCustomerID:    subscription.ShopCustomerID,
	}

	if err := tx.Create(&newSubscription).Error; err != nil {
		tx.Rollback()
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create new subscription"})
		return
	}

	// Create transaction record for the upgrade/downgrade
	var description string
	var amount int

	if newTier.CreditLimit > currentTier.CreditLimit {
		description = fmt.Sprintf("Upgrade from %s to %s tier", currentTier.Name, newTier.Name)
		amount = newCreditBalance - subscription.CreditBalance
	} else {
		description = fmt.Sprintf("Downgrade from %s to %s tier", currentTier.Name, newTier.Name)
		amount = newCreditBalance - subscription.CreditBalance
	}

	transaction := models.Transaction{
		ID:             uuid.New(),
		UserID:         user.ID,
		SubscriptionID: newSubscription.ID,
		Type:           "credit_add",
		Amount:         amount,
		Description:    description,
		Reference:      fmt.Sprintf("tier_change_%s_to_%s", currentTier.Name, newTier.Name),
	}

	if err := tx.Create(&transaction).Error; err != nil {
		tx.Rollback()
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create transaction record"})
		return
	}

	// Commit transaction
	if err := tx.Commit().Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to commit transaction"})
		return
	}

	// Load subscription tier for response
	database.DB.Model(&newSubscription).Association("SubscriptionTier").Find(&newSubscription.SubscriptionTier)

	c.JSON(http.StatusOK, gin.H{
		"message": fmt.Sprintf("Successfully changed subscription from %s to %s tier", currentTier.Name, newTier.Name),
		"subscription": newSubscription,
	})
}
