'use client';

import { useEffect, useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { CalendarIcon, ClockIcon, RefreshCw } from 'lucide-react';
import { formatDistanceToNow, format, parseISO } from 'date-fns';
import { useGetNextScheduledCreditDateQuery, useGetScheduledCreditHistoryQuery } from '@/lib/api/apiSlice';
import { Transaction } from '@/types';
import { Skeleton } from '@/components/ui/skeleton';

export function ScheduledCredits() {
  const { data: nextDateData, isLoading: isNextDateLoading, error: nextDateError } = useGetNextScheduledCreditDateQuery();
  const { data: historyData, isLoading: isHistoryLoading, error: historyError } = useGetScheduledCreditHistoryQuery();

  const formatDate = (dateString: string) => {
    try {
      const date = parseISO(dateString);
      return format(date, 'PPP');
    } catch (error) {
      return 'Invalid date';
    }
  };

  const formatRelativeDate = (dateString: string) => {
    try {
      const date = parseISO(dateString);
      return formatDistanceToNow(date, { addSuffix: true });
    } catch (error) {
      return 'Invalid date';
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <RefreshCw className="h-5 w-5" />
          Scheduled Credits
        </CardTitle>
        <CardDescription>
          Your subscription includes automatic credit replenishment
        </CardDescription>
      </CardHeader>
      <CardContent>
        {isNextDateLoading ? (
          <div className="space-y-4">
            <Skeleton className="h-8 w-full" />
            <Skeleton className="h-20 w-full" />
          </div>
        ) : nextDateError ? (
          <div className="text-destructive">
            Error loading next scheduled credit date
          </div>
        ) : (
          <div className="space-y-4">
            <div className="rounded-lg border p-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <CalendarIcon className="h-5 w-5 text-muted-foreground" />
                  <span className="font-medium">Next Credit Refresh</span>
                </div>
                <Badge variant="outline" className="bg-primary/10">
                  <ClockIcon className="mr-1 h-3 w-3" />
                  {nextDateData?.next_scheduled_credit_date
                    ? formatRelativeDate(nextDateData.next_scheduled_credit_date)
                    : 'Not scheduled'}
                </Badge>
              </div>
              <div className="mt-2 text-sm text-muted-foreground">
                {nextDateData?.next_scheduled_credit_date
                  ? `Your credits will be refreshed on ${formatDate(nextDateData.next_scheduled_credit_date)}`
                  : 'No scheduled credit refresh found'}
              </div>
            </div>

            <div>
              <h4 className="mb-2 font-medium">Recent Credit History</h4>
              {isHistoryLoading ? (
                <div className="space-y-2">
                  <Skeleton className="h-12 w-full" />
                  <Skeleton className="h-12 w-full" />
                  <Skeleton className="h-12 w-full" />
                </div>
              ) : historyError ? (
                <div className="text-destructive">
                  Error loading credit history
                </div>
              ) : historyData && historyData.length > 0 ? (
                <div className="space-y-2">
                  {historyData.slice(0, 5).map((transaction: Transaction) => (
                    <div
                      key={transaction.id}
                      className="flex items-center justify-between rounded-md border p-2 text-sm"
                    >
                      <div>
                        <div className="font-medium">{transaction.description}</div>
                        <div className="text-xs text-muted-foreground">
                          {formatDate(transaction.created_at)}
                        </div>
                      </div>
                      <div className="font-medium text-green-600">
                        +{transaction.amount} credits
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="rounded-md border p-4 text-center text-sm text-muted-foreground">
                  No scheduled credit history found
                </div>
              )}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
