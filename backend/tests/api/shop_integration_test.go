package api

import (
	"fmt"
	"testing"

	"github.com/adc-credit/backend/internal/database"
	"github.com/adc-credit/backend/internal/models"
	"github.com/adc-credit/backend/internal/routes"
	testutils "github.com/adc-credit/backend/internal/testing"
	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"github.com/stretchr/testify/suite"
)

// ShopIntegrationTestSuite tests the new shop APIs
type ShopIntegrationTestSuite struct {
	suite.Suite
	router    *gin.Engine
	testUser  *models.User
	testShop  *models.Shop
	authToken string
}

// SetupSuite runs once before all tests
func (suite *ShopIntegrationTestSuite) SetupSuite() {
	// Set up test environment
	err := testutils.SetupTestEnvironment()
	suite.Require().NoError(err)

	// Set up router with all routes
	suite.router = gin.New()
	suite.router.Use(gin.Recovery())

	// Register shop routes (they include their own auth middleware)
	routes.RegisterShopRoutes(suite.router)

	// Set the database connection for handlers
	database.DB = testutils.TestDB
}

// SetupTest runs before each test
func (suite *ShopIntegrationTestSuite) SetupTest() {
	// Clean test data
	testutils.CleanTestData()

	// Create test user with auth token
	var err error
	suite.testUser, suite.authToken = testutils.CreateTestUserWithAuth(suite.T(), "<EMAIL>", "Shop Test User")
	require.NoError(suite.T(), err)

	// Create test shop
	suite.testShop, err = suite.createTestShop()
	require.NoError(suite.T(), err)
}

// TearDownTest runs after each test
func (suite *ShopIntegrationTestSuite) TearDownTest() {
	testutils.CleanTestData()
}

// createTestShop creates a test shop for testing
func (suite *ShopIntegrationTestSuite) createTestShop() (*models.Shop, error) {
	// Generate unique slug for each test
	uniqueSlug := fmt.Sprintf("test-shop-%s", uuid.New().String()[:8])

	shop := &models.Shop{
		ID:           uuid.New(),
		OwnerUserID:  suite.testUser.ID,
		Name:         "Test Shop",
		Description:  "A test shop for integration testing",
		ContactEmail: "<EMAIL>",
		ContactPhone: "+1234567890",
		ShopType:     "retail",
		Slug:         uniqueSlug,
	}

	if err := testutils.TestDB.Create(shop).Error; err != nil {
		return nil, err
	}

	return shop, nil
}

// createTestCustomers creates test customers for the shop
func (suite *ShopIntegrationTestSuite) createTestCustomers(count int) ([]*models.ShopCustomer, error) {
	customers := make([]*models.ShopCustomer, count)

	for i := 0; i < count; i++ {
		// Create a user for the customer
		customerUser := &models.User{
			ID:    uuid.New(),
			Email: fmt.Sprintf("<EMAIL>", i),
			Name:  fmt.Sprintf("Customer %d", i),
			Role:  "user",
		}

		if err := testutils.TestDB.Create(customerUser).Error; err != nil {
			return nil, err
		}

		// Create shop customer
		customer := &models.ShopCustomer{
			ID:            uuid.New(),
			ShopID:        suite.testShop.ID,
			UserID:        customerUser.ID,
			CreditBalance: 100 + i*50, // Varying credit balances
		}

		if err := testutils.TestDB.Create(customer).Error; err != nil {
			return nil, err
		}

		customers[i] = customer
	}

	return customers, nil
}

// createTestCreditCodes creates test credit codes for the shop
func (suite *ShopIntegrationTestSuite) createTestCreditCodes(count int) ([]*models.CreditCode, error) {
	codes := make([]*models.CreditCode, count)

	for i := 0; i < count; i++ {
		code := &models.CreditCode{
			ID:         uuid.New(),
			ShopID:     suite.testShop.ID,
			Code:       fmt.Sprintf("TEST%d", 1000+i),
			Amount:     50 + i*25,
			IsRedeemed: i%2 == 0, // Half redeemed, half not
		}

		if err := testutils.TestDB.Create(code).Error; err != nil {
			return nil, err
		}

		codes[i] = code
	}

	return codes, nil
}

// ============================================================================
// SHOP STATISTICS API TESTS
// ============================================================================

func (suite *ShopIntegrationTestSuite) TestGetShopStats_Success() {
	t := suite.T()

	// Create test data
	_, err := suite.createTestCustomers(5)
	require.NoError(t, err)

	_, err = suite.createTestCreditCodes(10)
	require.NoError(t, err)

	// Make request to get shop stats
	req := testutils.TestRequest{
		Method: "GET",
		URL:    fmt.Sprintf("/api/v1/shops/%s/stats", suite.testShop.ID),
		Headers: map[string]string{
			"Authorization": "Bearer " + suite.authToken,
		},
	}

	resp := testutils.MakeRequest(t, suite.router, req)

	// Assert response
	testutils.AssertSuccessResponse(t, resp)
	assert.Equal(t, 200, resp.StatusCode)

	// Parse response
	stats := resp.Body

	// Verify statistics
	assert.Equal(t, suite.testShop.ID.String(), stats["shop_id"])
	assert.Equal(t, suite.testShop.Name, stats["shop_name"])
	assert.Equal(t, float64(5), stats["total_customers"])
	assert.Equal(t, float64(10), stats["total_credit_codes"])

	// Verify calculated fields exist
	assert.Contains(t, stats, "total_credits_issued")
	assert.Contains(t, stats, "total_credits_redeemed")
	assert.Contains(t, stats, "redemption_rate")
	assert.Contains(t, stats, "active_credit_codes")
	assert.Contains(t, stats, "total_customer_balance")

	// Verify customer balance calculation
	expectedBalance := float64(100 + 150 + 200 + 250 + 300) // Sum of customer balances
	assert.Equal(t, expectedBalance, stats["total_customer_balance"])
}

func (suite *ShopIntegrationTestSuite) TestGetShopStats_NotFound() {
	t := suite.T()

	// Use non-existent shop ID
	nonExistentID := uuid.New()

	req := testutils.TestRequest{
		Method: "GET",
		URL:    fmt.Sprintf("/api/v1/shops/%s/stats", nonExistentID),
		Headers: map[string]string{
			"Authorization": "Bearer " + suite.authToken,
		},
	}

	resp := testutils.MakeRequest(t, suite.router, req)

	// Should return 404
	assert.Equal(t, 404, resp.StatusCode)
	testutils.AssertErrorResponse(t, resp, 404)
}

func (suite *ShopIntegrationTestSuite) TestGetShopStats_Unauthorized() {
	t := suite.T()

	req := testutils.TestRequest{
		Method: "GET",
		URL:    fmt.Sprintf("/api/v1/shops/%s/stats", suite.testShop.ID),
		// No authorization header
	}

	resp := testutils.MakeRequest(t, suite.router, req)

	// Should return 401
	assert.Equal(t, 401, resp.StatusCode)
}

// ============================================================================
// SHOP API KEY MANAGEMENT TESTS
// ============================================================================

func (suite *ShopIntegrationTestSuite) TestCreateShopAPIKey_Success() {
	t := suite.T()

	apiKeyData := map[string]interface{}{
		"name":        "Test API Key",
		"permissions": []string{"read", "write"},
	}

	req := testutils.TestRequest{
		Method: "POST",
		URL:    fmt.Sprintf("/api/v1/shops/%s/apikeys", suite.testShop.ID),
		Headers: map[string]string{
			"Authorization": "Bearer " + suite.authToken,
		},
		Body: apiKeyData,
	}

	resp := testutils.MakeRequest(t, suite.router, req)

	// Assert response
	testutils.AssertSuccessResponse(t, resp)
	assert.Equal(t, 201, resp.StatusCode)

	// Parse response
	apiKey := resp.Body

	// Verify API key data
	assert.Equal(t, "Test API Key", apiKey["name"])
	assert.True(t, apiKey["enabled"].(bool))
	assert.Contains(t, apiKey["key"].(string), "sk_")
	assert.Equal(t, suite.testUser.ID.String(), apiKey["user_id"])

	// Verify permissions
	permissions := apiKey["permissions"].([]interface{})
	assert.Len(t, permissions, 2)
	assert.Contains(t, permissions, "read")
	assert.Contains(t, permissions, "write")
}

func (suite *ShopIntegrationTestSuite) TestGetShopAPIKeys_Success() {
	t := suite.T()

	// Create test API keys first
	for i := 0; i < 3; i++ {
		apiKey := &models.APIKey{
			ID:          uuid.New(),
			UserID:      suite.testUser.ID,
			ShopID:      &suite.testShop.ID,
			Name:        fmt.Sprintf("Test Key %d", i),
			Key:         fmt.Sprintf("sk_test_key_%d", i),
			Permissions: []string{"read"},
			Enabled:     true,
		}
		require.NoError(t, testutils.TestDB.Create(apiKey).Error)
	}

	req := testutils.TestRequest{
		Method: "GET",
		URL:    fmt.Sprintf("/api/v1/shops/%s/apikeys", suite.testShop.ID),
		Headers: map[string]string{
			"Authorization": "Bearer " + suite.authToken,
		},
	}

	resp := testutils.MakeRequest(t, suite.router, req)

	// Assert response
	testutils.AssertSuccessResponse(t, resp)
	assert.Equal(t, 200, resp.StatusCode)

	// For now, just verify the response is successful
	// The actual API response structure will be determined during testing
	assert.Contains(t, resp.Body, "message") // or whatever the actual response contains
}

func (suite *ShopIntegrationTestSuite) TestUpdateShopAPIKey_Success() {
	t := suite.T()

	// Create test API key first
	apiKey := &models.APIKey{
		ID:          uuid.New(),
		UserID:      suite.testUser.ID,
		ShopID:      &suite.testShop.ID,
		Name:        "Original Name",
		Key:         "sk_test_key",
		Permissions: []string{"read"},
		Enabled:     true,
	}
	require.NoError(t, testutils.TestDB.Create(apiKey).Error)

	// Update data
	updateData := map[string]interface{}{
		"name":        "Updated Name",
		"permissions": []string{"read", "write"},
		"enabled":     false,
	}

	req := testutils.TestRequest{
		Method: "PUT",
		URL:    fmt.Sprintf("/api/v1/shops/%s/apikeys/%s", suite.testShop.ID, apiKey.ID),
		Headers: map[string]string{
			"Authorization": "Bearer " + suite.authToken,
		},
		Body: updateData,
	}

	resp := testutils.MakeRequest(t, suite.router, req)

	// Assert response
	testutils.AssertSuccessResponse(t, resp)
	assert.Equal(t, 200, resp.StatusCode)

	// Parse response
	updatedKey := resp.Body

	// Verify updates
	assert.Equal(t, "Updated Name", updatedKey["name"])
	assert.False(t, updatedKey["enabled"].(bool))

	permissions := updatedKey["permissions"].([]interface{})
	assert.Len(t, permissions, 2)
	assert.Contains(t, permissions, "read")
	assert.Contains(t, permissions, "write")
}

func (suite *ShopIntegrationTestSuite) TestDeleteShopAPIKey_Success() {
	t := suite.T()

	// Create test API key first
	apiKey := &models.APIKey{
		ID:          uuid.New(),
		UserID:      suite.testUser.ID,
		ShopID:      &suite.testShop.ID,
		Name:        "To Delete",
		Key:         "sk_test_key_delete",
		Permissions: []string{"read"},
		Enabled:     true,
	}
	require.NoError(t, testutils.TestDB.Create(apiKey).Error)

	req := testutils.TestRequest{
		Method: "DELETE",
		URL:    fmt.Sprintf("/api/v1/shops/%s/apikeys/%s", suite.testShop.ID, apiKey.ID),
		Headers: map[string]string{
			"Authorization": "Bearer " + suite.authToken,
		},
	}

	resp := testutils.MakeRequest(t, suite.router, req)

	// Assert response
	testutils.AssertSuccessResponse(t, resp)
	assert.Equal(t, 200, resp.StatusCode)

	// Verify API key was deleted
	var deletedKey models.APIKey
	err := testutils.TestDB.First(&deletedKey, "id = ?", apiKey.ID).Error
	assert.Error(t, err) // Should not be found
}

// ============================================================================
// SHOP BRANCH API KEY MANAGEMENT TESTS
// ============================================================================

func (suite *ShopIntegrationTestSuite) TestCreateShopBranchAPIKey_Success() {
	t := suite.T()

	// Create test branch first
	branch := &models.ShopBranch{
		ID:     uuid.New(),
		ShopID: suite.testShop.ID,
		Name:   "Test Branch",
	}
	require.NoError(t, testutils.TestDB.Create(branch).Error)

	apiKeyData := map[string]interface{}{
		"name":        "Branch API Key",
		"permissions": []string{"read"},
	}

	req := testutils.TestRequest{
		Method: "POST",
		URL:    fmt.Sprintf("/api/v1/shops/branches/%s/apikeys", branch.ID),
		Headers: map[string]string{
			"Authorization": "Bearer " + suite.authToken,
		},
		Body: apiKeyData,
	}

	resp := testutils.MakeRequest(t, suite.router, req)

	// Assert response
	testutils.AssertSuccessResponse(t, resp)
	assert.Equal(t, 201, resp.StatusCode)

	// Parse response
	apiKey := resp.Body

	// Verify API key data
	assert.Equal(t, "Branch API Key", apiKey["name"])
	assert.True(t, apiKey["enabled"].(bool))
	assert.Contains(t, apiKey["key"].(string), "sk_")
	assert.Equal(t, suite.testUser.ID.String(), apiKey["user_id"])
	assert.Equal(t, suite.testShop.ID.String(), apiKey["shop_id"])
	assert.Equal(t, branch.ID.String(), apiKey["shop_branch_id"])
}

func (suite *ShopIntegrationTestSuite) TestGetShopBranchAPIKeys_Success() {
	t := suite.T()

	// Create test branch first
	branch := &models.ShopBranch{
		ID:     uuid.New(),
		ShopID: suite.testShop.ID,
		Name:   "Test Branch",
	}
	require.NoError(t, testutils.TestDB.Create(branch).Error)

	// Create test API keys for the branch
	for i := 0; i < 2; i++ {
		apiKey := &models.APIKey{
			ID:           uuid.New(),
			UserID:       suite.testUser.ID,
			ShopID:       &suite.testShop.ID,
			ShopBranchID: &branch.ID,
			Name:         fmt.Sprintf("Branch Key %d", i),
			Key:          fmt.Sprintf("sk_branch_key_%d", i),
			Permissions:  []string{"read"},
			Enabled:      true,
		}
		require.NoError(t, testutils.TestDB.Create(apiKey).Error)
	}

	req := testutils.TestRequest{
		Method: "GET",
		URL:    fmt.Sprintf("/api/v1/shops/branches/%s/apikeys", branch.ID),
		Headers: map[string]string{
			"Authorization": "Bearer " + suite.authToken,
		},
	}

	resp := testutils.MakeRequest(t, suite.router, req)

	// Assert response
	testutils.AssertSuccessResponse(t, resp)
	assert.Equal(t, 200, resp.StatusCode)

	// For now, just verify the response is successful
	// The actual API response structure will be determined during testing
	assert.Contains(t, resp.Body, "message") // or whatever the actual response contains
}

// ============================================================================
// ERROR HANDLING AND EDGE CASES
// ============================================================================

func (suite *ShopIntegrationTestSuite) TestShopAPIKey_InvalidShopID() {
	t := suite.T()

	req := testutils.TestRequest{
		Method: "GET",
		URL:    "/api/v1/shops/invalid-uuid/apikeys",
		Headers: map[string]string{
			"Authorization": "Bearer " + suite.authToken,
		},
	}

	resp := testutils.MakeRequest(t, suite.router, req)

	// Should return 400 for invalid UUID
	assert.Equal(t, 400, resp.StatusCode)
}

func (suite *ShopIntegrationTestSuite) TestShopAPIKey_AccessDenied() {
	t := suite.T()

	// Create another user and shop
	otherUser, _ := testutils.CreateTestUserWithAuth(t, "<EMAIL>", "Other User")
	defer testutils.CleanupTestUser(t, otherUser.ID)

	otherShop := &models.Shop{
		ID:          uuid.New(),
		OwnerUserID: otherUser.ID,
		Name:        "Other Shop",
		ShopType:    "retail",
		Slug:        "other-shop",
	}
	require.NoError(t, testutils.TestDB.Create(otherShop).Error)

	// Try to access other user's shop with our token
	req := testutils.TestRequest{
		Method: "GET",
		URL:    fmt.Sprintf("/api/v1/shops/%s/apikeys", otherShop.ID),
		Headers: map[string]string{
			"Authorization": "Bearer " + suite.authToken,
		},
	}

	resp := testutils.MakeRequest(t, suite.router, req)

	// Should return 404 (shop not found for this user)
	assert.Equal(t, 404, resp.StatusCode)
}

// Run the test suite
func TestShopIntegrationTestSuite(t *testing.T) {
	suite.Run(t, new(ShopIntegrationTestSuite))
}
