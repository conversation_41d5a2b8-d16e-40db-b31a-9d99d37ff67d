# Scheduled Credits Cloud Function

This Cloud Function is designed to automatically add credits to user subscriptions on a regular schedule. It is intended to be deployed on Google Cloud Platform (GCP) and triggered by Cloud Scheduler.

## Features

- Automatically adds credits to active subscriptions
- Creates transaction records for scheduled credit additions
- Sends webhook notifications when credits are added
- Secured with API key authentication
- Configurable schedule via Cloud Scheduler

## Deployment

### Prerequisites

- Google Cloud Platform account
- `gcloud` CLI installed and configured
- PostgreSQL database accessible from GCP
- API key for authentication

### Deployment Steps

1. Set up environment variables:

```bash
export PROJECT_ID="your-gcp-project-id"
export REGION="us-central1"
export SCHEDULER_API_KEY="your-secure-api-key"
export DATABASE_URL="postgresql://username:password@host:port/database"
```

2. Run the deployment script:

```bash
cd backend
chmod +x scripts/deploy_scheduled_credits.sh
./scripts/deploy_scheduled_credits.sh
```

This will:
- Deploy the Cloud Function to GCP
- Create a service account with necessary permissions
- Set up a Cloud Scheduler job to trigger the function daily

## Manual Testing

You can manually trigger the function using curl:

```bash
curl -X POST -H "X-API-Key: your-api-key" https://your-function-url
```

Or use the admin API endpoint:

```bash
curl -X POST -H "Authorization: Bearer your-admin-token" https://your-api-url/api/v1/admin/credits/scheduled/process
```

## Configuration

### Environment Variables

- `SCHEDULER_API_KEY`: API key for authenticating Cloud Scheduler requests
- `DATABASE_URL`: PostgreSQL connection string

### Cloud Scheduler

By default, the function is scheduled to run daily at midnight. You can modify the schedule using cron syntax:

```
# Run at midnight every day
0 0 * * *

# Run at 2 AM every Monday
0 2 * * 1

# Run at 15:30 on the first day of each month
30 15 1 * *
```

## Webhook Integration

When credits are added via the scheduled process, a webhook event `credit.scheduled` is triggered. You can subscribe to this event to receive notifications when scheduled credits are added.

Example webhook payload:

```json
{
  "id": "webhook-delivery-id",
  "event": "credit.scheduled",
  "created_at": "2023-06-01T00:00:00Z",
  "data": {
    "user_id": "user-uuid",
    "credits": 1000,
    "credit_balance": 2500,
    "transaction_id": "transaction-uuid",
    "subscription_id": "subscription-uuid",
    "description": "Scheduled credit addition for Pro subscription",
    "scheduled": true
  }
}
```

## Troubleshooting

- Check Cloud Function logs for errors
- Verify that the database connection is working
- Ensure the API key is correctly set in both the function and scheduler
- Check that the service account has the necessary permissions
