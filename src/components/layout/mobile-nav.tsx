"use client";

import { useState } from "react";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { cn } from "@/lib/utils";
import { But<PERSON> } from "@/components/ui/button";
import { Sheet, SheetContent, SheetTrigger } from "@/components/ui/sheet";
import { Menu } from "lucide-react";
import {
  LayoutDashboard,
  Key,
  BarChart3,
  CreditCard,
  Webhook,
  Settings,
  LineChart,
  Store,
} from "lucide-react";

export function MobileNav() {
  const [open, setOpen] = useState(false);
  const pathname = usePathname();

  const items = [
    {
      href: "/dashboard/merchant",
      title: "Overview",
      icon: <LayoutDashboard className="h-5 w-5" />,
    },
    {
      href: "/dashboard/api-keys",
      title: "API Keys",
      icon: <Key className="h-5 w-5" />,
    },
    {
      href: "/dashboard/usage",
      title: "Usage",
      icon: <BarChart3 className="h-5 w-5" />,
    },
    {
      href: "/dashboard/subscriptions",
      title: "Subscriptions",
      icon: <CreditCard className="h-5 w-5" />,
    },
    {
      href: "/dashboard/merchant",
      title: "Shops",
      icon: <Store className="h-5 w-5" />,
    },
    {
      href: "/dashboard/webhooks",
      title: "Webhooks",
      icon: <Webhook className="h-5 w-5" />,
    },
    {
      href: "/dashboard/analytics",
      title: "Analytics",
      icon: <LineChart className="h-5 w-5" />,
    },
    {
      href: "/dashboard/settings",
      title: "Settings",
      icon: <Settings className="h-5 w-5" />,
    },
  ];

  return (
    <Sheet open={open} onOpenChange={setOpen}>
      <SheetTrigger asChild>
        <Button
          variant="ghost"
          className="mr-2 px-0 text-base hover:bg-transparent focus-visible:bg-transparent focus-visible:ring-0 focus-visible:ring-offset-0 md:hidden"
        >
          <Menu className="h-6 w-6" />
          <span className="sr-only">Toggle Menu</span>
        </Button>
      </SheetTrigger>
      <SheetContent side="left" className="pr-0">
        <div className="px-7">
          <Link
            href="/"
            className="flex items-center"
            onClick={() => setOpen(false)}
          >
            <span className="font-bold text-lg">ADC Credit</span>
          </Link>
        </div>
        <div className="mt-8 px-4">
          <h2 className="text-lg font-semibold tracking-tight mb-4 px-4">Dashboard</h2>
          <nav className="flex flex-col space-y-1">
            {items.map((item) => (
              <Link
                key={item.href}
                href={item.href}
                onClick={() => setOpen(false)}
                className={cn(
                  "flex items-center gap-3 rounded-md px-4 py-2 text-sm font-medium transition-colors",
                  pathname === item.href
                    ? "bg-muted text-foreground"
                    : "text-muted-foreground hover:bg-muted hover:text-foreground"
                )}
              >
                {item.icon}
                {item.title}
              </Link>
            ))}
          </nav>
        </div>
      </SheetContent>
    </Sheet>
  );
}
