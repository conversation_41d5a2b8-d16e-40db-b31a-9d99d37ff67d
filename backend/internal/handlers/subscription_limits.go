package handlers

import (
	"net/http"

	"github.com/adc-credit/backend/internal/services"
	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

// SubscriptionLimitsHandler handles subscription limit-related requests
type SubscriptionLimitsHandler struct {
	guard *services.SubscriptionGuard
}

// NewSubscriptionLimitsHandler creates a new subscription limits handler
func NewSubscriptionLimitsHandler() *SubscriptionLimitsHandler {
	return &SubscriptionLimitsHandler{
		guard: services.NewSubscriptionGuard(),
	}
}

// GetUserLimits returns all subscription limits for the authenticated user
func (h *SubscriptionLimitsHandler) GetUserLimits(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	userUUID, ok := userID.(uuid.UUID)
	if !ok {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid user ID"})
		return
	}

	// Get user's active subscription
	subscription, err := h.guard.GetUserActiveSubscription(userUUID, "personal")
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "No active subscription found"})
		return
	}

	// Get all limit checks
	shopLimit, _ := h.guard.CheckShopLimit(userUUID)
	qrCodeLimit, _ := h.guard.CheckQRCodeLimit(userUUID)
	webhookLimit, _ := h.guard.CheckWebhookLimit(userUUID)
	creditBalance, _ := h.guard.CheckCreditBalance(userUUID, 0)
	analyticsHistoryDays, _ := h.guard.GetAnalyticsHistoryLimit(userUUID)
	supportLevel, _ := h.guard.GetSupportLevel(userUUID)

	response := gin.H{
		"subscription_tier": subscription.SubscriptionTier,
		"limits": gin.H{
			"shops": gin.H{
				"max":       subscription.SubscriptionTier.MaxShops,
				"current":   shopLimit.CurrentUsage,
				"unlimited": subscription.SubscriptionTier.UnlimitedShops,
				"allowed":   shopLimit.Allowed,
			},
			"customers_per_shop": gin.H{
				"max":       subscription.SubscriptionTier.MaxCustomersPerShop,
				"unlimited": subscription.SubscriptionTier.UnlimitedCustomers,
			},
			"api_keys_per_shop": gin.H{
				"max": subscription.SubscriptionTier.MaxAPIKeysPerShop,
			},
			"branches_per_shop": gin.H{
				"max":       subscription.SubscriptionTier.MaxBranchesPerShop,
				"unlimited": subscription.SubscriptionTier.UnlimitedBranches,
			},
			"qr_codes_per_month": gin.H{
				"max":       subscription.SubscriptionTier.MaxQRCodesPerMonth,
				"current":   qrCodeLimit.CurrentUsage,
				"unlimited": subscription.SubscriptionTier.UnlimitedQRCodes,
				"allowed":   qrCodeLimit.Allowed,
			},
			"webhooks": gin.H{
				"max":     subscription.SubscriptionTier.MaxWebhooks,
				"current": webhookLimit.CurrentUsage,
				"allowed": webhookLimit.Allowed,
			},
			"credits": gin.H{
				"limit":   subscription.SubscriptionTier.CreditLimit,
				"current": creditBalance.CurrentUsage,
			},
			"analytics_history_days": analyticsHistoryDays,
			"support_level":          supportLevel,
			"allowed_shop_types":     subscription.SubscriptionTier.AllowedShopTypes,
		},
	}

	c.JSON(http.StatusOK, response)
}

// CheckShopLimit checks if user can create more shops
func (h *SubscriptionLimitsHandler) CheckShopLimit(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	userUUID, ok := userID.(uuid.UUID)
	if !ok {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid user ID"})
		return
	}

	result, err := h.guard.CheckShopLimit(userUUID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to check shop limit"})
		return
	}

	c.JSON(http.StatusOK, result)
}

// CheckCustomerLimit checks if shop can add more customers
func (h *SubscriptionLimitsHandler) CheckCustomerLimit(c *gin.Context) {
	shopIDStr := c.Param("shopId")
	shopID, err := uuid.Parse(shopIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid shop ID"})
		return
	}

	result, err := h.guard.CheckCustomerLimit(shopID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to check customer limit"})
		return
	}

	c.JSON(http.StatusOK, result)
}

// CheckAPIKeyLimit checks if shop can create more API keys
func (h *SubscriptionLimitsHandler) CheckAPIKeyLimit(c *gin.Context) {
	shopIDStr := c.Param("shopId")
	shopID, err := uuid.Parse(shopIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid shop ID"})
		return
	}

	result, err := h.guard.CheckAPIKeyLimit(shopID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to check API key limit"})
		return
	}

	c.JSON(http.StatusOK, result)
}

// CheckBranchLimit checks if shop can create more branches
func (h *SubscriptionLimitsHandler) CheckBranchLimit(c *gin.Context) {
	shopIDStr := c.Param("shopId")
	shopID, err := uuid.Parse(shopIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid shop ID"})
		return
	}

	result, err := h.guard.CheckBranchLimit(shopID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to check branch limit"})
		return
	}

	c.JSON(http.StatusOK, result)
}

// CheckQRCodeLimit checks if user can generate more QR codes
func (h *SubscriptionLimitsHandler) CheckQRCodeLimit(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	userUUID, ok := userID.(uuid.UUID)
	if !ok {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid user ID"})
		return
	}

	result, err := h.guard.CheckQRCodeLimit(userUUID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to check QR code limit"})
		return
	}

	c.JSON(http.StatusOK, result)
}

// CheckWebhookLimit checks if user can create more webhooks
func (h *SubscriptionLimitsHandler) CheckWebhookLimit(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	userUUID, ok := userID.(uuid.UUID)
	if !ok {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid user ID"})
		return
	}

	result, err := h.guard.CheckWebhookLimit(userUUID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to check webhook limit"})
		return
	}

	c.JSON(http.StatusOK, result)
}

// CheckShopTypeAllowed checks if user can create shop of specific type
func (h *SubscriptionLimitsHandler) CheckShopTypeAllowed(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	userUUID, ok := userID.(uuid.UUID)
	if !ok {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid user ID"})
		return
	}

	shopType := c.Query("shop_type")
	if shopType == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Shop type is required"})
		return
	}

	result, err := h.guard.CheckShopTypeAllowed(userUUID, shopType)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to check shop type permission"})
		return
	}

	c.JSON(http.StatusOK, result)
}

// CheckCreditBalance checks if user has enough credits
func (h *SubscriptionLimitsHandler) CheckCreditBalance(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	userUUID, ok := userID.(uuid.UUID)
	if !ok {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid user ID"})
		return
	}

	// Get required credits from query parameter (default to 0 for balance check)
	requiredCredits := 0
	if creditsStr := c.Query("required_credits"); creditsStr != "" {
		if parsed, err := uuid.Parse(creditsStr); err == nil {
			requiredCredits = int(parsed.ID())
		}
	}

	result, err := h.guard.CheckCreditBalance(userUUID, requiredCredits)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to check credit balance"})
		return
	}

	c.JSON(http.StatusOK, result)
}
