#!/bin/bash

# Test script for the merchant credit stats endpoint
# Usage: ./test_credit_stats.sh [JWT_TOKEN]

# Configuration
BASE_URL="http://localhost:8400"
ENDPOINT="/api/v1/merchant/credit-stats"

# Get JWT token from parameter or environment
JWT_TOKEN=${1:-$JWT_TOKEN}

if [ -z "$JWT_TOKEN" ]; then
    echo "Error: JWT token is required"
    echo "Usage: $0 <JWT_TOKEN>"
    echo "Or set JWT_TOKEN environment variable"
    exit 1
fi

echo "Testing Merchant Credit Stats Endpoint"
echo "======================================"
echo "URL: ${BASE_URL}${ENDPOINT}"
echo "Method: GET"
echo ""

# Make the API request
response=$(curl -s -w "\nHTTP_STATUS:%{http_code}" \
    -H "Authorization: Bearer $JWT_TOKEN" \
    -H "Content-Type: application/json" \
    "${BASE_URL}${ENDPOINT}")

# Extract HTTP status and response body
http_status=$(echo "$response" | grep "HTTP_STATUS:" | cut -d: -f2)
response_body=$(echo "$response" | sed '/HTTP_STATUS:/d')

echo "HTTP Status: $http_status"
echo "Response:"
echo "$response_body" | jq . 2>/dev/null || echo "$response_body"

# Check if the request was successful
if [ "$http_status" = "200" ]; then
    echo ""
    echo "✅ Success! The endpoint is working correctly."
else
    echo ""
    echo "❌ Error: HTTP $http_status"
    
    case $http_status in
        401)
            echo "   - Check if your JWT token is valid"
            echo "   - Make sure you're authenticated"
            ;;
        404)
            echo "   - The endpoint route is not registered"
            echo "   - Check if the server is running"
            ;;
        500)
            echo "   - Internal server error"
            echo "   - Check server logs for details"
            ;;
    esac
fi
