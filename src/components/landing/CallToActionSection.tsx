export const CallToActionSection = () => {
  return (
    <section className="@container py-10 md:py-16 lg:py-20 bg-accent rounded-xl border border-primary shadow-xl">
      <div className="flex flex-col justify-center items-center gap-6 px-6 md:px-10 text-center">
        <h2 className="text-foreground text-3xl md:text-4xl lg:text-5xl font-bold leading-tight tracking-tight max-w-2xl">
          Ready to Take Control of Your APIs?
        </h2>
        <p className="text-muted-foreground text-base md:text-lg leading-relaxed max-w-xl">
          Join thousands of developers who trust API Manager to streamline their workflows and scale their applications.
        </p>
        <div className="flex justify-center mt-4">
          <button className="flex min-w-[150px] max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-lg h-12 px-6 md:h-14 md:px-8 bg-primary hover:bg-primary/90 text-primary-foreground text-base md:text-lg font-semibold leading-normal tracking-[0.015em] transition-colors shadow-lg hover:shadow-xl transform hover:scale-105">
            <span className="truncate">Sign Up Now - It&apos;s Free!</span>
          </button>
        </div>
      </div>
    </section>
  );
};
