import { Session } from 'next-auth';

/**
 * Session management utilities for the application
 * This file centralizes all session-related functionality
 */

/**
 * Gets the access token from the session
 * @param session The NextAuth session object
 * @returns The access token or null if not found
 */
export function getAccessToken(session: Session | null): string | null {
  if (!session) return null;
  
  // Check if token is in session.accessToken
  if (session.accessToken) {
    return session.accessToken;
  }
  
  // Check if token is in session.user.accessToken
  if (session.user && 'accessToken' in session.user) {
    return session.user.accessToken as string;
  }
  
  // No token found
  return null;
}

/**
 * Stores a session token in browser storage
 * @param token The access token to store
 * @param refreshToken Optional refresh token
 * @param user Optional user data
 */
export function storeSessionToken(token: string, refreshToken?: string, user?: any): void {
  // Store in sessionStorage (for current session)
  sessionStorage.setItem('accessToken', token);
  
  if (refreshToken) {
    sessionStorage.setItem('refreshToken', refreshToken);
  }
  
  if (user) {
    sessionStorage.setItem('currentUser', JSON.stringify(user));
  }
  
  // Also store in localStorage for persistence
  localStorage.setItem('accessToken', token);
  
  if (refreshToken) {
    localStorage.setItem('refreshToken', refreshToken);
  }
  
  if (user) {
    localStorage.setItem('currentUser', JSON.stringify(user));
  }
}

/**
 * Retrieves a session token from browser storage
 * @returns The stored token or null if not found
 */
export function getStoredSessionToken(): string | null {
  // Try sessionStorage first (current session)
  const sessionToken = sessionStorage.getItem('accessToken');
  if (sessionToken) {
    return sessionToken;
  }
  
  // Fall back to localStorage (persistent)
  const localToken = localStorage.getItem('accessToken');
  return localToken;
}

/**
 * Retrieves the current user from browser storage
 * @returns The stored user object or null if not found
 */
export function getStoredUser(): any | null {
  // Try sessionStorage first
  const sessionUser = sessionStorage.getItem('currentUser');
  if (sessionUser) {
    try {
      return JSON.parse(sessionUser);
    } catch (e) {
      console.error('Failed to parse user from sessionStorage', e);
    }
  }
  
  // Fall back to localStorage
  const localUser = localStorage.getItem('currentUser');
  if (localUser) {
    try {
      return JSON.parse(localUser);
    } catch (e) {
      console.error('Failed to parse user from localStorage', e);
    }
  }
  
  return null;
}

/**
 * Clears all session data from browser storage
 */
export function clearSessionData(): void {
  // Clear from sessionStorage
  sessionStorage.removeItem('accessToken');
  sessionStorage.removeItem('refreshToken');
  sessionStorage.removeItem('currentUser');
  
  // Clear from localStorage
  localStorage.removeItem('accessToken');
  localStorage.removeItem('refreshToken');
  localStorage.removeItem('currentUser');
}

/**
 * Checks if the user is authenticated based on stored tokens
 * @returns True if authenticated, false otherwise
 */
export function isAuthenticated(): boolean {
  return !!getStoredSessionToken();
}

/**
 * Exchanges a Google OAuth token for a backend JWT token
 * @param googleToken The Google OAuth token
 * @returns Promise resolving to the backend token response
 */
export async function exchangeGoogleToken(googleToken: string): Promise<any> {
  const apiUrl = process.env.NEXT_PUBLIC_BACKEND_URL || 'http://localhost:8400';
  
  const response = await fetch(`${apiUrl}/api/v1/auth/google`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({ token: googleToken }),
  });
  
  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(errorData.error || 'Failed to exchange token');
  }
  
  return response.json();
}
