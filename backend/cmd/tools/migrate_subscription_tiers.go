package main

import (
	"log"

	"github.com/adc-credit/backend/internal/database"
	"github.com/adc-credit/backend/internal/models"
	"github.com/joho/godotenv"
)

func main() {
	// Load environment variables
	if err := godotenv.Load(); err != nil {
		log.Println("No .env file found, using environment variables")
	}

	// Initialize database
	database.InitDB()

	// Run migration
	if err := migrateSubscriptionTiers(); err != nil {
		log.Fatalf("Failed to migrate subscription tiers: %v", err)
	}

	log.Println("Successfully migrated subscription tiers")
}

func migrateSubscriptionTiers() error {
	db := database.DB

	// Auto-migrate the SubscriptionTier model to add new fields
	if err := db.AutoMigrate(&models.SubscriptionTier{}); err != nil {
		return err
	}

	log.Println("Added new fields to subscription_tiers table")

	// Update existing tiers with default values if they exist
	var existingTiers []models.SubscriptionTier
	if err := db.Find(&existingTiers).Error; err != nil {
		return err
	}

	for _, tier := range existingTiers {
		// Set default values for new fields based on tier name
		switch tier.Name {
		case "Free", "Starter":
			tier.MaxShops = 1
			tier.MaxCustomersPerShop = 50
			tier.MaxAPIKeysPerShop = 1
			tier.MaxBranchesPerShop = 0
			tier.MaxQRCodesPerMonth = 500
			tier.AnalyticsHistoryDays = 30
			tier.SupportLevel = "community"
			tier.AllowedShopTypes = models.StringSlice{"retail"}
			tier.UnlimitedShops = false
			tier.UnlimitedCustomers = false
			tier.UnlimitedQRCodes = false
			tier.UnlimitedBranches = false

		case "Pro", "Business":
			tier.MaxShops = 5
			tier.MaxCustomersPerShop = 1000
			tier.MaxAPIKeysPerShop = 5
			tier.MaxBranchesPerShop = 10
			tier.MaxQRCodesPerMonth = 2000
			tier.AnalyticsHistoryDays = 365
			tier.SupportLevel = "email"
			tier.AllowedShopTypes = models.StringSlice{"retail", "api_service"}
			tier.UnlimitedShops = false
			tier.UnlimitedCustomers = false
			tier.UnlimitedQRCodes = false
			tier.UnlimitedBranches = false

		case "Enterprise":
			tier.MaxShops = 0 // Will be unlimited
			tier.MaxCustomersPerShop = 0 // Will be unlimited
			tier.MaxAPIKeysPerShop = 20
			tier.MaxBranchesPerShop = 0 // Will be unlimited
			tier.MaxQRCodesPerMonth = 0 // Will be unlimited
			tier.AnalyticsHistoryDays = 0 // Unlimited
			tier.SupportLevel = "priority"
			tier.AllowedShopTypes = models.StringSlice{"retail", "api_service", "enterprise"}
			tier.UnlimitedShops = true
			tier.UnlimitedCustomers = true
			tier.UnlimitedQRCodes = true
			tier.UnlimitedBranches = true

		default:
			// Default values for unknown tiers
			tier.MaxShops = 1
			tier.MaxCustomersPerShop = 50
			tier.MaxAPIKeysPerShop = 1
			tier.MaxBranchesPerShop = 0
			tier.MaxQRCodesPerMonth = 100
			tier.AnalyticsHistoryDays = 30
			tier.SupportLevel = "community"
			tier.AllowedShopTypes = models.StringSlice{"retail"}
			tier.UnlimitedShops = false
			tier.UnlimitedCustomers = false
			tier.UnlimitedQRCodes = false
			tier.UnlimitedBranches = false
		}

		// Update the tier
		if err := db.Save(&tier).Error; err != nil {
			log.Printf("Failed to update tier %s: %v", tier.Name, err)
			continue
		}

		log.Printf("Updated tier: %s", tier.Name)
	}

	return nil
}
