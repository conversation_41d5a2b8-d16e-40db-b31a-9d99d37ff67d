"use client";

import * as React from "react";
import { ReactNode } from "react";
import Link from "next/link";
import { usePathname } from "next/navigation";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { ChevronRight, Home } from "lucide-react";

interface BreadcrumbSegment {
  label: string;
  href: string;
  isCurrentPage?: boolean;
}

interface BreadcrumbsProps {
  segments?: BreadcrumbSegment[];
  homeHref?: string;
  homeLabel?: string;
  className?: string;
  separator?: ReactNode;
}

/**
 * Converts a pathname into breadcrumb segments
 * e.g. /dashboard/merchant/123/customers -> ['dashboard', 'merchant', '123', 'customers']
 */
const getDefaultSegments = (pathname: string): BreadcrumbSegment[] => {
  // Skip empty segments
  const pathSegments = pathname.split("/").filter(Boolean);

  // Build up the breadcrumb segments with proper hrefs
  return pathSegments.map((segment, index) => {
    // Build the href for this segment
    const href = "/" + pathSegments.slice(0, index + 1).join("/");

    // Format the label (capitalize first letter, replace hyphens with spaces)
    let label = segment
      .replace(/-/g, " ")
      .replace(/^\w/, (c) => c.toUpperCase());

    // Check if this is a UUID (simple check for length and dashes)
    if (segment.length > 30 && segment.includes("-")) {
      label = "Details";
    }

    return {
      label,
      href,
      isCurrentPage: index === pathSegments.length - 1,
    };
  });
};

export function Breadcrumbs({
  segments,
  homeHref = "/dashboard",
  homeLabel = "Dashboard",
  className = "",
  separator = <ChevronRight className="h-4 w-4" />,
}: BreadcrumbsProps) {
  const pathname = usePathname();

  // Use provided segments or generate from pathname
  const breadcrumbSegments = segments || getDefaultSegments(pathname);

  // Don't show breadcrumbs on the home page
  if (pathname === "/" || pathname === homeHref) {
    return null;
  }

  return (
    <Breadcrumb className={`mb-4 ${className}`}>
      <BreadcrumbList>
        {/* Home link */}
        <BreadcrumbItem key="home">
          <BreadcrumbLink asChild>
            <Link href={homeHref} className="flex items-center">
              <Home className="h-4 w-4 mr-1" />
              <span className="hidden sm:inline">{homeLabel}</span>
            </Link>
          </BreadcrumbLink>
        </BreadcrumbItem>

        <BreadcrumbSeparator key="home-separator">{separator}</BreadcrumbSeparator>

        {/* Path segments */}
        {breadcrumbSegments.flatMap((segment, index) => {
          // Create an array of elements for each segment
          const elements = [
            // The breadcrumb item
            <BreadcrumbItem key={`item-${segment.href}`}>
              {index < breadcrumbSegments.length - 1 ? (
                <BreadcrumbLink asChild>
                  <Link href={segment.href}>{segment.label}</Link>
                </BreadcrumbLink>
              ) : (
                <BreadcrumbPage>{segment.label}</BreadcrumbPage>
              )}
            </BreadcrumbItem>
          ];

          // Add separator if not the last item
          if (index < breadcrumbSegments.length - 1) {
            elements.push(
              <BreadcrumbSeparator key={`separator-${segment.href}`}>
                {separator}
              </BreadcrumbSeparator>
            );
          }

          return elements;
        })}
      </BreadcrumbList>
    </Breadcrumb>
  );
}

/**
 * Merchant-specific breadcrumbs with custom styling
 */
export function MerchantBreadcrumbs({
  segments,
  className = "",
}: BreadcrumbsProps) {
  return (
    <Breadcrumbs
      segments={segments}
      homeHref="/dashboard/merchant"
      homeLabel="Dashboard"
      className={`px-4 text-[#8a745c] text-sm ${className}`}
      separator={<ChevronRight className="h-3 w-3 text-[#8a745c]" />}
    />
  );
}

/**
 * Customer-specific breadcrumbs with custom styling
 */
export function CustomerBreadcrumbs({
  segments,
  className = "",
}: BreadcrumbsProps) {
  return (
    <Breadcrumbs
      segments={segments}
      homeHref="/dashboard/customer"
      homeLabel="Home"
      className={`px-4 text-[#8a745c] text-sm ${className}`}
      separator={<ChevronRight className="h-3 w-3 text-[#8a745c]" />}
    />
  );
}
