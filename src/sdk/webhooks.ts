/**
 * ADC Credit SDK Webhooks Module
 */
import { ApiClient } from './client';
import { 
  APIResponse, 
  CreateWebhookRequest, 
  UpdateWebhookRequest, 
  Webhook, 
  WebhookDelivery 
} from './types';

/**
 * Webhooks module for the ADC Credit SDK
 */
export class Webhooks {
  private client: ApiClient;

  /**
   * Creates a new Webhooks instance
   * @param client API client
   */
  constructor(client: ApiClient) {
    this.client = client;
  }

  /**
   * Gets all webhooks for the current user
   * @returns List of webhooks
   */
  public async getAll(): Promise<APIResponse<Webhook[]>> {
    return this.client.get<Webhook[]>('/webhooks');
  }

  /**
   * Gets a specific webhook
   * @param id Webhook ID
   * @returns Webhook
   */
  public async get(id: string): Promise<APIResponse<Webhook>> {
    return this.client.get<Webhook>(`/webhooks/${id}`);
  }

  /**
   * Creates a new webhook
   * @param data Webhook data
   * @returns Created webhook
   */
  public async create(data: CreateWebhookRequest): Promise<APIResponse<Webhook>> {
    return this.client.post<Webhook>('/webhooks', data);
  }

  /**
   * Updates a webhook
   * @param id Webhook ID
   * @param data Webhook data to update
   * @returns Updated webhook
   */
  public async update(id: string, data: UpdateWebhookRequest): Promise<APIResponse<Webhook>> {
    return this.client.put<Webhook>(`/webhooks/${id}`, data);
  }

  /**
   * Deletes a webhook
   * @param id Webhook ID
   * @returns Success message
   */
  public async delete(id: string): Promise<APIResponse<{ message: string }>> {
    return this.client.delete<{ message: string }>(`/webhooks/${id}`);
  }

  /**
   * Gets webhook deliveries
   * @param id Webhook ID
   * @param params Query parameters
   * @returns List of webhook deliveries
   */
  public async getDeliveries(
    id: string,
    params?: {
      limit?: number;
      offset?: number;
      start_date?: string;
      end_date?: string;
      success?: boolean;
    }
  ): Promise<APIResponse<WebhookDelivery[]>> {
    // Convert params to query string
    const queryParams = new URLSearchParams();
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined) {
          queryParams.append(key, value.toString());
        }
      });
    }
    
    const queryString = queryParams.toString();
    const endpoint = queryString 
      ? `/webhooks/${id}/deliveries?${queryString}` 
      : `/webhooks/${id}/deliveries`;
    
    return this.client.get<WebhookDelivery[]>(endpoint);
  }
}
