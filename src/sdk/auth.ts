/**
 * ADC Credit SDK Authentication Module
 */
import { ApiClient } from './client';
import { APIResponse } from './types';

/**
 * Authentication response from the API
 */
interface AuthResponse {
  token: string;
  user: {
    id: string;
    email: string;
    name: string;
    picture: string;
    role: string;
  };
}

/**
 * Refresh token response from the API
 */
interface RefreshTokenResponse {
  token: string;
}

/**
 * Authentication module for the ADC Credit SDK
 */
export class Auth {
  private client: ApiClient;

  /**
   * Creates a new Auth instance
   * @param client API client
   */
  constructor(client: ApiClient) {
    this.client = client;
  }

  /**
   * Logs in with email and password
   * @param email User email
   * @param password User password
   * @returns Authentication response
   */
  public async login(email: string, password: string): Promise<APIResponse<AuthResponse>> {
    return this.client.post<AuthResponse>('/auth/login', {
      email,
      password,
    });
  }

  /**
   * Logs in with Google
   * @param googleToken Google OAuth token
   * @returns Authentication response
   */
  public async loginWithGoogle(googleToken: string): Promise<APIResponse<AuthResponse>> {
    return this.client.post<AuthResponse>('/auth/google', {
      token: googleToken,
    });
  }

  /**
   * Refreshes the authentication token
   * @param refreshToken Refresh token
   * @returns New authentication token
   */
  public async refreshToken(refreshToken: string): Promise<APIResponse<RefreshTokenResponse>> {
    return this.client.post<RefreshTokenResponse>('/auth/refresh', {
      refresh_token: refreshToken,
    });
  }

  /**
   * Registers a new user
   * @param email User email
   * @param password User password
   * @param name User name
   * @returns Authentication response
   */
  public async register(
    email: string,
    password: string,
    name: string
  ): Promise<APIResponse<AuthResponse>> {
    return this.client.post<AuthResponse>('/auth/register', {
      email,
      password,
      name,
    });
  }

  /**
   * Sends a password reset email
   * @param email User email
   * @returns Success message
   */
  public async forgotPassword(email: string): Promise<APIResponse<{ message: string }>> {
    return this.client.post<{ message: string }>('/auth/forgot-password', {
      email,
    });
  }

  /**
   * Resets a user's password
   * @param token Reset token
   * @param password New password
   * @returns Success message
   */
  public async resetPassword(
    token: string,
    password: string
  ): Promise<APIResponse<{ message: string }>> {
    return this.client.post<{ message: string }>('/auth/reset-password', {
      token,
      password,
    });
  }

  /**
   * Gets the current user
   * @returns Current user
   */
  public async getCurrentUser(): Promise<APIResponse<{ id: string; email: string; name: string; picture: string; role: string }>> {
    return this.client.get<{ id: string; email: string; name: string; picture: string; role: string }>('/users/me');
  }

  /**
   * Updates the current user
   * @param data User data to update
   * @returns Updated user
   */
  public async updateCurrentUser(data: {
    name?: string;
    picture?: string;
  }): Promise<APIResponse<{ id: string; email: string; name: string; picture: string; role: string }>> {
    return this.client.put<{ id: string; email: string; name: string; picture: string; role: string }>('/users/me', data);
  }
}
