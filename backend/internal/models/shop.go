package models

import (
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// Shop represents any business entity (retail, API service, enterprise)
type Shop struct {
	ID          uuid.UUID `gorm:"type:uuid;primary_key;default:gen_random_uuid()" json:"id"`
	Slug        string    `json:"slug"`
	Name        string    `json:"name"`
	Description string    `json:"description"`
	ShopType    string    `gorm:"default:retail" json:"shop_type"` // "retail", "api_service", "enterprise"

	// Contact Information (from MerchantShop)
	ContactEmail string `json:"contact_email"`
	ContactPhone string `json:"contact_phone"`

	// Ownership
	OwnerUserID uuid.UUID `gorm:"type:uuid;not null" json:"owner_user_id"`
	Owner       User      `json:"-" gorm:"foreignKey:OwnerUserID"`

	// Hierarchical Structure
	Branches []ShopBranch `json:"branches,omitempty" gorm:"foreignKey:ShopID"`

	// Relationships
	Customers   []ShopCustomer `json:"customers,omitempty" gorm:"foreignKey:ShopID"`
	CreditCodes []CreditCode   `json:"credit_codes,omitempty" gorm:"foreignKey:ShopID"`
	APIKeys     []APIKey       `json:"api_keys,omitempty" gorm:"foreignKey:ShopID"`

	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `gorm:"index" json:"-"`
}

// ShopBranch represents a division within a shop (department, location, etc.)
type ShopBranch struct {
	ID          uuid.UUID `gorm:"type:uuid;primary_key;default:gen_random_uuid()" json:"id"`
	ShopID      uuid.UUID `gorm:"type:uuid;not null" json:"shop_id"`
	Shop        Shop      `json:"-" gorm:"foreignKey:ShopID"`
	Name        string    `json:"name"`
	Description string    `json:"description"`
	BranchType  string    `gorm:"default:location" json:"branch_type"` // "department", "location", "division"

	// Contact Information (for physical locations)
	ContactEmail string `json:"contact_email"`
	ContactPhone string `json:"contact_phone"`
	Address      string `json:"address"`

	// Relationships
	Users   []User   `json:"users,omitempty" gorm:"foreignKey:ShopBranchID"`
	APIKeys []APIKey `json:"api_keys,omitempty" gorm:"foreignKey:ShopBranchID"`

	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `gorm:"index" json:"-"`
}

// Note: ShopCustomer, CreditCode, and ShopCreditTransaction models are temporarily
// kept in merchant.go to avoid conflicts during migration. They will be moved here
// after the migration is complete.
