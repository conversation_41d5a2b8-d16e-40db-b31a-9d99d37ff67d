import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import Link from "next/link";

export default function DocsPage() {
  return (
    <div className="w-full">
      <main className="container py-10">
        <h1 className="text-3xl font-bold mb-8">API Documentation</h1>

        <Tabs defaultValue="overview" className="w-full">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="authentication">Authentication</TabsTrigger>
            <TabsTrigger value="integration">Integration</TabsTrigger>
            <TabsTrigger value="examples">Examples</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="mt-6">
            <Card>
              <CardHeader>
                <CardTitle>API Overview</CardTitle>
                <CardDescription>
                  Understanding the ADC Credit System API
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <p>
                  The ADC Credit System provides a simple and secure way to integrate credit-based API access into your applications. The system allows you to:
                </p>
                <ul className="list-disc pl-6 space-y-2">
                  <li>Generate and manage API keys for your applications</li>
                  <li>Track API usage and credit consumption</li>
                  <li>Set limits based on subscription tiers</li>
                  <li>Integrate with any application using our RESTful API</li>
                </ul>

                <h3 className="text-lg font-semibold mt-6">Base URL</h3>
                <p className="font-mono bg-gray-100 dark:bg-gray-800 p-2 rounded">
                  https://api.example.com/v1
                </p>

                <h3 className="text-lg font-semibold mt-6">Response Format</h3>
                <p>
                  All API responses are returned in JSON format. Successful responses will have a 2xx status code, while errors will have a 4xx or 5xx status code.
                </p>

                <div className="bg-gray-100 dark:bg-gray-800 p-4 rounded-lg mt-4">
                  <p className="font-mono text-sm">
                    {`// Success response example
{
  "message": "API key verified successfully",
  "valid": true,
  "credit_balance": 8500
}`}
                  </p>
                </div>

                <div className="bg-gray-100 dark:bg-gray-800 p-4 rounded-lg mt-4">
                  <p className="font-mono text-sm">
                    {`// Error response example
{
  "error": "Invalid API key"
}`}
                  </p>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="authentication" className="mt-6">
            <Card>
              <CardHeader>
                <CardTitle>Authentication</CardTitle>
                <CardDescription>
                  How to authenticate with the ADC Credit System API
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <p>
                  The ADC Credit System API uses API keys for authentication. You can generate API keys from your dashboard.
                </p>

                <h3 className="text-lg font-semibold mt-6">API Key Authentication</h3>
                <p>
                  To authenticate API requests, include your API key in the <code>X-API-Key</code> header:
                </p>

                <div className="bg-gray-100 dark:bg-gray-800 p-4 rounded-lg mt-4">
                  <p className="font-mono text-sm">
                    {`curl -X POST https://api.example.com/v1/external/consume \\
  -H "Content-Type: application/json" \\
  -H "X-API-Key: your_api_key" \\
  -d '{"endpoint": "/api/data", "method": "GET", "credits": 1}'`}
                  </p>
                </div>

                <h3 className="text-lg font-semibold mt-6">API Key Security</h3>
                <p>
                  Keep your API keys secure and never expose them in client-side code. If you believe an API key has been compromised, you should immediately disable it and create a new one.
                </p>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="integration" className="mt-6">
            <Card>
              <CardHeader>
                <CardTitle>Integration Guide</CardTitle>
                <CardDescription>
                  How to integrate the ADC Credit System with your application
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <h3 className="text-lg font-semibold">Step 1: Generate an API Key</h3>
                <p>
                  Create an API key from your dashboard. Make sure to save the key securely as it will only be shown once.
                </p>

                <h3 className="text-lg font-semibold mt-6">Step 2: Verify API Key</h3>
                <p>
                  Before performing operations, verify that the API key is valid and has enough credits:
                </p>

                <div className="bg-gray-100 dark:bg-gray-800 p-4 rounded-lg mt-4">
                  <p className="font-mono text-sm">
                    {`// Request
POST /external/verify
{
  "api_key": "your_api_key",
  "credits": 1
}

// Response
{
  "valid": true,
  "credit_balance": 8500
}`}
                  </p>
                </div>

                <h3 className="text-lg font-semibold mt-6">Step 3: Consume Credits</h3>
                <p>
                  When performing an operation that should consume credits, make a request to the consume endpoint:
                </p>

                <div className="bg-gray-100 dark:bg-gray-800 p-4 rounded-lg mt-4">
                  <p className="font-mono text-sm">
                    {`// Request
POST /external/consume
Headers: X-API-Key: your_api_key
{
  "endpoint": "/api/data",
  "method": "GET",
  "credits": 1,
  "ip_address": "***********",  // Optional
  "user_agent": "MyApp/1.0"      // Optional
}

// Response
{
  "message": "Credits consumed successfully",
  "credit_balance": 8499,
  "usage": {
    "id": "123e4567-e89b-12d3-a456-426614174000",
    "api_key_id": "123e4567-e89b-12d3-a456-426614174000",
    "endpoint": "/api/data",
    "method": "GET",
    "credits": 1,
    "timestamp": "2025-05-18T12:00:00Z",
    "success": true,
    "ip_address": "***********",
    "user_agent": "MyApp/1.0"
  }
}`}
                  </p>
                </div>

                <h3 className="text-lg font-semibold mt-6">Step 4: Handle Errors</h3>
                <p>
                  Be sure to handle error responses, especially for insufficient credits:
                </p>

                <div className="bg-gray-100 dark:bg-gray-800 p-4 rounded-lg mt-4">
                  <p className="font-mono text-sm">
                    {`// Insufficient credits response
{
  "error": "Insufficient credits",
  "credit_balance": 0,
  "required": 1
}`}
                  </p>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="examples" className="mt-6">
            <Card>
              <CardHeader>
                <CardTitle>Code Examples</CardTitle>
                <CardDescription>
                  Example code for integrating with the ADC Credit System
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="bg-gray-50 dark:bg-gray-800 p-6 rounded-lg border border-gray-200 dark:border-gray-700">
                  <h3 className="text-lg font-semibold mb-2">Interactive Code Examples</h3>
                  <p className="mb-4">
                    We've created a dedicated page with interactive code examples using our new code block component.
                    These examples show how to integrate with our API in various programming languages.
                  </p>
                  <Link href="/docs/api-examples">
                    <Button>View Interactive Examples</Button>
                  </Link>
                </div>

                <h3 className="text-lg font-semibold">Basic Examples</h3>

                <div className="space-y-4">
                  <div>
                    <h4 className="font-medium mb-2">JavaScript</h4>
                    <div className="bg-gray-100 dark:bg-gray-800 p-4 rounded-lg">
                      <p className="font-mono text-sm">
                        {`// Consume credits example
const response = await fetch('https://api.example.com/v1/external/consume', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'X-API-Key': 'your_api_key'
  },
  body: JSON.stringify({
    endpoint: '/api/data',
    method: 'GET',
    credits: 1
  })
});`}
                      </p>
                    </div>
                  </div>

                  <div>
                    <h4 className="font-medium mb-2">Python</h4>
                    <div className="bg-gray-100 dark:bg-gray-800 p-4 rounded-lg">
                      <p className="font-mono text-sm">
                        {`# Consume credits example
import requests

response = requests.post(
    'https://api.example.com/v1/external/consume',
    headers={
        'X-API-Key': 'your_api_key'
    },
    json={
        'endpoint': '/api/data',
        'method': 'GET',
        'credits': 1
    }
)`}
                      </p>
                    </div>
                  </div>

                  <div>
                    <h4 className="font-medium mb-2">cURL</h4>
                    <div className="bg-gray-100 dark:bg-gray-800 p-4 rounded-lg">
                      <p className="font-mono text-sm">
                        {`curl -X POST https://api.example.com/v1/external/consume \\
  -H "Content-Type: application/json" \\
  -H "X-API-Key: your_api_key" \\
  -d '{"endpoint": "/api/data", "method": "GET", "credits": 1}'`}
                      </p>
                    </div>
                  </div>
                </div>

                <div className="mt-6">
                  <h3 className="text-lg font-semibold mb-2">New Features</h3>
                  <ul className="list-disc pl-6 space-y-2">
                    <li>
                      <strong>Rate Limiting</strong> - Our API now includes customizable rate limiting based on subscription tier
                    </li>
                    <li>
                      <strong>Webhooks</strong> - Receive real-time notifications for credit consumption and other events
                    </li>
                    <li>
                      <strong>Advanced Analytics</strong> - Detailed usage statistics and performance metrics
                    </li>
                  </ul>
                  <p className="mt-4">
                    See our <Link href="/docs/api-examples" className="text-primary underline">interactive examples</Link> for detailed implementation guides.
                  </p>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </main>
    </div>
  );
}
