'use client';

import Link from 'next/link';
import { useAppSession } from '@/lib/session';

export const Header = () => {
  const { isAuthenticated, clearSession } = useAppSession();

  return (
    <header className="flex items-center justify-between whitespace-nowrap border-b border-solid border-b-[#283039] px-10 py-5 sticky top-0 z-50 bg-[#111418]/80 backdrop-blur-md">
      <div className="flex items-center gap-4 text-white">
        <div className="size-7 text-[#0c7ff2]">
          <svg fill="none" viewBox="0 0 48 48" xmlns="http://www.w3.org/2000/svg">
            <path clipRule="evenodd" d="M12.0799 24L4 19.2479L9.95537 8.75216L18.04 13.4961L18.0446 4H29.9554L29.96 13.4961L38.0446 8.75216L44 19.2479L35.92 24L44 28.7521L38.0446 39.2479L29.96 34.5039L29.9554 44H18.0446L18.04 34.5039L9.95537 39.2479L4 28.7521L12.0799 24Z" fill="currentColor" fillRule="evenodd"></path>
          </svg>
        </div>
        <h2 className="text-white text-xl font-bold leading-tight tracking-[-0.015em]">API Manager</h2>
      </div>
      <nav className="flex flex-1 justify-end gap-8 items-center">
        <div className="flex items-center gap-6">
          <a className="text-gray-300 hover:text-white text-sm font-medium leading-normal transition-colors" href="#">Product</a>
          <a className="text-gray-300 hover:text-white text-sm font-medium leading-normal transition-colors" href="#">Solutions</a>
          <a className="text-gray-300 hover:text-white text-sm font-medium leading-normal transition-colors" href="#">Pricing</a>
          <a className="text-gray-300 hover:text-white text-sm font-medium leading-normal transition-colors" href="#">Resources</a>
        </div>
        <div className="flex gap-3">
          {isAuthenticated ? (
            <>
              <Link href="/dashboard" className="flex min-w-[84px] max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-lg h-10 px-5 bg-[#0c7ff2] hover:bg-[#0a6acc] text-white text-sm font-semibold leading-normal tracking-[0.015em] transition-colors">
                <span className="truncate">Dashboard</span>
              </Link>
              <button 
                onClick={clearSession}
                className="flex min-w-[84px] max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-lg h-10 px-5 bg-[#283039] hover:bg-[#3b4754] text-white text-sm font-semibold leading-normal tracking-[0.015em] transition-colors"
              >
                <span className="truncate">Log out</span>
              </button>
            </>
          ) : (
            <>
              <Link href="/auth/register" className="flex min-w-[84px] max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-lg h-10 px-5 bg-[#0c7ff2] hover:bg-[#0a6acc] text-white text-sm font-semibold leading-normal tracking-[0.015em] transition-colors">
                <span className="truncate">Sign up</span>
              </Link>
              <Link href="/auth/signin" className="flex min-w-[84px] max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-lg h-10 px-5 bg-[#283039] hover:bg-[#3b4754] text-white text-sm font-semibold leading-normal tracking-[0.015em] transition-colors">
                <span className="truncate">Log in</span>
              </Link>
            </>
          )}
        </div>
      </nav>
    </header>
  );
};
