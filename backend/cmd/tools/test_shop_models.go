package main

import (
	"fmt"
	"log"

	"github.com/adc-credit/backend/internal/models"
	"github.com/google/uuid"
	"github.com/joho/godotenv"
	"gorm.io/driver/postgres"
	"gorm.io/gorm"
)

func main() {
	// Load environment variables
	if err := godotenv.Load(); err != nil {
		// Try to load from the project root directory
		if err := godotenv.Load("../../.env"); err != nil {
			log.Println("No .env file found, using environment variables")
		}
	}

	// Connect to test database directly
	dsn := "postgres://weerawat@localhost:5432/adc_credit_test?sslmode=disable"
	db, err := gorm.Open(postgres.Open(dsn), &gorm.Config{})
	if err != nil {
		log.Fatalf("Failed to connect to database: %v", err)
	}

	fmt.Println("Testing unified Shop models...")

	if err := testShopModels(db); err != nil {
		log.Fatalf("Test failed: %v", err)
	}

	fmt.Println("All tests passed!")
}

func testShopModels(db *gorm.DB) error {
	// Step 1: Create tables manually in correct order
	fmt.Println("Creating tables...")
	
	// Create User table first
	if err := db.AutoMigrate(&models.User{}); err != nil {
		return fmt.Errorf("failed to create users table: %w", err)
	}
	
	// Create Shop table
	if err := db.AutoMigrate(&models.Shop{}); err != nil {
		return fmt.Errorf("failed to create shops table: %w", err)
	}
	
	// Create ShopBranch table
	if err := db.AutoMigrate(&models.ShopBranch{}); err != nil {
		return fmt.Errorf("failed to create shop_branches table: %w", err)
	}
	
	// Create APIKey table
	if err := db.AutoMigrate(&models.APIKey{}); err != nil {
		return fmt.Errorf("failed to create api_keys table: %w", err)
	}

	fmt.Println("✓ Tables created successfully")

	// Step 2: Create test data
	fmt.Println("Creating test data...")

	// Create a test user
	testUser := models.User{
		ID:    uuid.New(),
		Email: "<EMAIL>",
		Name:  "Test User",
		Role:  "user",
	}

	if err := db.Create(&testUser).Error; err != nil {
		return fmt.Errorf("failed to create test user: %w", err)
	}
	fmt.Println("✓ Created test user")

	// Test 1: Create a retail shop
	retailShop := models.Shop{
		ID:           uuid.New(),
		Slug:         "test-coffee-shop",
		Name:         "Test Coffee Shop",
		Description:  "A test coffee shop",
		ShopType:     "retail",
		ContactEmail: "<EMAIL>",
		ContactPhone: "+1234567890",
		OwnerUserID:  testUser.ID,
	}

	if err := db.Create(&retailShop).Error; err != nil {
		return fmt.Errorf("failed to create retail shop: %w", err)
	}
	fmt.Println("✓ Created retail shop")

	// Test 2: Create an API service shop
	apiShop := models.Shop{
		ID:          uuid.New(),
		Slug:        "test-api-service",
		Name:        "Test API Service",
		Description: "A test API service company",
		ShopType:    "api_service",
		OwnerUserID: testUser.ID,
	}

	if err := db.Create(&apiShop).Error; err != nil {
		return fmt.Errorf("failed to create API service shop: %w", err)
	}
	fmt.Println("✓ Created API service shop")

	// Test 3: Create an enterprise shop
	enterpriseShop := models.Shop{
		ID:           uuid.New(),
		Slug:         "test-enterprise",
		Name:         "Test Enterprise",
		Description:  "A test enterprise company",
		ShopType:     "enterprise",
		ContactEmail: "<EMAIL>",
		OwnerUserID:  testUser.ID,
	}

	if err := db.Create(&enterpriseShop).Error; err != nil {
		return fmt.Errorf("failed to create enterprise shop: %w", err)
	}
	fmt.Println("✓ Created enterprise shop")

	// Test 4: Create shop branches
	// Location branch for retail shop
	locationBranch := models.ShopBranch{
		ID:           uuid.New(),
		ShopID:       retailShop.ID,
		Name:         "Downtown Location",
		Description:  "Downtown coffee shop location",
		BranchType:   "location",
		ContactEmail: "<EMAIL>",
		Address:      "123 Main St, Downtown",
	}

	if err := db.Create(&locationBranch).Error; err != nil {
		return fmt.Errorf("failed to create location branch: %w", err)
	}
	fmt.Println("✓ Created location branch")

	// Department branch for API service shop
	departmentBranch := models.ShopBranch{
		ID:          uuid.New(),
		ShopID:      apiShop.ID,
		Name:        "Engineering Department",
		Description: "Engineering team department",
		BranchType:  "department",
	}

	if err := db.Create(&departmentBranch).Error; err != nil {
		return fmt.Errorf("failed to create department branch: %w", err)
	}
	fmt.Println("✓ Created department branch")

	// Test 5: Create API keys for shops and branches
	// Shop-level API key
	shopAPIKey := models.APIKey{
		ID:       uuid.New(),
		UserID:   testUser.ID,
		ShopID:   &retailShop.ID,
		Name:     "Retail Shop API Key",
		Key:      "test-shop-api-key-" + uuid.New().String(),
		Enabled:  true,
	}

	if err := db.Create(&shopAPIKey).Error; err != nil {
		return fmt.Errorf("failed to create shop API key: %w", err)
	}
	fmt.Println("✓ Created shop-level API key")

	// Branch-level API key
	branchAPIKey := models.APIKey{
		ID:           uuid.New(),
		UserID:       testUser.ID,
		ShopBranchID: &locationBranch.ID,
		Name:         "Location Branch API Key",
		Key:          "test-branch-api-key-" + uuid.New().String(),
		Enabled:      true,
	}

	if err := db.Create(&branchAPIKey).Error; err != nil {
		return fmt.Errorf("failed to create branch API key: %w", err)
	}
	fmt.Println("✓ Created branch-level API key")

	// Test 6: Query shops with relationships
	var shops []models.Shop
	if err := db.Preload("Branches").Preload("APIKeys").Find(&shops).Error; err != nil {
		return fmt.Errorf("failed to query shops with relationships: %w", err)
	}

	fmt.Printf("✓ Found %d shops with relationships\n", len(shops))

	// Test 7: Query by shop type
	var retailShops []models.Shop
	if err := db.Where("shop_type = ?", "retail").Find(&retailShops).Error; err != nil {
		return fmt.Errorf("failed to query retail shops: %w", err)
	}

	fmt.Printf("✓ Found %d retail shops\n", len(retailShops))

	var apiShops []models.Shop
	if err := db.Where("shop_type = ?", "api_service").Find(&apiShops).Error; err != nil {
		return fmt.Errorf("failed to query API service shops: %w", err)
	}

	fmt.Printf("✓ Found %d API service shops\n", len(apiShops))

	// Test 8: Test relationships work correctly
	var shopWithBranches models.Shop
	if err := db.Preload("Branches").First(&shopWithBranches, "id = ?", retailShop.ID).Error; err != nil {
		return fmt.Errorf("failed to load shop with branches: %w", err)
	}

	if len(shopWithBranches.Branches) == 0 {
		return fmt.Errorf("shop should have branches but none found")
	}

	fmt.Printf("✓ Shop has %d branches as expected\n", len(shopWithBranches.Branches))

	// Test 9: Test API key relationships
	var apiKeyWithShop models.APIKey
	if err := db.First(&apiKeyWithShop, "shop_id = ?", retailShop.ID).Error; err != nil {
		return fmt.Errorf("failed to find API key by shop ID: %w", err)
	}

	fmt.Println("✓ API key shop relationship works")

	var apiKeyWithBranch models.APIKey
	if err := db.First(&apiKeyWithBranch, "shop_branch_id = ?", locationBranch.ID).Error; err != nil {
		return fmt.Errorf("failed to find API key by branch ID: %w", err)
	}

	fmt.Println("✓ API key branch relationship works")

	// Cleanup test data
	db.Delete(&branchAPIKey)
	db.Delete(&shopAPIKey)
	db.Delete(&departmentBranch)
	db.Delete(&locationBranch)
	db.Delete(&enterpriseShop)
	db.Delete(&apiShop)
	db.Delete(&retailShop)
	db.Delete(&testUser)

	fmt.Println("✓ Cleaned up test data")

	return nil
}
