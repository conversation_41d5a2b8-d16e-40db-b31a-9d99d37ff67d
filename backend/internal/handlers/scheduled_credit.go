package handlers

import (
	"net/http"

	"github.com/adc-credit/backend/internal/database"
	"github.com/adc-credit/backend/internal/models"
	"github.com/adc-credit/backend/internal/services"
	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

// getScheduledCreditService returns a new instance of the scheduled credit service
func getScheduledCreditService() *services.ScheduledCreditService {
	return services.NewScheduledCreditService()
}

// ProcessScheduledCredits processes all scheduled credits
// This endpoint can be called manually or by a Cloud Scheduler job
func ProcessScheduledCredits(c *gin.Context) {
	// Check for API key authentication
	apiKey, exists := c.Get("apiKey")
	if !exists {
		// If no API key, check for admin user
		user, exists := c.Get("user")
		if !exists {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "Authentication required"})
			return
		}

		// Check if user is admin
		typedUser := user.(models.User)
		if typedUser.Role != "admin" {
			c.J<PERSON>(http.StatusForbidden, gin.H{"error": "Admin access required"})
			return
		}
	} else {
		// If API key exists, check if it has admin permissions
		typedAPIKey := apiKey.(models.APIKey)
		hasAdminPerm := false
		for _, perm := range typedAPIKey.Permissions {
			if perm == "admin" {
				hasAdminPerm = true
				break
			}
		}

		if !hasAdminPerm {
			c.JSON(http.StatusForbidden, gin.H{"error": "Admin API key required"})
			return
		}
	}

	// Process scheduled credits
	service := getScheduledCreditService()
	processedCount, err := service.ProcessScheduledCredits()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "Scheduled credits processed successfully",
		"count":   processedCount,
	})
}

// GetNextScheduledCreditDate returns the date when the next scheduled credit will be added
func GetNextScheduledCreditDate(c *gin.Context) {
	user := c.MustGet("user").(models.User)

	// Get active subscription
	var subscription models.Subscription
	if err := database.DB.Where("user_id = ? AND status = ?", user.ID, "active").First(&subscription).Error; err != nil {
		c.JSON(http.StatusOK, gin.H{
			"next_scheduled_credit_date": nil,
			"message":                    "No active subscription found",
		})
		return
	}

	// Get next scheduled credit date
	service := getScheduledCreditService()

	// Initialize the database in the service
	service.SetDB(database.DB)

	nextDate, err := service.GetNextScheduledCreditDate(subscription.ID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	if nextDate == nil {
		c.JSON(http.StatusOK, gin.H{
			"next_scheduled_credit_date": nil,
			"message":                    "No scheduled credits found",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"next_scheduled_credit_date": nextDate,
	})
}

// GetScheduledCreditHistory returns the history of scheduled credit additions
func GetScheduledCreditHistory(c *gin.Context) {
	user := c.MustGet("user").(models.User)

	// Get scheduled credit transactions
	var transactions []models.Transaction
	if err := database.DB.Where("user_id = ? AND type = ?", user.ID, "credit_scheduled").
		Order("created_at DESC").
		Find(&transactions).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch scheduled credit history"})
		return
	}

	c.JSON(http.StatusOK, transactions)
}

// ManuallyAddScheduledCredits allows an admin to manually add scheduled credits to a specific user
func ManuallyAddScheduledCredits(c *gin.Context) {
	// Check for admin user
	user, exists := c.Get("user")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Authentication required"})
		return
	}

	// Check if user is admin
	typedUser := user.(models.User)
	if typedUser.Role != "admin" {
		c.JSON(http.StatusForbidden, gin.H{"error": "Admin access required"})
		return
	}

	// Parse request
	type ManualAddRequest struct {
		UserID string `json:"user_id" binding:"required"`
	}

	var req ManualAddRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Parse user ID
	userID, err := uuid.Parse(req.UserID)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid user ID"})
		return
	}

	// Get user's active subscription
	var subscription models.Subscription
	if err := database.DB.Where("user_id = ? AND status = ?", userID, "active").
		Preload("SubscriptionTier").
		First(&subscription).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "No active subscription found for user"})
		return
	}

	// Add scheduled credits
	service := getScheduledCreditService()
	if err := service.AddScheduledCreditsToSubscription(subscription); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message":        "Scheduled credits added successfully",
		"user_id":        userID.String(),
		"credit_balance": subscription.CreditBalance,
	})
}
