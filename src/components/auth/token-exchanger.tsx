"use client";

import { useSession } from "next-auth/react";
import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { AlertCircle } from "lucide-react";
import { exchangeGoogleToken, storeToken, getStoredToken } from "@/lib/auth/token-utils";

/**
 * This component handles exchanging the Google OAuth token for a backend JWT token
 * It should be rendered on pages that require authentication
 */
export function TokenExchanger() {
  const { data: session, status } = useSession();
  const [error, setError] = useState<string | null>(null);
  const [exchanging, setExchanging] = useState(false);
  const router = useRouter();

  useEffect(() => {
    console.log("TokenExchanger: Session status:", status);
    console.log("TokenExchanger: Session data:", session);

    // Check if we already have a token
    const existingToken = getStoredToken();
    if (existingToken) {
      console.log("TokenExchanger: Already have a token, no need to exchange");
      return;
    }

    // Only proceed if we have a session with an access token
    if (status === "authenticated" && session?.accessToken && !exchanging) {
      setExchanging(true);

      // Exchange the Google token for a backend JWT token
      const performTokenExchange = async () => {
        try {
          console.log("TokenExchanger: Exchanging token...");

          // Use our utility function to exchange the token
          const data = await exchangeGoogleToken(session.accessToken);

          console.log("TokenExchanger: Token exchange successful");

          // Store the token using our utility function
          storeToken(data.token, data.refreshToken, data.user);

          // Force a refresh to ensure the new token is used
          window.location.reload();
        } catch (error) {
          console.error("TokenExchanger: Error exchanging token:", error);
          setError(error instanceof Error ? error.message : "Failed to exchange token");
        } finally {
          setExchanging(false);
        }
      };

      performTokenExchange();
    }
  }, [session, status, router, exchanging]);

  // If there's an error, show it
  if (error) {
    return (
      <Alert variant="destructive" className="mb-4">
        <AlertCircle className="h-4 w-4" />
        <AlertDescription>
          Authentication error: {error}. Please try logging in again.
        </AlertDescription>
      </Alert>
    );
  }

  // Otherwise, render nothing
  return null;
}
