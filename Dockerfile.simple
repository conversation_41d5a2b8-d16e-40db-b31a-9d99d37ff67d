# Use Bun as the base image
FROM oven/bun:1-slim

WORKDIR /app

# Copy package.json and bun.lock
COPY package.json ./
COPY bun.lock ./

# Install dependencies
RUN bun install --frozen-lockfile

# Copy the rest of the application code
COPY . .

# Set environment variables
ENV NODE_ENV=production

# Expose the port (Cloud Run will set PORT env var)
EXPOSE 8080

# Start the custom Express server
CMD ["bun", "run", "start:server"]
