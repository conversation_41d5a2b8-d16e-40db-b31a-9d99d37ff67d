"use client";

import { useSearchParams } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import Link from "next/link";
import { Suspense } from "react";

function AuthErrorContent() {
  const searchParams = useSearchParams();
  const error = searchParams.get("error");

  const getErrorMessage = (errorCode: string | null) => {
    switch (errorCode) {
      case "Configuration":
        return "There is a problem with the server configuration.";
      case "AccessDenied":
        return "You do not have access to sign in.";
      case "Verification":
        return "The verification token has expired or has already been used.";
      case "OAuthSignin":
        return "Error starting the OAuth sign in flow.";
      case "OAuthCallback":
        return "Error completing the OAuth sign in flow.";
      case "OAuthCreateAccount":
        return "Error creating the OAuth user in the database.";
      case "EmailCreateAccount":
        return "Error creating the email user in the database.";
      case "Callback":
        return "Error during the OAuth callback.";
      case "OAuthAccountNotLinked":
        return "The email on the OAuth account is already linked to another account.";
      case "EmailSignin":
        return "Error sending the email sign in link.";
      case "CredentialsSignin":
        return "The sign in credentials are invalid.";
      case "SessionRequired":
        return "You must be signed in to access this page.";
      default:
        return "An unexpected error occurred.";
    }
  };

  return (
    <div className="flex min-h-screen items-center justify-center bg-gray-50 dark:bg-gray-900 px-4">
      <Card className="w-full max-w-md">
        <CardHeader className="space-y-1 text-center">
          <CardTitle className="text-2xl font-bold text-red-600">Authentication Error</CardTitle>
          <CardDescription>
            There was a problem signing you in
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="p-4 bg-red-50 text-red-500 rounded-md">
            <p className="font-medium">Error: {error || "Unknown"}</p>
            <p className="mt-2">{getErrorMessage(error)}</p>
          </div>

          <div className="text-center text-sm text-gray-500 dark:text-gray-400">
            <p>If this problem persists, please contact support.</p>
          </div>
        </CardContent>
        <CardFooter className="flex justify-center space-x-4">
          <Link href="/auth/signin">
            <Button variant="outline">Try Again</Button>
          </Link>
          <Link href="/">
            <Button>Back to Home</Button>
          </Link>
        </CardFooter>
      </Card>
    </div>
  );
}

export default function AuthErrorPage() {
  return (
    <Suspense fallback={
      <div className="flex min-h-screen items-center justify-center bg-gray-50 dark:bg-gray-900 px-4">
        <Card className="w-full max-w-md">
          <CardHeader className="space-y-1 text-center">
            <CardTitle className="text-2xl font-bold text-red-600">Authentication Error</CardTitle>
            <CardDescription>
              Loading error details...
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex justify-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
            </div>
          </CardContent>
        </Card>
      </div>
    }>
      <AuthErrorContent />
    </Suspense>
  );
}
