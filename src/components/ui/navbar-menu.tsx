"use client";
import React from "react";
import { motion } from "framer-motion";
import Link from "next/link";
import Image from "next/image";

const transition = {
  type: "spring",
  mass: 0.5,
  damping: 11.5,
  stiffness: 100,
  restDelta: 0.001,
  restSpeed: 0.001,
};

export const MenuItem = ({
  setActive,
  active,
  item,
  children,
}: {
  setActive: (item: string) => void;
  active: string | null;
  item: string;
  children?: React.ReactNode;
}) => {
  // Add a small delay before closing the menu to give users time to move to the submenu
  const handleMouseLeave = () => {
    // Don't immediately close the menu when mouse leaves the item
    // This will be handled by the Menu component's onMouseLeave
  };

  return (
    <div
      onMouseEnter={() => setActive(item)}
      onMouseLeave={handleMouseLeave}
      className="relative"
    >
      <motion.p
        transition={{ duration: 0.3 }}
        className="cursor-pointer text-[#181510] hover:text-[#8a745c] font-medium px-3 py-2"
      >
        {item}
      </motion.p>
      {active !== null && (
        <motion.div
          initial={{ opacity: 0, scale: 0.85, y: 10 }}
          animate={{ opacity: 1, scale: 1, y: 0 }}
          transition={transition}
        >
          {active === item && (
            <div
              className="absolute top-[calc(100%_+_0.5rem)] left-1/2 transform -translate-x-1/2"
              onMouseEnter={() => setActive(item)} // Keep the menu open when hovering the dropdown
            >
              {/* This invisible element creates a safe hover area between the menu item and dropdown */}
              <div className="h-4 w-full" />
              <motion.div
                transition={transition}
                layoutId="active" // layoutId ensures smooth animation
                className="bg-[#fbfaf9] backdrop-blur-sm rounded-lg overflow-hidden border border-[#e5ccb2] shadow-md"
                onMouseEnter={() => setActive(item)} // Keep the menu open when hovering the dropdown
              >
                <motion.div
                  layout // layout ensures smooth animation
                  className="w-max h-full p-4"
                >
                  {children}
                </motion.div>
              </motion.div>
            </div>
          )}
        </motion.div>
      )}
    </div>
  );
};

export const Menu = ({
  setActive,
  children,
}: {
  setActive: (item: string | null) => void;
  children: React.ReactNode;
}) => {
  // Add a timeout reference to handle delayed menu closing
  const closeTimeout = React.useRef<NodeJS.Timeout | null>(null);

  // Handle mouse leave with a delay
  const handleMouseLeave = () => {
    // Clear any existing timeout
    if (closeTimeout.current) {
      clearTimeout(closeTimeout.current);
    }

    // Set a new timeout to close the menu after a delay
    closeTimeout.current = setTimeout(() => {
      setActive(null);
    }, 300); // 300ms delay before closing
  };

  // Handle mouse enter to cancel any pending close
  const handleMouseEnter = () => {
    // Clear any existing timeout to prevent the menu from closing
    if (closeTimeout.current) {
      clearTimeout(closeTimeout.current);
      closeTimeout.current = null;
    }
  };

  return (
    <nav
      onMouseLeave={handleMouseLeave}
      onMouseEnter={handleMouseEnter}
      className="relative rounded-lg border-transparent bg-[#fbfaf9] flex justify-center space-x-6 px-4 py-2"
    >
      {children}
    </nav>
  );
};

export const ProductItem = ({
  title,
  description,
  href,
  src,
}: {
  title: string;
  description: string;
  href: string;
  src: string;
}) => {
  return (
    <Link href={href} className="flex space-x-2">
      <Image
        src={src}
        width={140}
        height={70}
        alt={title}
        className="flex-shrink-0 rounded-md shadow-2xl"
      />
      <div>
        <h4 className="text-xl font-bold mb-1 text-black dark:text-white">
          {title}
        </h4>
        <p className="text-neutral-700 text-sm max-w-[10rem] dark:text-neutral-300">
          {description}
        </p>
      </div>
    </Link>
  );
};

export const HoveredLink = ({ children, ...rest }: any) => {
  return (
    <Link
      {...rest}
      className="text-[#8a745c] hover:text-[#181510] py-2 px-3 block transition-colors rounded-md hover:bg-[#f1edea] w-full"
    >
      {children}
    </Link>
  );
};
