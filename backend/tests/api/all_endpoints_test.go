package api

import (
	"net/http"
	"testing"

	testutils "github.com/adc-credit/backend/internal/testing"
	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/suite"
)

type AllEndpointsTestSuite struct {
	suite.Suite
	router *gin.Engine
}

func (suite *AllEndpointsTestSuite) SetupSuite() {
	// Setup test environment
	err := testutils.SetupTestEnvironment()
	suite.Require().NoError(err)

	// Setup router with all endpoints
	suite.router = gin.New()
	suite.router.Use(gin.Recovery())
	
	suite.setupAllEndpoints()
}

func (suite *AllEndpointsTestSuite) TearDownSuite() {
	testutils.CleanupTestEnvironment()
}

func (suite *AllEndpointsTestSuite) SetupTest() {
	testutils.CleanTestData()
}

func (suite *AllEndpointsTestSuite) setupAllEndpoints() {
	// Protected routes
	protected := suite.router.Group("/api/v1")
	protected.Use(suite.mockAuthMiddleware())
	{
		// User management endpoints
		users := protected.Group("/users")
		{
			users.GET("/me", func(c *gin.Context) {
				user := c.MustGet("user")
				c.JSON(http.StatusOK, user)
			})
			users.PUT("/me", func(c *gin.Context) {
				c.JSON(http.StatusOK, gin.H{"message": "User updated successfully"})
			})
		}

		// Organization management endpoints
		protected.GET("/organizations", func(c *gin.Context) {
			c.JSON(http.StatusOK, gin.H{
				"data": []gin.H{
					{"id": "org1", "name": "Test Org", "slug": "test-org"},
				},
				"total": 1,
			})
		})
		protected.POST("/organizations", func(c *gin.Context) {
			c.JSON(http.StatusCreated, gin.H{
				"id": "new-org-id",
				"message": "Organization created successfully",
			})
		})
		protected.GET("/organizations/:slug", func(c *gin.Context) {
			slug := c.Param("slug")
			c.JSON(http.StatusOK, gin.H{
				"id": "org-id",
				"name": "Test Organization",
				"slug": slug,
			})
		})
		protected.PUT("/organizations/:slug", func(c *gin.Context) {
			slug := c.Param("slug")
			c.JSON(http.StatusOK, gin.H{
				"slug": slug,
				"message": "Organization updated successfully",
			})
		})
		protected.DELETE("/organizations/:slug", func(c *gin.Context) {
			c.JSON(http.StatusNoContent, nil)
		})

		// External user management
		protected.GET("/org-users", func(c *gin.Context) {
			c.JSON(http.StatusOK, gin.H{
				"data": []gin.H{
					{"id": "ext-user1", "email": "<EMAIL>", "credits": 100},
				},
				"total": 1,
			})
		})
		protected.POST("/org-users", func(c *gin.Context) {
			c.JSON(http.StatusCreated, gin.H{
				"id": "new-ext-user-id",
				"message": "External user created successfully",
			})
		})
		protected.GET("/org-users/:id", func(c *gin.Context) {
			id := c.Param("id")
			c.JSON(http.StatusOK, gin.H{
				"id": id,
				"email": "<EMAIL>",
				"credits": 100,
			})
		})
		protected.PUT("/org-users/:id", func(c *gin.Context) {
			id := c.Param("id")
			c.JSON(http.StatusOK, gin.H{
				"id": id,
				"message": "External user updated successfully",
			})
		})
		protected.DELETE("/org-users/:id", func(c *gin.Context) {
			c.JSON(http.StatusNoContent, nil)
		})
		protected.POST("/org-users/:id/credits/add", func(c *gin.Context) {
			id := c.Param("id")
			c.JSON(http.StatusOK, gin.H{
				"id": id,
				"message": "Credits added to external user",
				"new_balance": 150,
			})
		})
		protected.POST("/org-users/:id/credits/reduce", func(c *gin.Context) {
			id := c.Param("id")
			c.JSON(http.StatusOK, gin.H{
				"id": id,
				"message": "Credits reduced from external user",
				"new_balance": 50,
			})
		})

		// Credit management endpoints
		credits := protected.Group("/credits")
		{
			credits.GET("", func(c *gin.Context) {
				c.JSON(http.StatusOK, gin.H{
					"credit_balance": 1000,
					"subscription_type": "premium",
				})
			})
			credits.POST("/add", func(c *gin.Context) {
				c.JSON(http.StatusOK, gin.H{
					"message": "Credits added successfully",
					"new_balance": 1100,
				})
			})
			credits.GET("/transactions", func(c *gin.Context) {
				c.JSON(http.StatusOK, gin.H{
					"data": []gin.H{
						{"id": "tx1", "type": "credit_add", "amount": 100},
					},
					"total": 1,
				})
			})
			credits.GET("/scheduled/next", func(c *gin.Context) {
				c.JSON(http.StatusOK, gin.H{
					"next_scheduled_date": "2023-02-01T00:00:00Z",
					"credits_amount": 1000,
				})
			})
			credits.GET("/scheduled/history", func(c *gin.Context) {
				c.JSON(http.StatusOK, gin.H{
					"data": []gin.H{
						{"date": "2023-01-01T00:00:00Z", "credits": 1000, "status": "completed"},
					},
					"total": 1,
				})
			})
		}

		// Usage statistics endpoints
		usage := protected.Group("/usage")
		{
			usage.GET("", func(c *gin.Context) {
				c.JSON(http.StatusOK, gin.H{
					"data": []gin.H{
						{"endpoint": "/api/test", "requests": 100, "credits": 50},
					},
					"total": 1,
				})
			})
			usage.GET("/summary", func(c *gin.Context) {
				c.JSON(http.StatusOK, gin.H{
					"total_requests": 1000,
					"total_credits": 500,
					"avg_response_time": 150,
				})
			})
		}

		// Subscription management endpoints
		subscriptions := protected.Group("/subscriptions")
		{
			subscriptions.GET("", func(c *gin.Context) {
				c.JSON(http.StatusOK, gin.H{
					"data": []gin.H{
						{"id": "sub1", "tier": "premium", "status": "active"},
					},
					"total": 1,
				})
			})
			subscriptions.GET("/active", func(c *gin.Context) {
				c.JSON(http.StatusOK, gin.H{
					"id": "sub1",
					"tier": "premium",
					"status": "active",
					"credit_balance": 1000,
				})
			})
			subscriptions.GET("/tiers", func(c *gin.Context) {
				c.JSON(http.StatusOK, gin.H{
					"data": []gin.H{
						{"id": 1, "name": "Basic", "price": 9.99, "credit_limit": 1000},
						{"id": 2, "name": "Premium", "price": 19.99, "credit_limit": 5000},
					},
				})
			})
			subscriptions.POST("", func(c *gin.Context) {
				c.JSON(http.StatusCreated, gin.H{
					"id": "new-sub-id",
					"message": "Subscription created successfully",
				})
			})
			subscriptions.PUT("/:id", func(c *gin.Context) {
				id := c.Param("id")
				c.JSON(http.StatusOK, gin.H{
					"id": id,
					"message": "Subscription updated successfully",
				})
			})
			subscriptions.DELETE("/:id", func(c *gin.Context) {
				c.JSON(http.StatusOK, gin.H{
					"message": "Subscription cancelled successfully",
				})
			})
			subscriptions.POST("/:id/upgrade", func(c *gin.Context) {
				id := c.Param("id")
				c.JSON(http.StatusOK, gin.H{
					"id": id,
					"message": "Subscription upgraded successfully",
				})
			})
		}

		// Stripe integration endpoints
		stripe := protected.Group("/stripe")
		{
			stripe.POST("/create-checkout-session", func(c *gin.Context) {
				c.JSON(http.StatusOK, gin.H{
					"checkout_url": "https://checkout.stripe.com/session123",
					"session_id": "cs_test_session123",
				})
			})
		}

		// Webhook management endpoints
		webhooks := protected.Group("/webhooks")
		{
			webhooks.GET("", func(c *gin.Context) {
				c.JSON(http.StatusOK, gin.H{
					"data": []gin.H{
						{"id": "wh1", "name": "Test Webhook", "url": "https://example.com/webhook", "active": true},
					},
					"total": 1,
				})
			})
			webhooks.POST("", func(c *gin.Context) {
				c.JSON(http.StatusCreated, gin.H{
					"id": "new-webhook-id",
					"message": "Webhook created successfully",
				})
			})
			webhooks.GET("/:id", func(c *gin.Context) {
				id := c.Param("id")
				c.JSON(http.StatusOK, gin.H{
					"id": id,
					"name": "Test Webhook",
					"url": "https://example.com/webhook",
					"active": true,
				})
			})
			webhooks.PUT("/:id", func(c *gin.Context) {
				id := c.Param("id")
				c.JSON(http.StatusOK, gin.H{
					"id": id,
					"message": "Webhook updated successfully",
				})
			})
			webhooks.DELETE("/:id", func(c *gin.Context) {
				c.JSON(http.StatusNoContent, nil)
			})
			webhooks.GET("/:id/deliveries", func(c *gin.Context) {
				id := c.Param("id")
				c.JSON(http.StatusOK, gin.H{
					"webhook_id": id,
					"data": []gin.H{
						{"id": "del1", "event": "credit.consumed", "status_code": 200, "success": true},
					},
					"total": 1,
				})
			})
		}
	}

	// Stripe webhook (no auth required)
	suite.router.POST("/webhook/stripe", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{"message": "Webhook processed successfully"})
	})

	// Public Stripe endpoint
	suite.router.POST("/api/stripe/create-checkout-session", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{
			"checkout_url": "https://checkout.stripe.com/session123",
			"session_id": "cs_test_session123",
		})
	})

	// Scheduled tasks endpoints
	public := suite.router.Group("/api/v1")
	{
		public.POST("/tasks/process-scheduled-credits", func(c *gin.Context) {
			c.JSON(http.StatusOK, gin.H{"message": "Scheduled credits processed"})
		})
		public.POST("/tasks/process-credit-resets", func(c *gin.Context) {
			c.JSON(http.StatusOK, gin.H{"message": "Credit resets processed"})
		})
	}
}

// Mock authentication middleware
func (suite *AllEndpointsTestSuite) mockAuthMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		auth := c.GetHeader("Authorization")
		if auth == "" {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "Authorization required"})
			c.Abort()
			return
		}

		mockUser := gin.H{
			"id":    "user-123",
			"email": "<EMAIL>",
			"name":  "Test User",
			"role":  "user",
		}
		c.Set("user", mockUser)
		c.Next()
	}
}

// Run the test suite
func TestAllEndpointsTestSuite(t *testing.T) {
	suite.Run(t, new(AllEndpointsTestSuite))
}
