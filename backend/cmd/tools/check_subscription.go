package main

import (
	"fmt"
	"log"

	"github.com/adc-credit/backend/internal/database"
	"github.com/adc-credit/backend/internal/models"
	"github.com/joho/godotenv"
)

func main() {
	// Load environment variables
	if err := godotenv.Load(); err != nil {
		log.Println("No .env file found, using environment variables")
	}

	// Initialize database connection
	if err := database.InitDB(); err != nil {
		log.Fatalf("Failed to connect to database: %v", err)
	}

	// Get all users
	var users []models.User
	if err := database.DB.Find(&users).Error; err != nil {
		log.Fatalf("Failed to get users: %v", err)
	}

	fmt.Printf("Found %d users\n", len(users))

	// For each user, get their subscriptions
	for _, user := range users {
		var subscriptions []models.Subscription
		if err := database.DB.Where("user_id = ?", user.ID).
			Preload("SubscriptionTier").
			Find(&subscriptions).Error; err != nil {
			log.Fatalf("Failed to get subscriptions for user %s: %v", user.Email, err)
		}

		fmt.Printf("User: %s (%s)\n", user.Name, user.Email)
		fmt.Printf("  Subscriptions: %d\n", len(subscriptions))

		for i, sub := range subscriptions {
			fmt.Printf("  Subscription #%d:\n", i+1)
			fmt.Printf("    ID: %s\n", sub.ID)
			fmt.Printf("    Tier: %s (ID: %d)\n", sub.SubscriptionTier.Name, sub.SubscriptionTierID)
			fmt.Printf("    Status: %s\n", sub.Status)
			fmt.Printf("    Start Date: %s\n", sub.StartDate)
			if sub.EndDate != nil {
				fmt.Printf("    End Date: %s\n", *sub.EndDate)
			}
			fmt.Printf("    Auto Renew: %v\n", sub.AutoRenew)
			fmt.Printf("    Credit Balance: %d\n", sub.CreditBalance)
		}
		fmt.Println()
	}

	// Also check subscription tiers
	var tiers []models.SubscriptionTier
	if err := database.DB.Find(&tiers).Error; err != nil {
		log.Fatalf("Failed to get subscription tiers: %v", err)
	}

	fmt.Printf("Found %d subscription tiers\n", len(tiers))
	for i, tier := range tiers {
		fmt.Printf("Tier #%d:\n", i+1)
		fmt.Printf("  ID: %d\n", tier.ID)
		fmt.Printf("  Name: %s\n", tier.Name)
		fmt.Printf("  Price: $%.2f\n", tier.Price)
		fmt.Printf("  Credit Limit: %d\n", tier.CreditLimit)
	}
}
