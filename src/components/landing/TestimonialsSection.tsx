export const TestimonialsSection = () => {
  const testimonials = [
    {
      image: "https://lh3.googleusercontent.com/aida-public/AB6AXuDh3ufyoCevuquRJdhp5_MmfeN3BqkzqYeUX-mZwEpLwvuzF_DJz-0BEvZYBdmFvKqeEDzfxWy4nCnI0xep4Z6cF3muW0ZgFOijC1nwxp9c5GdeLifgP9W76XvyIij4XOaRXt2kqNu2427DA2pRrV23yFufH8f4khDkFHTq4tK-v_hiEBlabVnDkSZguWPfEZ8O6iVqMyvZvB3PSGIm3TPwP5c00q_dLNYolanTgLRQdgKouRLvPXK5anus06dE00O_KtyBnIxxUxNc",
      quote: "This API management tool has revolutionized our workflow. The analytics are incredibly insightful, and the key management features are top-notch.",
      author: "<PERSON>, CTO at Tech Solutions"
    },
    {
      image: "https://lh3.googleusercontent.com/aida-public/AB6AXuDNrYDeGT-ICoAFFtyIRkGECEbas4EAWB2ilFyjIcnTNu8rxeMtXej_6ExpbE-RLE8_fPh_lD18mOv9ERN9jhWzw16YNShTPiNRXAfbhJHSBkvMXzqXgifpw6IJM38sjqWcJ-cse1VyGhChxrGDYfWDe_68Y032cfhvcCfjCI_cY59myJFShnvn1HVZ9pagGUVH-h3D8TKb96TOzcCguD98q2c2Izr7boIfrpEmPZ24Jl_WZ1aEz23o7Bp9pxnsKziODMUWZpg6A8nq",
      quote: "We've seen a significant improvement in our API performance and security since implementing this solution. The support team is also very responsive and helpful.",
      author: "David Chen, Lead Developer at Innovate Systems"
    },
    {
      image: "https://lh3.googleusercontent.com/aida-public/AB6AXuCKSRLSjWYO1ynvTMGhgsxCME-x1OgBUf0Dzxa_Z3A6bvcMbW73hfC3RhhS9wvcz-Orvk89Wd4dgv0yTEqnW7r8qiPpOysGWYBfrFh3pbbEGhlt3dQzADoV2j5M-OSt1kZWS5qOAIOSTOCSmyTwhtXXpFizkTJooxGErDUYoCj6LaEATg8RyCS7fo-OdbKu8ztHk1AtosS2pPHWOY_agMcDkPgjPLRfsdHjBkbRNQGNVhhZwSdtFtIYUKWPP5NjiaRF88-wKvKfzQNV",
      quote: "The flexible subscription options and organization management tools have made it much easier to scale our API usage and collaborate with our team.",
      author: "Emily Rodriguez, Product Manager at Global Corp"
    }
  ];

  return (
    <section className="flex flex-col gap-8 md:gap-12 items-center">
      <div className="text-center max-w-3xl">
        <h2 className="text-white text-3xl md:text-4xl font-bold leading-tight tracking-tight mb-3">Loved by Developers Worldwide</h2>
        <p className="text-gray-300 text-base md:text-lg leading-relaxed">
          Hear what our users are saying about API Manager.
        </p>
      </div>
      <div className="flex overflow-x-auto w-full gap-6 pb-6 [-ms-scrollbar-style:none] [scrollbar-width:none] [&::-webkit-scrollbar]:hidden">
        {testimonials.map((testimonial, index) => (
          <div key={index} className="flex flex-none flex-col gap-4 rounded-xl bg-[#161a1e] p-6 border border-[#283039] min-w-[300px] md:min-w-[360px] shadow-lg">
            <div 
              className="w-full h-56 bg-center bg-no-repeat aspect-video bg-cover rounded-lg" 
              style={{ backgroundImage: `url("${testimonial.image}")` }}
            ></div>
            <blockquote className="mt-2">
              <p className="text-white text-base font-medium leading-relaxed">
                &ldquo;{testimonial.quote}&rdquo;
              </p>
            </blockquote>
            <footer className="mt-auto pt-2">
              <p className="text-gray-400 text-sm font-normal leading-normal">{testimonial.author}</p>
            </footer>
          </div>
        ))}
      </div>
    </section>
  );
};
