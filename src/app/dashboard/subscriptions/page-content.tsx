"use client";

import { useSession } from "next-auth/react";
import { useEffect, useState } from "react";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Subscription, SubscriptionTier } from "@/types";
import { toast } from "sonner";
import { useRouter, useSearchParams } from "next/navigation";

import { RefreshCw, CreditCard, User, Store } from "lucide-react";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import MerchantSubscriptionPanel from "@/components/subscription/merchant-subscription-panel";
import CustomerSubscriptionPanel from "@/components/subscription/customer-subscription-panel";
import { MySubscriptionsPanel } from "@/components/subscription/my-subscriptions-panel";
import {
  useGetSubscriptionTiersQuery,
  useGetSubscriptionsQuery,
  useUpdateSubscriptionMutation,
  useCancelSubscriptionMutation,
  useCreateSubscriptionMutation
} from "@/lib/api/apiSlice";



// Helper function to get access token from session
const getAccessToken = (session: ReturnType<typeof useSession>['data']): string | null => {
  if (!session) return null;

  if (session.accessToken) {
    return session.accessToken;
  } else if (session.user && 'accessToken' in session.user) {
    return session.user.accessToken as string;
  }

  return null;
};

export default function SubscriptionsPage() {
  const { data: session } = useSession();
  const router = useRouter();
  const searchParams = useSearchParams();

  // RTK Query hooks
  const {
    data: subscriptions = [],
    isLoading: isLoadingSubscriptions,
    refetch: refetchSubscriptions
  } = useGetSubscriptionsQuery();

  const {
    data: subscriptionTiers = [],
    isLoading: isLoadingTiers
  } = useGetSubscriptionTiersQuery();

  const [updateSubscriptionMutation] = useUpdateSubscriptionMutation();
  const [cancelSubscriptionMutation] = useCancelSubscriptionMutation();
  const [createSubscriptionMutation] = useCreateSubscriptionMutation();

  // Local state
  const [selectedTier, setSelectedTier] = useState<SubscriptionTier | null>(null);
  const [confirmDialogOpen, setConfirmDialogOpen] = useState(false);
  const [cancelDialogOpen, setCancelDialogOpen] = useState(false);
  const [subscriptionToCancel, setSubscriptionToCancel] = useState<Subscription | null>(null);
  const [isCreatingCheckout, setIsCreatingCheckout] = useState(false);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [isUpgrading, setIsUpgrading] = useState(false);

  // Get active subscriptions by type
  const getActiveSubscriptionsByType = (type: string) => {
    return subscriptions.filter((sub) => sub.status === "active" && sub.subscription_type === type);
  };

  // Get personal active subscription
  const getPersonalActiveSubscription = () => {
    return subscriptions.find((sub) => sub.status === "active" && sub.subscription_type === "personal");
  };

  // Compute loading state
  const loading = isLoadingSubscriptions || isLoadingTiers;

  // Store access token in session storage for other components to use
  useEffect(() => {
    if (!session) return;

    const accessToken = getAccessToken(session);
    if (accessToken) {
      sessionStorage.setItem('accessToken', accessToken);
    }
  }, [session]);

  // Check for success or canceled status from Stripe checkout
  useEffect(() => {
    const success = searchParams.get('success');
    const canceled = searchParams.get('canceled');
    const sessionId = searchParams.get('session_id');
    const tierIdParam = searchParams.get('tier_id');
    const subscriptionType = searchParams.get('subscription_type') || 'personal';
    const merchantShopId = searchParams.get('merchant_shop_id');
    const shopCustomerId = searchParams.get('shop_customer_id');

    // Create a flag to track if we're handling a redirect
    const isHandlingRedirect = success === 'true' || canceled === 'true';

    if (isHandlingRedirect) {
      // First, remove the query parameters to prevent infinite loops
      router.replace('/dashboard/subscriptions', { scroll: false });

      if (success === 'true') {
        toast.success('Subscription created successfully!');

        // If we have a session ID and tier ID, create a subscription directly in the backend
        if (sessionId && tierIdParam) {
          const tierId = parseInt(tierIdParam, 10);

          // Create subscription using RTK Query with the appropriate type and IDs
          createSubscriptionMutation({
            subscription_tier_id: tierId,
            auto_renew: true,
            subscription_type: subscriptionType,
            ...(merchantShopId && { merchant_shop_id: merchantShopId }),
            ...(shopCustomerId && { shop_customer_id: shopCustomerId })
          })
            .unwrap()
            .then(() => {
              console.log('Subscription created successfully');
            })
            .catch((error) => {
              console.error('Failed to create subscription:', error);
            });
        } else {
          console.warn('Missing session ID or tier ID in redirect URL');
        }
      } else if (canceled === 'true') {
        toast.error('Subscription checkout was canceled.');
      }
    }
  }, [searchParams, router, session, createSubscriptionMutation]);

  // Store access token in session storage for RTK Query to use
  useEffect(() => {
    if (!session) return;

    const accessToken = getAccessToken(session);
    if (accessToken) {
      sessionStorage.setItem('accessToken', accessToken);
    }
  }, [session]);

  const handleCreateSubscription = async () => {
    if (!session?.user || !selectedTier) return;

    setIsCreatingCheckout(true);

    try {
      // For free tiers, create subscription directly
      if (selectedTier.price === 0) {
        await createSubscriptionMutation({
          subscription_tier_id: selectedTier.id,
          auto_renew: true,
          subscription_type: "personal"
        }).unwrap();

        toast.success(`Successfully subscribed to ${selectedTier.name} plan`);
        setConfirmDialogOpen(false);
      } else {
        // Get the access token
        const accessToken = getAccessToken(session);

        if (!accessToken) {
          throw new Error('No access token available');
        }

        // Create a checkout session using the proxy API
        const response = await fetch('/api/stripe/create-checkout-session', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            tier_id: selectedTier.id,
            subscription_type: 'personal'
          }),
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || 'Failed to create checkout session');
        }

        const data = await response.json();

        // Check if it's a free tier response from the backend
        if (data.free_tier) {
          // For free tiers, create subscription directly
          await createSubscriptionMutation({
            subscription_tier_id: selectedTier.id,
            auto_renew: true,
            subscription_type: "personal"
          }).unwrap();

          toast.success(`Successfully subscribed to ${selectedTier.name} plan`);
          setConfirmDialogOpen(false);
          return;
        }

        if (!data.checkout_url) {
          throw new Error('Failed to create checkout session');
        }

        // Close the dialog
        setConfirmDialogOpen(false);

        // Redirect to Stripe Checkout
        window.location.href = data.checkout_url;
      }
    } catch (error) {
      console.error("Error creating checkout session:", error);
      toast.error(error instanceof Error ? error.message : "Failed to create checkout session");
    } finally {
      setIsCreatingCheckout(false);
    }
  };

  const handleToggleAutoRenew = async (subscription: Subscription) => {
    try {
      // Use RTK Query mutation
      const data = await updateSubscriptionMutation({
        id: subscription.id,
        auto_renew: !subscription.auto_renew,
      }).unwrap();

      toast.success(`Auto-renew ${data.auto_renew ? "enabled" : "disabled"}`);
    } catch (error) {
      console.error("Error updating subscription:", error);
      toast.error(error instanceof Error ? error.message : "Failed to update subscription");
    }
  };

  const handleCancelSubscription = async () => {
    if (!subscriptionToCancel) return;

    try {
      // Use RTK Query mutation
      await cancelSubscriptionMutation(subscriptionToCancel.id).unwrap();

      setCancelDialogOpen(false);
      setSubscriptionToCancel(null);
      toast.success("Subscription cancelled successfully");

      // Refresh the subscriptions data
      refetchSubscriptions();
    } catch (error) {
      console.error("Error cancelling subscription:", error);
      toast.error(error instanceof Error ? error.message : "Failed to cancel subscription");
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  // This function is kept for backward compatibility
  const getActiveSubscription = () => {
    return getPersonalActiveSubscription();
  };

  const handleRefreshData = async () => {
    if (!session) return;

    setIsRefreshing(true);

    try {
      // Refetch data using RTK Query
      await Promise.all([
        refetchSubscriptions(),
        // No need to explicitly refetch subscription tiers as they rarely change
      ]);

      toast.success("Subscription data refreshed");
    } catch (error) {
      console.error("Error refreshing data:", error);
      toast.error("Failed to refresh subscription data");
    } finally {
      setIsRefreshing(false);
    }
  };

  // Handle subscription upgrade/downgrade
  const handleUpgradeSubscription = async (subscriptionId: string, newTierId: number, prorate: boolean) => {
    if (!session) return;

    setIsUpgrading(true);

    try {
      // Get the access token
      const accessToken = getAccessToken(session);

      if (!accessToken) {
        throw new Error('No access token available');
      }

      // Create a checkout session for the upgrade using the proxy API
      const response = await fetch(`/api/subscriptions/${subscriptionId}/upgrade`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          new_tier_id: newTierId,
          prorate: prorate
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to upgrade subscription');
      }

      // If there's a checkout URL, redirect to Stripe
      if (data.checkout_url) {
        window.location.href = data.checkout_url;
        return;
      }

      // If no checkout URL, the upgrade was processed directly
      toast.success(data.message || 'Subscription upgraded successfully');

      // Refresh the subscriptions data
      await refetchSubscriptions();

    } catch (error) {
      console.error("Error upgrading subscription:", error);
      toast.error(error instanceof Error ? error.message : "Failed to upgrade subscription");
    } finally {
      setIsUpgrading(false);
    }
  };

  // Handle merchant subscription upgrade
  const handleMerchantUpgrade = async (shopId: string, tierId: number) => {
    if (!session) return;

    try {
      // In a real implementation, this would call an API to upgrade the merchant's subscription
      // For now, we'll just show a toast message
      toast.success(`Upgrading shop ${shopId} to tier ${tierId}`);

      // This would typically call a mutation like:
      // await updateMerchantSubscriptionMutation({ shopId, tierId }).unwrap();

      // For demonstration purposes, we'll just return a resolved promise
      return Promise.resolve();
    } catch (error) {
      console.error("Error upgrading merchant subscription:", error);
      toast.error("Failed to upgrade merchant subscription");
      throw error;
    }
  };

  // Check if user is not authenticated
  if (!session) {
    return (
      <div className="flex h-[calc(100vh-4rem)] flex-col items-center justify-center gap-4">
        <h2 className="text-2xl font-bold">Please sign in to view your subscriptions</h2>
        <Button onClick={() => router.push('/api/auth/signin')}>
          Sign In
        </Button>
      </div>
    );
  }

  if (loading) {
    return (
      <div className="flex h-[calc(100vh-4rem)] items-center justify-center">
        <div className="h-8 w-8 animate-spin rounded-full border-b-2 border-primary"></div>
      </div>
    );
  }

  const activeSubscription = getActiveSubscription();

  return (
    <div>
      <div className="flex justify-between items-center mb-8">
        <h1 className="text-3xl font-bold">Subscriptions</h1>
        <Button
          variant="outline"
          size="sm"
          onClick={handleRefreshData}
          disabled={isRefreshing}
        >
          {isRefreshing ? (
            <>
              <span className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-b-transparent"></span>
              Refreshing...
            </>
          ) : (
            <>
              <RefreshCw className="mr-2 h-4 w-4" />
              Refresh
            </>
          )}
        </Button>
      </div>

      {/* My Subscriptions Panel */}
      <MySubscriptionsPanel
        subscriptions={subscriptions}
        tiers={subscriptionTiers}
        onManage={handleToggleAutoRenew}
        onCancel={(subscription) => {
          setSubscriptionToCancel(subscription);
          setCancelDialogOpen(true);
        }}
        onUpgrade={handleUpgradeSubscription}
      />

      <Tabs defaultValue="personal" className="w-full mb-8">
        <TabsList className="grid w-full grid-cols-3 mb-8">
          <TabsTrigger value="personal">
            <User className="h-4 w-4 mr-2" />
            Personal
          </TabsTrigger>
          <TabsTrigger value="merchant">
            <Store className="h-4 w-4 mr-2" />
            Merchant
          </TabsTrigger>
          <TabsTrigger value="customer">
            <CreditCard className="h-4 w-4 mr-2" />
            Customer
          </TabsTrigger>
        </TabsList>

        <TabsContent value="personal">
          {activeSubscription && (
            <Card className="mb-8 border shadow-sm">
              <CardHeader className="pb-3">
                <CardTitle>Current Subscription</CardTitle>
                <CardDescription>
                  Your active subscription plan and details
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid gap-6 md:grid-cols-2">
                  <div>
                    <h3 className="text-lg font-semibold">
                      {activeSubscription.subscription_tier.name} Plan
                    </h3>
                    <p className="text-sm text-muted-foreground mt-1">
                      {activeSubscription.subscription_tier.description}
                    </p>
                    <div className="mt-4">
                      <div className="flex justify-between text-sm">
                        <span>Credit Balance:</span>
                        <span className="font-medium">
                          {activeSubscription.credit_balance.toLocaleString()} / {activeSubscription.subscription_tier.credit_limit.toLocaleString()}
                        </span>
                      </div>
                      <div className="h-2 w-full bg-muted rounded-full overflow-hidden mt-1">
                        <div
                          className="h-full bg-primary"
                          style={{
                            width: `${
                              (activeSubscription.credit_balance /
                                activeSubscription.subscription_tier.credit_limit) *
                              100
                            }%`,
                          }}
                        ></div>
                      </div>
                    </div>
                    <div className="mt-4 space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span>Start Date:</span>
                        <span>{formatDate(activeSubscription.start_date)}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Auto-Renew:</span>
                        <span>{activeSubscription.auto_renew ? "Yes" : "No"}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Price:</span>
                        <span>${activeSubscription.subscription_tier.price.toFixed(2)}/month</span>
                      </div>
                      <div className="flex justify-between font-medium text-primary">
                        <span>Usage This Month:</span>
                        <span>{activeSubscription.subscription_tier.credit_limit - activeSubscription.credit_balance} credits</span>
                      </div>
                    </div>
                  </div>
                  <div className="flex flex-col justify-center space-y-4 md:items-end">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleToggleAutoRenew(activeSubscription)}
                    >
                      {activeSubscription.auto_renew
                        ? "Disable Auto-Renew"
                        : "Enable Auto-Renew"}
                    </Button>
                    <Button
                      variant="destructive"
                      size="sm"
                      onClick={() => {
                        setSubscriptionToCancel(activeSubscription);
                        setCancelDialogOpen(true);
                      }}
                    >
                      Cancel Subscription
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          <h2 className="text-2xl font-bold mb-6">Available Plans</h2>
          <div className="grid gap-6 md:grid-cols-3">
            {subscriptionTiers.map((tier) => (
              <Card key={tier.id} className="flex flex-col border shadow-sm hover:shadow-md transition-shadow">
                <CardHeader className="pb-3">
                  <CardTitle>{tier.name}</CardTitle>
                  <CardDescription>{tier.description}</CardDescription>
                </CardHeader>
                <CardContent className="flex-1">
                  <div className="text-3xl font-bold mb-4">
                    ${tier.price.toFixed(2)}
                    <span className="text-sm font-normal text-muted-foreground">
                      /month
                    </span>
                  </div>
                  <div className="space-y-2 mb-6">
                    <div className="flex justify-between text-sm">
                      <span>Credits:</span>
                      <span className="font-medium">
                        {tier.credit_limit.toLocaleString()}
                      </span>
                    </div>
                  </div>
                  <ul className="space-y-2 mb-6">
                    {Array.isArray(tier.features) ? (
                      tier.features.map((featureStr: string, i: number) => {
                        // Parse the feature string which is in JSON format
                        try {
                          // Remove the curly braces and parse as an array
                          const cleanedStr = featureStr.replace(/^\{|\}$/g, '');
                          const featuresArray = JSON.parse(`[${cleanedStr}]`);

                          return featuresArray.map((feature: string, innerIndex: number) => (
                            <li key={`${i}-${innerIndex}`} className="flex items-start">
                              <svg
                                xmlns="http://www.w3.org/2000/svg"
                                viewBox="0 0 20 20"
                                fill="currentColor"
                                className="h-5 w-5 mr-2 text-green-500 flex-shrink-0"
                              >
                                <path
                                  fillRule="evenodd"
                                  d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                                  clipRule="evenodd"
                                />
                              </svg>
                              <span className="text-sm">{feature}</span>
                            </li>
                          ));
                        } catch {
                          // Fallback to displaying the raw string if parsing fails
                          return (
                            <li key={i} className="flex items-start">
                              <svg
                                xmlns="http://www.w3.org/2000/svg"
                                viewBox="0 0 20 20"
                                fill="currentColor"
                                className="h-5 w-5 mr-2 text-green-500 flex-shrink-0"
                              >
                                <path
                                  fillRule="evenodd"
                                  d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                                  clipRule="evenodd"
                                />
                              </svg>
                              <span className="text-sm">{featureStr}</span>
                            </li>
                          );
                        }
                      })
                    ) : (
                      <li className="flex items-start">
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          viewBox="0 0 20 20"
                          fill="currentColor"
                          className="h-5 w-5 mr-2 text-green-500 flex-shrink-0"
                        >
                          <path
                            fillRule="evenodd"
                            d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                            clipRule="evenodd"
                          />
                        </svg>
                        <span className="text-sm">No features available</span>
                      </li>
                    )}
                  </ul>
                </CardContent>
                <CardFooter className="pt-0">
                  <Button
                    className="w-full"
                    variant={
                      activeSubscription?.subscription_tier.id === tier.id
                        ? "outline"
                        : "default"
                    }
                    size="sm"
                    disabled={activeSubscription?.subscription_tier.id === tier.id || isCreatingCheckout}
                    onClick={() => {
                      setSelectedTier(tier);
                      setConfirmDialogOpen(true);
                    }}
                  >
                    {isCreatingCheckout && selectedTier?.id === tier.id ? (
                      <>
                        <span className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-b-transparent"></span>
                        Processing...
                      </>
                    ) : activeSubscription?.subscription_tier.id === tier.id ? (
                      "Current Plan"
                    ) : (
                      "Select Plan"
                    )}
                  </Button>
                </CardFooter>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="merchant">
          <MerchantSubscriptionPanel
            subscriptions={subscriptions}
            createSubscription={createSubscriptionMutation}
          />
        </TabsContent>

        <TabsContent value="customer">
          <CustomerSubscriptionPanel
            subscriptions={subscriptions}
            createSubscription={createSubscriptionMutation}
          />
        </TabsContent>
      </Tabs>

      {/* Confirm Subscription Dialog */}
      <Dialog open={confirmDialogOpen} onOpenChange={setConfirmDialogOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Confirm Subscription</DialogTitle>
            <DialogDescription>
              Are you sure you want to subscribe to the {selectedTier?.name} plan?
              {activeSubscription && (
                <Alert className="mt-4">
                  <AlertDescription>
                    This will replace your current {activeSubscription.subscription_tier.name} plan.
                  </AlertDescription>
                </Alert>
              )}
            </DialogDescription>
          </DialogHeader>
          <div className="py-4">
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span>Plan:</span>
                <span className="font-medium">{selectedTier?.name}</span>
              </div>
              <div className="flex justify-between">
                <span>Price:</span>
                <span className="font-medium">
                  ${selectedTier?.price.toFixed(2)}/month
                </span>
              </div>
              <div className="flex justify-between">
                <span>Credits:</span>
                <span className="font-medium">
                  {selectedTier?.credit_limit.toLocaleString()}
                </span>
              </div>
            </div>
          </div>
          <DialogFooter className="sm:justify-end">
            <Button variant="outline" size="sm" onClick={() => setConfirmDialogOpen(false)}>
              Cancel
            </Button>
            <Button
              size="sm"
              onClick={handleCreateSubscription}
              disabled={isCreatingCheckout}
            >
              {isCreatingCheckout ? (
                <>
                  <span className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-b-transparent"></span>
                  Processing...
                </>
              ) : (
                "Confirm"
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Cancel Subscription Dialog */}
      <Dialog open={cancelDialogOpen} onOpenChange={setCancelDialogOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Cancel Subscription</DialogTitle>
            <DialogDescription>
              Are you sure you want to cancel your {subscriptionToCancel?.subscription_tier.name} subscription?
            </DialogDescription>
          </DialogHeader>
          <div className="py-4">
            <Alert>
              <AlertDescription>
                Your subscription will be cancelled immediately. You will still have access to your remaining credits until they are used up.
              </AlertDescription>
            </Alert>
          </div>
          <DialogFooter className="sm:justify-end">
            <Button variant="outline" size="sm" onClick={() => setCancelDialogOpen(false)}>
              Keep Subscription
            </Button>
            <Button variant="destructive" size="sm" onClick={handleCancelSubscription}>
              Cancel Subscription
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
