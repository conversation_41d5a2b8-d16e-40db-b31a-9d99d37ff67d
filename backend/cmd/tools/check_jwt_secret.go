package main

import (
	"fmt"
	"log"
	"os"

	"github.com/joho/godotenv"
)

func main() {
	// Load environment variables
	if err := godotenv.Load(); err != nil {
		log.Println("No .env file found, using environment variables")
	}

	// Check JWT_SECRET
	jwtSecret := os.Getenv("JWT_SECRET")
	if jwtSecret == "" {
		fmt.Println("WARNING: JWT_SECRET is not set!")
	} else {
		fmt.Printf("JWT_SECRET is set (length: %d)\n", len(jwtSecret))
	}

	// Check JWT_REFRESH_SECRET
	jwtRefreshSecret := os.Getenv("JWT_REFRESH_SECRET")
	if jwtRefreshSecret == "" {
		fmt.Println("WARNING: JWT_REFRESH_SECRET is not set!")
	} else {
		fmt.Printf("JWT_REFRESH_SECRET is set (length: %d)\n", len(jwtRefreshSecret))
	}

	fmt.Println("\nMake sure these secrets are consistent across all environments!")
}
