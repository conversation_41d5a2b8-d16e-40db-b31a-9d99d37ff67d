"use client";

import { useEffect, useRef, useCallback } from "react";
import { Html5Qrcode } from "html5-qrcode";

interface Html5QrcodePluginProps {
  fps?: number;
  qrbox?: number;
  aspectRatio?: number;
  disableFlip?: boolean;
  qrCodeSuccessCallback: (decodedText: string, decodedResult: any) => void;
  qrCodeErrorCallback?: (error: any) => void;
}

const qrcodeRegionId = "html5qr-code-full-region";

const Html5QrcodePlugin = ({
  fps = 10,
  qrbox = 250,
  aspectRatio = 1.0,
  disableFlip = false,
  qrCodeSuccessCallback,
  qrCodeErrorCallback,
}: Html5QrcodePluginProps) => {
  const html5QrCodeRef = useRef<Html5Qrcode | null>(null);
  const successCallbackRef = useRef(qrCodeSuccessCallback);
  const errorCallbackRef = useRef(qrCodeErrorCallback);

  // Update refs when callbacks change
  useEffect(() => {
    successCallbackRef.current = qrCodeSuccessCallback;
  }, [qrCodeSuccessCallback]);

  useEffect(() => {
    errorCallbackRef.current = qrCodeErrorCallback;
  }, [qrCodeErrorCallback]);

  // Stable callback functions
  const stableSuccessCallback = useCallback((decodedText: string, decodedResult: any) => {
    successCallbackRef.current(decodedText, decodedResult);
  }, []);

  const stableErrorCallback = useCallback((error: any) => {
    // Only call error callback for actual errors, not "no QR code found" messages
    if (errorCallbackRef.current && !error.includes?.("No QR code found")) {
      errorCallbackRef.current(error);
    }
  }, []);

  useEffect(() => {
    // Create instance of Html5Qrcode
    const html5QrCode = new Html5Qrcode(qrcodeRegionId);
    html5QrCodeRef.current = html5QrCode;

    // Start scanning
    const config = { fps, qrbox, aspectRatio, disableFlip };
    html5QrCode.start(
      { facingMode: "environment" }, // Use the back camera
      config,
      stableSuccessCallback,
      stableErrorCallback
    ).catch((err) => {
      console.error("Error starting QR code scanner:", err);
    });

    // Cleanup on unmount
    return () => {
      if (html5QrCodeRef.current && html5QrCodeRef.current.isScanning) {
        html5QrCodeRef.current
          .stop()
          .catch((err) => console.error("Error stopping QR code scanner:", err));
      }
    };
  }, [fps, qrbox, aspectRatio, disableFlip, stableSuccessCallback, stableErrorCallback]);

  return (
    <div className="w-full">
      <div 
        id={qrcodeRegionId} 
        className="w-full rounded-md overflow-hidden"
        style={{ 
          position: "relative",
          padding: "0",
          border: "1px solid #e5e7eb",
          minHeight: "300px"
        }}
      />
      <p className="text-xs text-muted-foreground text-center mt-2">
        Position the QR code within the frame to scan
      </p>
    </div>
  );
};

export default Html5QrcodePlugin;
