#!/bin/bash

# This script deploys the scheduled credits Cloud Function to GCP
# and sets up a Cloud Scheduler job to trigger it daily

# Configuration
PROJECT_ID=${PROJECT_ID:-"adc-credit"}
REGION=${REGION:-"us-central1"}
FUNCTION_NAME="process-scheduled-credits"
SERVICE_ACCOUNT=${SERVICE_ACCOUNT:-"$FUNCTION_NAME-sa@$PROJECT_ID.iam.gserviceaccount.com"}
SCHEDULER_JOB_NAME="daily-credit-refresh"
SCHEDULER_SCHEDULE="0 0 * * *"  # Run at midnight every day
SCHEDULER_API_KEY=${SCHEDULER_API_KEY:-""}

# Check if API key is provided
if [ -z "$SCHEDULER_API_KEY" ]; then
  echo "Error: SCHEDULER_API_KEY environment variable is required"
  echo "Usage: SCHEDULER_API_KEY=your_api_key ./deploy_scheduled_credits.sh"
  exit 1
fi

# Check if gcloud is installed
if ! command -v gcloud &> /dev/null; then
  echo "Error: gcloud CLI is not installed"
  echo "Please install the Google Cloud SDK: https://cloud.google.com/sdk/docs/install"
  exit 1
fi

# Check if user is logged in
if ! gcloud auth list --filter=status:ACTIVE --format="value(account)" | grep -q "@"; then
  echo "Error: Not logged in to gcloud"
  echo "Please run 'gcloud auth login' first"
  exit 1
fi

# Set the project
echo "Setting project to $PROJECT_ID..."
gcloud config set project $PROJECT_ID

# Create service account if it doesn't exist
echo "Creating service account if it doesn't exist..."
if ! gcloud iam service-accounts describe $SERVICE_ACCOUNT &> /dev/null; then
  gcloud iam service-accounts create $(echo $SERVICE_ACCOUNT | cut -d '@' -f 1) \
    --display-name="Service account for $FUNCTION_NAME"
fi

# Grant necessary permissions to the service account
echo "Granting necessary permissions to the service account..."
gcloud projects add-iam-policy-binding $PROJECT_ID \
  --member="serviceAccount:$SERVICE_ACCOUNT" \
  --role="roles/cloudsql.client"

# Deploy the Cloud Function
echo "Deploying Cloud Function..."
gcloud functions deploy $FUNCTION_NAME \
  --gen2 \
  --runtime=go123 \
  --region=$REGION \
  --source=./cmd/cloud-functions/process_scheduled_credits \
  --entry-point=ProcessScheduledCredits \
  --trigger-http \
  --service-account=$SERVICE_ACCOUNT \
  --set-env-vars="SCHEDULER_API_KEY=$SCHEDULER_API_KEY,DATABASE_URL=$DATABASE_URL" \
  --allow-unauthenticated

# Get the function URL
FUNCTION_URL=$(gcloud functions describe $FUNCTION_NAME \
  --gen2 \
  --region=$REGION \
  --format="value(serviceConfig.uri)")

echo "Cloud Function deployed at: $FUNCTION_URL"

# Create or update the Cloud Scheduler job
echo "Setting up Cloud Scheduler job..."
if gcloud scheduler jobs describe $SCHEDULER_JOB_NAME --location=$REGION &> /dev/null; then
  # Update existing job
  gcloud scheduler jobs update http $SCHEDULER_JOB_NAME \
    --location=$REGION \
    --schedule="$SCHEDULER_SCHEDULE" \
    --uri="$FUNCTION_URL" \
    --http-method=POST \
    --headers="X-API-Key=$SCHEDULER_API_KEY"
else
  # Create new job
  gcloud scheduler jobs create http $SCHEDULER_JOB_NAME \
    --location=$REGION \
    --schedule="$SCHEDULER_SCHEDULE" \
    --uri="$FUNCTION_URL" \
    --http-method=POST \
    --headers="X-API-Key=$SCHEDULER_API_KEY"
fi

echo "Deployment completed successfully!"
echo "Cloud Function: $FUNCTION_NAME"
echo "Cloud Scheduler job: $SCHEDULER_JOB_NAME (runs $SCHEDULER_SCHEDULE)"
echo ""
echo "To test the function manually, run:"
echo "curl -X POST -H \"X-API-Key: $SCHEDULER_API_KEY\" $FUNCTION_URL"
