'use client';

import { ReactNode } from 'react';
import { SessionProvider } from '@/lib/session/sessionContext';
import { SessionProvider as NextAuthSessionProvider } from 'next-auth/react';

interface ProvidersProps {
  children: ReactNode;
}

/**
 * Application providers wrapper component
 * This component wraps the application with all necessary providers
 */
export function Providers({ children }: ProvidersProps) {
  return (
    <NextAuthSessionProvider>
      <SessionProvider>
        {children}
      </SessionProvider>
    </NextAuthSessionProvider>
  );
}

export default Providers;
