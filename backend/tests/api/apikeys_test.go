package api

import (
	"testing"

	"github.com/adc-credit/backend/internal/handlers"
	testutils "github.com/adc-credit/backend/internal/testing"
	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/suite"
)

type APIKeysTestSuite struct {
	suite.Suite
	router *gin.Engine
}

func (suite *APIKeysTestSuite) SetupSuite() {
	// Setup test environment
	err := testutils.SetupTestEnvironment()
	suite.Require().NoError(err)

	// Setup router with API key routes
	suite.router = gin.New()
	suite.router.Use(gin.Recovery())
	
	// Setup API key routes
	apiKeyHandler := handlers.NewAPIKeyHandler(testutils.TestDB)
	api := suite.router.Group("/api/v1")
	{
		apikeys := api.Group("/apikeys")
		{
			apikeys.GET("", apiKeyHandler.ListAPIKeys)
			apikeys.POST("", apiKeyHandler.CreateAPIKey)
			apikeys.GET("/:id", apiKeyHandler.GetAPIKey)
			apikeys.PUT("/:id", apiKeyHandler.UpdateAPIKey)
			apikeys.DELETE("/:id", apiKeyHandler.DeleteAPIKey)
			apikeys.POST("/:id/regenerate", apiKeyHandler.RegenerateAPIKey)
		}
	}
}

func (suite *APIKeysTestSuite) TearDownSuite() {
	testutils.CleanupTestEnvironment()
}

func (suite *APIKeysTestSuite) SetupTest() {
	testutils.CleanTestData()
}

func (suite *APIKeysTestSuite) TestCreateAPIKey() {
	t := suite.T()

	// Create test user with subscription
	user, subscription, token := testutils.CreateTestUserWithSubscription(t, "<EMAIL>", "Test User", 1000)
	defer testutils.CleanupTestUser(t, user.ID)

	// Test creating API key
	createData := map[string]interface{}{
		"name":        "Test API Key",
		"description": "API key for testing",
		"permissions": []string{"read", "write"},
	}

	req := testutils.WithAuth(testutils.TestRequest{
		Method: "POST",
		URL:    "/api/v1/apikeys",
		Body:   createData,
	}, user.ID, user.Email)

	resp := testutils.MakeRequest(t, suite.router, req)

	// Assertions
	testutils.AssertSuccessResponse(t, resp)
	assert.Equal(t, 201, resp.StatusCode)
	
	// Check response structure
	testutils.AssertJSONResponse(t, resp, map[string]interface{}{
		"name":        "Test API Key",
		"description": "API key for testing",
	})
	
	testutils.AssertValidUUID(t, resp.Body["id"])
	testutils.AssertValidTimestamp(t, resp.Body["created_at"])
	
	// Should include the API key in response
	assert.Contains(t, resp.Body, "key")
	assert.NotEmpty(t, resp.Body["key"])
}

func (suite *APIKeysTestSuite) TestCreateAPIKeyInvalidData() {
	t := suite.T()

	// Create test user
	user, _, token := testutils.CreateTestUserWithSubscription(t, "<EMAIL>", "Test User", 1000)
	defer testutils.CleanupTestUser(t, user.ID)

	// Test with missing name
	createData := map[string]interface{}{
		"description": "API key without name",
	}

	req := testutils.WithAuth(testutils.TestRequest{
		Method: "POST",
		URL:    "/api/v1/apikeys",
		Body:   createData,
	}, user.ID, user.Email)

	resp := testutils.MakeRequest(t, suite.router, req)

	// Should return 400 Bad Request
	testutils.AssertErrorResponse(t, resp, 400)
}

func (suite *APIKeysTestSuite) TestListAPIKeys() {
	t := suite.T()

	// Create test user with API keys
	user, apiKey, token := testutils.CreateTestUserWithAPIKey(t, "<EMAIL>", "Test User")
	defer testutils.CleanupTestUser(t, user.ID)

	// Create additional API key
	apiKey2, err := testutils.CreateTestAPIKey(user.ID, "Second API Key")
	suite.Require().NoError(err)

	// Test listing API keys
	req := testutils.WithAuth(testutils.TestRequest{
		Method: "GET",
		URL:    "/api/v1/apikeys",
		QueryParams: map[string]string{
			"limit": "10",
			"page":  "1",
		},
	}, user.ID, user.Email)

	resp := testutils.MakeRequest(t, suite.router, req)

	// Assertions
	testutils.AssertSuccessResponse(t, resp)
	assert.Equal(t, 200, resp.StatusCode)
	
	// Check pagination structure
	testutils.AssertPaginatedResponse(t, resp)
	testutils.AssertArrayResponse(t, resp, 2) // At least 2 API keys
	
	// Check that API keys don't include the actual key value
	data := resp.Body["data"].([]interface{})
	firstAPIKey := data[0].(map[string]interface{})
	assert.NotContains(t, firstAPIKey, "key")
}

func (suite *APIKeysTestSuite) TestGetAPIKey() {
	t := suite.T()

	// Create test user with API key
	user, apiKey, token := testutils.CreateTestUserWithAPIKey(t, "<EMAIL>", "Test User")
	defer testutils.CleanupTestUser(t, user.ID)

	// Test getting API key
	req := testutils.WithAuth(testutils.TestRequest{
		Method: "GET",
		URL:    "/api/v1/apikeys/" + apiKey.ID.String(),
	}, user.ID, user.Email)

	resp := testutils.MakeRequest(t, suite.router, req)

	// Assertions
	testutils.AssertSuccessResponse(t, resp)
	assert.Equal(t, 200, resp.StatusCode)
	
	// Check response structure
	testutils.AssertJSONResponse(t, resp, map[string]interface{}{
		"name": apiKey.Name,
	})
	
	testutils.AssertValidUUID(t, resp.Body["id"])
	
	// Should not include the actual key value
	assert.NotContains(t, resp.Body, "key")
}

func (suite *APIKeysTestSuite) TestGetAPIKeyNotFound() {
	t := suite.T()

	// Create test user
	user, _, token := testutils.CreateTestUserWithSubscription(t, "<EMAIL>", "Test User", 1000)
	defer testutils.CleanupTestUser(t, user.ID)

	// Test getting non-existent API key
	req := testutils.WithAuth(testutils.TestRequest{
		Method: "GET",
		URL:    "/api/v1/apikeys/00000000-0000-0000-0000-000000000000",
	}, user.ID, user.Email)

	resp := testutils.MakeRequest(t, suite.router, req)

	// Should return 404 Not Found
	testutils.AssertErrorResponse(t, resp, 404)
}

func (suite *APIKeysTestSuite) TestUpdateAPIKey() {
	t := suite.T()

	// Create test user with API key
	user, apiKey, token := testutils.CreateTestUserWithAPIKey(t, "<EMAIL>", "Test User")
	defer testutils.CleanupTestUser(t, user.ID)

	// Test updating API key
	updateData := map[string]interface{}{
		"name":        "Updated API Key",
		"description": "Updated description",
		"permissions": []string{"read"},
	}

	req := testutils.WithAuth(testutils.TestRequest{
		Method: "PUT",
		URL:    "/api/v1/apikeys/" + apiKey.ID.String(),
		Body:   updateData,
	}, user.ID, user.Email)

	resp := testutils.MakeRequest(t, suite.router, req)

	// Assertions
	testutils.AssertSuccessResponse(t, resp)
	assert.Equal(t, 200, resp.StatusCode)
	
	// Check updated data
	testutils.AssertJSONResponse(t, resp, map[string]interface{}{
		"name":        "Updated API Key",
		"description": "Updated description",
	})
}

func (suite *APIKeysTestSuite) TestDeleteAPIKey() {
	t := suite.T()

	// Create test user with API key
	user, apiKey, token := testutils.CreateTestUserWithAPIKey(t, "<EMAIL>", "Test User")
	defer testutils.CleanupTestUser(t, user.ID)

	// Test deleting API key
	req := testutils.WithAuth(testutils.TestRequest{
		Method: "DELETE",
		URL:    "/api/v1/apikeys/" + apiKey.ID.String(),
	}, user.ID, user.Email)

	resp := testutils.MakeRequest(t, suite.router, req)

	// Assertions
	testutils.AssertSuccessResponse(t, resp)
	assert.Equal(t, 204, resp.StatusCode)

	// Verify API key is deleted
	var count int64
	testutils.TestDB.Model(&apiKey).Where("id = ?", apiKey.ID).Count(&count)
	assert.Equal(t, int64(0), count)
}

func (suite *APIKeysTestSuite) TestRegenerateAPIKey() {
	t := suite.T()

	// Create test user with API key
	user, apiKey, token := testutils.CreateTestUserWithAPIKey(t, "<EMAIL>", "Test User")
	defer testutils.CleanupTestUser(t, user.ID)

	// Store original key
	originalKey := apiKey.Key

	// Test regenerating API key
	req := testutils.WithAuth(testutils.TestRequest{
		Method: "POST",
		URL:    "/api/v1/apikeys/" + apiKey.ID.String() + "/regenerate",
	}, user.ID, user.Email)

	resp := testutils.MakeRequest(t, suite.router, req)

	// Assertions
	testutils.AssertSuccessResponse(t, resp)
	assert.Equal(t, 200, resp.StatusCode)
	
	// Should include new API key
	assert.Contains(t, resp.Body, "key")
	newKey := resp.Body["key"].(string)
	assert.NotEmpty(t, newKey)
	assert.NotEqual(t, originalKey, newKey)
}

func (suite *APIKeysTestSuite) TestAPIKeyUnauthorizedAccess() {
	t := suite.T()

	// Create two users
	user1, apiKey1, token1 := testutils.CreateTestUserWithAPIKey(t, "<EMAIL>", "User 1")
	user2, _, token2 := testutils.CreateTestUserWithAPIKey(t, "<EMAIL>", "User 2")
	defer func() {
		testutils.CleanupTestUser(t, user1.ID)
		testutils.CleanupTestUser(t, user2.ID)
	}()

	// Test user2 trying to access user1's API key
	req := testutils.WithAuth(testutils.TestRequest{
		Method: "GET",
		URL:    "/api/v1/apikeys/" + apiKey1.ID.String(),
	}, user2.ID, user2.Email)

	resp := testutils.MakeRequest(t, suite.router, req)

	// Should return 403 Forbidden or 404 Not Found
	assert.True(t, resp.StatusCode == 403 || resp.StatusCode == 404)
}

// Run the test suite
func TestAPIKeysTestSuite(t *testing.T) {
	suite.Run(t, new(APIKeysTestSuite))
}
