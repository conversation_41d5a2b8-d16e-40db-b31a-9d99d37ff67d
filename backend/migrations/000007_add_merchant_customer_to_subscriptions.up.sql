-- Add merchant_shop_id and shop_customer_id columns to subscriptions table
ALTER TABLE subscriptions ADD COLUMN IF NOT EXISTS merchant_shop_id UUID;
ALTER TABLE subscriptions ADD COLUMN IF NOT EXISTS shop_customer_id UUID;

-- Add foreign key constraints
ALTER TABLE subscriptions ADD CONSTRAINT fk_subscriptions_merchant_shop
    FOREIGN KEY (merchant_shop_id) REFERENCES merchant_shops(id) ON DELETE SET NULL;

ALTER TABLE subscriptions ADD CONSTRAINT fk_subscriptions_shop_customer
    FOREIGN KEY (shop_customer_id) REFERENCES shop_customers(id) ON DELETE SET NULL;

-- Add subscription_type column to indicate the type of subscription
ALTER TABLE subscriptions ADD COLUMN IF NOT EXISTS subscription_type VARCHAR(20) DEFAULT 'personal';

-- Update existing subscriptions to have subscription_type = 'personal'
UPDATE subscriptions SET subscription_type = 'personal' WHERE subscription_type IS NULL;

-- Create index on subscription_type for faster queries
CREATE INDEX IF NOT EXISTS idx_subscriptions_type ON subscriptions(subscription_type);

-- <PERSON>reate indexes on the new foreign key columns
CREATE INDEX IF NOT EXISTS idx_subscriptions_merchant_shop_id ON subscriptions(merchant_shop_id);
CREATE INDEX IF NOT EXISTS idx_subscriptions_shop_customer_id ON subscriptions(shop_customer_id);
