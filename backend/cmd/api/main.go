package main

import (
	"log"
	"os"

	"github.com/adc-credit/backend/internal/database"
	"github.com/adc-credit/backend/internal/routes"
	"github.com/gin-gonic/gin"
	"github.com/joho/godotenv"
)

func main() {
	// Load environment variables
	// Try to load from the current directory
	if err := godotenv.Load(); err != nil {
		// Try to load from the project root directory
		if err := godotenv.Load("../../.env"); err != nil {
			// Try with absolute path
			if err := godotenv.Load("/Users/<USER>/Desktop/adc/adc-credit/backend/.env"); err != nil {
				log.Println("No .env file found, using environment variables")
			} else {
				log.Println("Loaded .env file from absolute path")
			}
		} else {
			log.Println("Loaded .env file from project root")
		}
	} else {
		log.Println("Loaded .env file from current directory")
	}

	// Print environment variables for debugging
	log.Printf("STRIPE_SECRET_KEY: %s", os.Getenv("STRIPE_SECRET_KEY"))
	log.Printf("STRIPE_WEBHOOK_SECRET: %s", os.Getenv("STRIPE_WEBHOOK_SECRET"))

	// Initialize database connection
	if err := database.InitDB(); err != nil {
		log.Fatalf("Failed to connect to database: %v", err)
	}

	// Set up Gin router
	r := gin.Default()

	// Enable CORS with proper configuration for credentials
	r.Use(func(c *gin.Context) {
		// Get the origin from the request
		origin := c.Request.Header.Get("Origin")
		if origin == "" {
			// Default to all origins for development
			origin = "*"
		}

		// Set CORS headers
		c.Writer.Header().Set("Access-Control-Allow-Origin", origin)
		c.Writer.Header().Set("Access-Control-Allow-Credentials", "true")
		c.Writer.Header().Set("Access-Control-Allow-Headers", "Content-Type, Content-Length, Accept-Encoding, X-CSRF-Token, Authorization, X-API-Key, accept, origin, Cache-Control, X-Requested-With, Cookie")
		c.Writer.Header().Set("Access-Control-Allow-Methods", "POST, OPTIONS, GET, PUT, DELETE, PATCH")
		c.Writer.Header().Set("Access-Control-Expose-Headers", "Content-Length, Content-Type, Set-Cookie")

		// Log the request for debugging
		log.Printf("Received request: %s %s from origin: %s", c.Request.Method, c.Request.URL.Path, origin)

		// Handle preflight requests
		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(204)
			return
		}

		c.Next()
	})

	// Register routes
	routes.RegisterRoutes(r)

	// Get port from environment variable or use default
	port := os.Getenv("PORT")
	if port == "" {
		port = "8400"
	}

	// Start server
	log.Printf("Server starting on port %s...", port)
	if err := r.Run(":" + port); err != nil {
		log.Fatalf("Failed to start server: %v", err)
	}
}
