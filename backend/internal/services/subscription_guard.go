package services

import (
	"fmt"
	"time"

	"github.com/adc-credit/backend/internal/database"
	"github.com/adc-credit/backend/internal/models"
	"github.com/google/uuid"
	"gorm.io/gorm"
)

// SubscriptionGuard provides subscription limit checking and enforcement
type SubscriptionGuard struct {
	db *gorm.DB
}

// NewSubscriptionGuard creates a new subscription guard instance
func NewSubscriptionGuard() *SubscriptionGuard {
	return &SubscriptionGuard{
		db: database.DB,
	}
}

// LimitCheckResult represents the result of a limit check
type LimitCheckResult struct {
	Allowed      bool   `json:"allowed"`
	CurrentUsage int    `json:"current_usage"`
	Limit        int    `json:"limit"`
	Unlimited    bool   `json:"unlimited"`
	Message      string `json:"message"`
}

// GetUserActiveSubscription gets the user's active subscription with tier
func (sg *SubscriptionGuard) GetUserActiveSubscription(userID uuid.UUID, subscriptionType string) (*models.Subscription, error) {
	var subscription models.Subscription
	query := sg.db.Where("user_id = ? AND status = ?", userID, "active").
		Preload("SubscriptionTier")

	if subscriptionType != "" {
		query = query.Where("subscription_type = ?", subscriptionType)
	}

	if err := query.First(&subscription).Error; err != nil {
		return nil, err
	}

	return &subscription, nil
}

// CheckShopLimit checks if user can create more shops
func (sg *SubscriptionGuard) CheckShopLimit(userID uuid.UUID) (*LimitCheckResult, error) {
	subscription, err := sg.GetUserActiveSubscription(userID, "personal")
	if err != nil {
		return &LimitCheckResult{
			Allowed: false,
			Message: "No active subscription found",
		}, err
	}

	// Count current shops (using MerchantShop for now until Shop model is fixed)
	var currentShops int64
	if err := sg.db.Model(&models.MerchantShop{}).Where("owner_user_id = ?", userID).Count(&currentShops).Error; err != nil {
		return nil, err
	}

	// Check if unlimited
	if subscription.SubscriptionTier.UnlimitedShops {
		return &LimitCheckResult{
			Allowed:      true,
			CurrentUsage: int(currentShops),
			Unlimited:    true,
			Message:      "Unlimited shops allowed",
		}, nil
	}

	// Check limit
	allowed := int(currentShops) < subscription.SubscriptionTier.MaxShops
	message := fmt.Sprintf("Using %d of %d shops", currentShops, subscription.SubscriptionTier.MaxShops)
	if !allowed {
		message = fmt.Sprintf("Shop limit reached (%d/%d). Upgrade your subscription to create more shops.",
			currentShops, subscription.SubscriptionTier.MaxShops)
	}

	return &LimitCheckResult{
		Allowed:      allowed,
		CurrentUsage: int(currentShops),
		Limit:        subscription.SubscriptionTier.MaxShops,
		Unlimited:    false,
		Message:      message,
	}, nil
}

// CheckCustomerLimit checks if shop can add more customers
func (sg *SubscriptionGuard) CheckCustomerLimit(shopID uuid.UUID) (*LimitCheckResult, error) {
	// Get shop owner
	var shop models.Shop
	if err := sg.db.First(&shop, "id = ?", shopID).Error; err != nil {
		return nil, err
	}

	subscription, err := sg.GetUserActiveSubscription(shop.OwnerUserID, "personal")
	if err != nil {
		return &LimitCheckResult{
			Allowed: false,
			Message: "No active subscription found",
		}, err
	}

	// Count current customers for this shop
	var currentCustomers int64
	if err := sg.db.Model(&models.ShopCustomer{}).Where("shop_id = ?", shopID).Count(&currentCustomers).Error; err != nil {
		return nil, err
	}

	// Check if unlimited
	if subscription.SubscriptionTier.UnlimitedCustomers {
		return &LimitCheckResult{
			Allowed:      true,
			CurrentUsage: int(currentCustomers),
			Unlimited:    true,
			Message:      "Unlimited customers allowed",
		}, nil
	}

	// Check limit
	allowed := int(currentCustomers) < subscription.SubscriptionTier.MaxCustomersPerShop
	message := fmt.Sprintf("Using %d of %d customers", currentCustomers, subscription.SubscriptionTier.MaxCustomersPerShop)
	if !allowed {
		message = fmt.Sprintf("Customer limit reached (%d/%d). Upgrade your subscription to add more customers.",
			currentCustomers, subscription.SubscriptionTier.MaxCustomersPerShop)
	}

	return &LimitCheckResult{
		Allowed:      allowed,
		CurrentUsage: int(currentCustomers),
		Limit:        subscription.SubscriptionTier.MaxCustomersPerShop,
		Unlimited:    false,
		Message:      message,
	}, nil
}

// CheckAPIKeyLimit checks if shop can create more API keys
func (sg *SubscriptionGuard) CheckAPIKeyLimit(shopID uuid.UUID) (*LimitCheckResult, error) {
	// Get shop owner
	var shop models.Shop
	if err := sg.db.First(&shop, "id = ?", shopID).Error; err != nil {
		return nil, err
	}

	subscription, err := sg.GetUserActiveSubscription(shop.OwnerUserID, "personal")
	if err != nil {
		return &LimitCheckResult{
			Allowed: false,
			Message: "No active subscription found",
		}, err
	}

	// Count current API keys for this shop
	var currentAPIKeys int64
	if err := sg.db.Model(&models.APIKey{}).Where("shop_id = ?", shopID).Count(&currentAPIKeys).Error; err != nil {
		return nil, err
	}

	// Check limit
	allowed := int(currentAPIKeys) < subscription.SubscriptionTier.MaxAPIKeysPerShop
	message := fmt.Sprintf("Using %d of %d API keys", currentAPIKeys, subscription.SubscriptionTier.MaxAPIKeysPerShop)
	if !allowed {
		message = fmt.Sprintf("API key limit reached (%d/%d). Upgrade your subscription to create more API keys.",
			currentAPIKeys, subscription.SubscriptionTier.MaxAPIKeysPerShop)
	}

	return &LimitCheckResult{
		Allowed:      allowed,
		CurrentUsage: int(currentAPIKeys),
		Limit:        subscription.SubscriptionTier.MaxAPIKeysPerShop,
		Unlimited:    false,
		Message:      message,
	}, nil
}

// CheckBranchLimit checks if shop can create more branches
func (sg *SubscriptionGuard) CheckBranchLimit(shopID uuid.UUID) (*LimitCheckResult, error) {
	// Get shop owner
	var shop models.Shop
	if err := sg.db.First(&shop, "id = ?", shopID).Error; err != nil {
		return nil, err
	}

	subscription, err := sg.GetUserActiveSubscription(shop.OwnerUserID, "personal")
	if err != nil {
		return &LimitCheckResult{
			Allowed: false,
			Message: "No active subscription found",
		}, err
	}

	// Count current branches for this shop
	var currentBranches int64
	if err := sg.db.Model(&models.ShopBranch{}).Where("shop_id = ?", shopID).Count(&currentBranches).Error; err != nil {
		return nil, err
	}

	// Check if unlimited
	if subscription.SubscriptionTier.UnlimitedBranches {
		return &LimitCheckResult{
			Allowed:      true,
			CurrentUsage: int(currentBranches),
			Unlimited:    true,
			Message:      "Unlimited branches allowed",
		}, nil
	}

	// Check limit
	allowed := int(currentBranches) < subscription.SubscriptionTier.MaxBranchesPerShop
	message := fmt.Sprintf("Using %d of %d branches", currentBranches, subscription.SubscriptionTier.MaxBranchesPerShop)
	if !allowed {
		message = fmt.Sprintf("Branch limit reached (%d/%d). Upgrade your subscription to create more branches.",
			currentBranches, subscription.SubscriptionTier.MaxBranchesPerShop)
	}

	return &LimitCheckResult{
		Allowed:      allowed,
		CurrentUsage: int(currentBranches),
		Limit:        subscription.SubscriptionTier.MaxBranchesPerShop,
		Unlimited:    false,
		Message:      message,
	}, nil
}

// CheckQRCodeLimit checks if user can generate more QR codes this month
func (sg *SubscriptionGuard) CheckQRCodeLimit(userID uuid.UUID) (*LimitCheckResult, error) {
	subscription, err := sg.GetUserActiveSubscription(userID, "personal")
	if err != nil {
		return &LimitCheckResult{
			Allowed: false,
			Message: "No active subscription found",
		}, err
	}

	// Check if unlimited
	if subscription.SubscriptionTier.UnlimitedQRCodes {
		return &LimitCheckResult{
			Allowed:   true,
			Unlimited: true,
			Message:   "Unlimited QR codes allowed",
		}, nil
	}

	// Count QR codes generated this month
	now := time.Now()
	startOfMonth := time.Date(now.Year(), now.Month(), 1, 0, 0, 0, 0, now.Location())
	endOfMonth := startOfMonth.AddDate(0, 1, 0)

	var currentQRCodes int64
	// Count from credit codes table (assuming QR codes are tracked there)
	// Use merchant_shops table instead of shops for now
	if err := sg.db.Model(&models.CreditCode{}).
		Joins("JOIN merchant_shops ON credit_codes.shop_id = merchant_shops.id").
		Where("merchant_shops.owner_user_id = ? AND credit_codes.created_at >= ? AND credit_codes.created_at < ?",
			userID, startOfMonth, endOfMonth).
		Count(&currentQRCodes).Error; err != nil {
		return nil, err
	}

	// Check if unlimited QR codes
	if subscription.SubscriptionTier.UnlimitedQRCodes {
		return &LimitCheckResult{
			Allowed:      true,
			CurrentUsage: int(currentQRCodes),
			Limit:        0, // 0 indicates unlimited
			Unlimited:    true,
			Message:      fmt.Sprintf("Using %d QR codes this month (unlimited)", currentQRCodes),
		}, nil
	}

	// Check limit for non-unlimited plans
	allowed := int(currentQRCodes) < subscription.SubscriptionTier.MaxQRCodesPerMonth
	message := fmt.Sprintf("Using %d of %d QR codes this month", currentQRCodes, subscription.SubscriptionTier.MaxQRCodesPerMonth)
	if !allowed {
		message = fmt.Sprintf("QR code limit reached (%d/%d). Upgrade your subscription to generate more QR codes.",
			currentQRCodes, subscription.SubscriptionTier.MaxQRCodesPerMonth)
	}

	return &LimitCheckResult{
		Allowed:      allowed,
		CurrentUsage: int(currentQRCodes),
		Limit:        subscription.SubscriptionTier.MaxQRCodesPerMonth,
		Unlimited:    false,
		Message:      message,
	}, nil
}

// CheckWebhookLimit checks if user can create more webhooks
func (sg *SubscriptionGuard) CheckWebhookLimit(userID uuid.UUID) (*LimitCheckResult, error) {
	subscription, err := sg.GetUserActiveSubscription(userID, "personal")
	if err != nil {
		return &LimitCheckResult{
			Allowed: false,
			Message: "No active subscription found",
		}, err
	}

	// Count current webhooks
	var currentWebhooks int64
	if err := sg.db.Model(&models.Webhook{}).Where("user_id = ?", userID).Count(&currentWebhooks).Error; err != nil {
		return nil, err
	}

	// Check limit - if MaxWebhooks is 0, no webhooks are allowed
	allowed := subscription.SubscriptionTier.MaxWebhooks > 0 && int(currentWebhooks) < subscription.SubscriptionTier.MaxWebhooks
	message := fmt.Sprintf("Using %d of %d webhooks", currentWebhooks, subscription.SubscriptionTier.MaxWebhooks)
	if subscription.SubscriptionTier.MaxWebhooks == 0 {
		message = "Webhooks are not available in your subscription tier. Upgrade to create webhooks."
	} else if !allowed {
		message = fmt.Sprintf("Webhook limit reached (%d/%d). Upgrade your subscription to create more webhooks.",
			currentWebhooks, subscription.SubscriptionTier.MaxWebhooks)
	}

	return &LimitCheckResult{
		Allowed:      allowed,
		CurrentUsage: int(currentWebhooks),
		Limit:        subscription.SubscriptionTier.MaxWebhooks,
		Unlimited:    false,
		Message:      message,
	}, nil
}

// CheckShopTypeAllowed checks if user can create shop of specific type
func (sg *SubscriptionGuard) CheckShopTypeAllowed(userID uuid.UUID, shopType string) (*LimitCheckResult, error) {
	subscription, err := sg.GetUserActiveSubscription(userID, "personal")
	if err != nil {
		return &LimitCheckResult{
			Allowed: false,
			Message: "No active subscription found",
		}, err
	}

	// Check if shop type is allowed
	allowedTypes := subscription.SubscriptionTier.AllowedShopTypes
	for _, allowedType := range allowedTypes {
		if allowedType == shopType {
			return &LimitCheckResult{
				Allowed: true,
				Message: fmt.Sprintf("Shop type '%s' is allowed", shopType),
			}, nil
		}
	}

	return &LimitCheckResult{
		Allowed: false,
		Message: fmt.Sprintf("Shop type '%s' is not allowed in your subscription tier. Allowed types: %v",
			shopType, allowedTypes),
	}, nil
}

// CheckCreditBalance checks if user has enough credits
func (sg *SubscriptionGuard) CheckCreditBalance(userID uuid.UUID, requiredCredits int) (*LimitCheckResult, error) {
	subscription, err := sg.GetUserActiveSubscription(userID, "personal")
	if err != nil {
		return &LimitCheckResult{
			Allowed: false,
			Message: "No active subscription found",
		}, err
	}

	allowed := subscription.CreditBalance >= requiredCredits
	message := fmt.Sprintf("Available credits: %d, Required: %d", subscription.CreditBalance, requiredCredits)
	if !allowed {
		message = fmt.Sprintf("Insufficient credits. Available: %d, Required: %d",
			subscription.CreditBalance, requiredCredits)
	}

	return &LimitCheckResult{
		Allowed:      allowed,
		CurrentUsage: subscription.CreditBalance,
		Message:      message,
	}, nil
}

// GetAnalyticsHistoryLimit returns the analytics history limit in days
func (sg *SubscriptionGuard) GetAnalyticsHistoryLimit(userID uuid.UUID) (int, error) {
	subscription, err := sg.GetUserActiveSubscription(userID, "personal")
	if err != nil {
		return 0, err
	}

	return subscription.SubscriptionTier.AnalyticsHistoryDays, nil
}

// GetSupportLevel returns the support level for the user
func (sg *SubscriptionGuard) GetSupportLevel(userID uuid.UUID) (string, error) {
	subscription, err := sg.GetUserActiveSubscription(userID, "personal")
	if err != nil {
		return "none", err
	}

	return subscription.SubscriptionTier.SupportLevel, nil
}
