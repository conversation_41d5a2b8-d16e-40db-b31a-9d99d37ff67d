package main

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"os"

	"github.com/GoogleCloudPlatform/functions-framework-go/functions"
	"github.com/adc-credit/backend/internal/database"
	"github.com/adc-credit/backend/internal/services"
	"github.com/joho/godotenv"
)

func init() {
	// Register the function
	functions.HTTP("ProcessScheduledCredits", processScheduledCredits)

	// Load environment variables
	if err := godotenv.Load(); err != nil {
		log.Println("No .env file found, using environment variables")
	}

	// Initialize database connection
	if err := database.InitDB(); err != nil {
		log.Fatalf("Failed to connect to database: %v", err)
	}
}

// processScheduledCredits is the entry point for the Cloud Function
func processScheduledCredits(w http.ResponseWriter, r *http.Request) {
	// Set CORS headers for the preflight request
	if r.Method == http.MethodOptions {
		w.Header().Set("Access-Control-Allow-Origin", "*")
		w.Header().Set("Access-Control-Allow-Methods", "POST")
		w.<PERSON><PERSON>().Set("Access-Control-Allow-Headers", "Content-Type,Authorization")
		w.Header().Set("Access-Control-Max-Age", "3600")
		w.WriteHeader(http.StatusNoContent)
		return
	}

	// Set CORS headers for the main request
	w.Header().Set("Access-Control-Allow-Origin", "*")
	w.Header().Set("Content-Type", "application/json")

	// Only allow POST requests
	if r.Method != http.MethodPost {
		http.Error(w, "Only POST method is allowed", http.StatusMethodNotAllowed)
		return
	}

	// Check for API key authentication
	apiKey := r.Header.Get("X-API-Key")
	if apiKey == "" {
		http.Error(w, "API key required", http.StatusUnauthorized)
		return
	}

	// Verify API key against environment variable
	expectedAPIKey := os.Getenv("SCHEDULER_API_KEY")
	if expectedAPIKey == "" {
		log.Println("Warning: SCHEDULER_API_KEY environment variable not set")
		http.Error(w, "Server configuration error", http.StatusInternalServerError)
		return
	}

	if apiKey != expectedAPIKey {
		http.Error(w, "Invalid API key", http.StatusUnauthorized)
		return
	}

	// Process scheduled credits
	scheduledCreditService := services.NewScheduledCreditService()
	processedCount, err := scheduledCreditService.ProcessScheduledCredits()
	if err != nil {
		log.Printf("Error processing scheduled credits: %v", err)
		http.Error(w, fmt.Sprintf("Error processing scheduled credits: %v", err), http.StatusInternalServerError)
		return
	}

	// Return success response
	response := map[string]interface{}{
		"message": "Scheduled credits processed successfully",
		"count":   processedCount,
	}

	// Write response
	w.WriteHeader(http.StatusOK)
	json.NewEncoder(w).Encode(response)
}

func main() {
	// This is used when running locally. When deployed to GCP,
	// the main function is ignored.
	port := os.Getenv("PORT")
	if port == "" {
		port = "8080"
	}

	log.Printf("Listening on port %s", port)
	if err := http.ListenAndServe(":"+port, nil); err != nil {
		log.Fatal(err)
	}
}
