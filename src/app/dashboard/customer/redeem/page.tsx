"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { useSession } from "next-auth/react";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useToast } from "@/components/ui/use-toast";
import { QrCode, Scan, CreditCard, Camera } from "phosphor-react";
import { CustomerBreadcrumbs } from "@/components/navigation/breadcrumbs";
import { CustomerPageHeader } from "@/components/layout/page-header-mobile";
import { useRedeemCreditCodeMutation } from "@/lib/api/apiSlice";
import Link from "next/link";
import { ArrowLeft } from "lucide-react";

export default function RedeemCodePage() {
  const { data: session } = useSession();
  const router = useRouter();
  const { toast } = useToast();
  const [code, setCode] = useState("");
  const [isScanning, setIsScanning] = useState(false);
  const [redeemCode, { isLoading }] = useRedeemCreditCodeMutation();
  const [redemptionResult, setRedemptionResult] = useState<{
    success: boolean;
    message: string;
    creditBalance?: number;
    shopName?: string;
  } | null>(null);

  const handleRedeemCode = async () => {
    if (!code.trim()) {
      toast({
        title: "Error",
        description: "Please enter a credit code",
        variant: "destructive",
      });
      return;
    }

    try {
      const result = await redeemCode({ code }).unwrap();
      setRedemptionResult({
        success: true,
        message: "Credit code redeemed successfully!",
        creditBalance: result.credit_balance,
        shopName: result.shop_name,
      });
      setCode("");
    } catch (error: any) {
      setRedemptionResult({
        success: false,
        message: error.data?.message || "Failed to redeem code. Please try again.",
      });
    }
  };

  const startScanner = () => {
    setIsScanning(true);
    // In a real implementation, this would initialize a QR code scanner
    // For now, we'll just simulate it with a timeout
    setTimeout(() => {
      // Simulate finding a code
      const simulatedCode = "COFFEE123";
      setCode(simulatedCode);
      setIsScanning(false);
      toast({
        title: "Code scanned",
        description: `Found code: ${simulatedCode}`,
      });
    }, 2000);
  };

  return (
    <div className="relative flex size-full min-h-screen flex-col bg-[#fbfaf9] justify-between group/design-root overflow-x-hidden">
      <div>
        <CustomerPageHeader
          title="Redeem Credit Code"
          backHref="/dashboard/customer"
        />

        {/* Breadcrumbs */}
        <CustomerBreadcrumbs
          segments={[
            { label: "Redeem", href: "/dashboard/customer/redeem", isCurrentPage: true }
          ]}
        />

        <div className="p-4">
          <Card>
            <CardHeader>
              <CardTitle>Redeem a Credit Code</CardTitle>
              <CardDescription>
                Enter a credit code or scan a QR code to add credits to your account
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="grid gap-2">
                  <Label htmlFor="code">Credit Code</Label>
                  <div className="flex gap-2">
                    <Input
                      id="code"
                      placeholder="Enter credit code"
                      value={code}
                      onChange={(e) => setCode(e.target.value)}
                    />
                    <Button
                      variant="outline"
                      size="icon"
                      onClick={() => router.push("/dashboard/customer/scan")}
                    >
                      <Camera size={20} weight="regular" />
                    </Button>
                  </div>
                </div>

                {isScanning && (
                  <div className="p-4 border rounded-md bg-muted text-center">
                    <QrCode size={64} className="mx-auto mb-2" />
                    <p>Scanning QR code...</p>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setIsScanning(false)}
                      className="mt-2"
                    >
                      Cancel
                    </Button>
                  </div>
                )}

                {redemptionResult && (
                  <div
                    className={`p-4 border rounded-md ${
                      redemptionResult.success ? "bg-green-50 border-green-200" : "bg-red-50 border-red-200"
                    }`}
                  >
                    <p
                      className={`font-medium ${
                        redemptionResult.success ? "text-green-700" : "text-red-700"
                      }`}
                    >
                      {redemptionResult.success ? "Success!" : "Error"}
                    </p>
                    <p className="mt-1">{redemptionResult.message}</p>
                    {redemptionResult.success && redemptionResult.creditBalance !== undefined && (
                      <div className="mt-2">
                        <p>
                          New balance at {redemptionResult.shopName}: {redemptionResult.creditBalance} credits
                        </p>
                      </div>
                    )}
                  </div>
                )}
              </div>
            </CardContent>
            <CardFooter>
              <Button
                className="w-full"
                onClick={handleRedeemCode}
                disabled={isLoading || !code.trim() || isScanning}
              >
                {isLoading ? "Redeeming..." : "Redeem Code"}
              </Button>
            </CardFooter>
          </Card>
        </div>
      </div>

      <div>
        <div className="flex gap-2 border-t border-[#f1edea] bg-[#fbfaf9] px-4 pb-3 pt-2">
          <Link className="just flex flex-1 flex-col items-center justify-end gap-1 text-[#8a745c]" href="/dashboard/customer">
            <div className="text-[#8a745c] flex h-8 items-center justify-center">
              <ArrowLeft size={24} weight="regular" />
            </div>
            <p className="text-[#8a745c] text-xs font-medium leading-normal tracking-[0.015em]">Back</p>
          </Link>
          <div className="just flex flex-1 flex-col items-center justify-end gap-1 rounded-full text-[#181510]">
            <div className="text-[#181510] flex h-8 items-center justify-center">
              <CreditCard size={24} weight="fill" />
            </div>
            <p className="text-[#181510] text-xs font-medium leading-normal tracking-[0.015em]">Redeem</p>
          </div>
        </div>
        <div className="h-5 bg-[#fbfaf9]"></div>
      </div>
    </div>
  );
}
