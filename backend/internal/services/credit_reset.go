package services

import (
	"fmt"
	"log"
	"time"

	"github.com/adc-credit/backend/internal/database"
	"github.com/adc-credit/backend/internal/models"
	"github.com/google/uuid"
	"gorm.io/gorm"
)

// CreditResetService handles the monthly credit reset for users
type CreditResetService struct {
	db             *gorm.DB
	webhookService *WebhookService
}

// NewCreditResetService creates a new credit reset service
func NewCreditResetService() *CreditResetService {
	return &CreditResetService{
		db:             database.DB,
		webhookService: NewWebhookService(),
	}
}

// ProcessCreditResets processes all users due for credit reset
func (s *CreditResetService) ProcessCreditResets() (int, error) {
	// Find all users due for credit reset
	var users []models.User
	now := time.Now()
	if err := s.db.Where("next_credit_reset_date <= ? AND monthly_credits > 0", now).Find(&users).Error; err != nil {
		return 0, fmt.Errorf("failed to fetch users for credit reset: %w", err)
	}

	log.Printf("Found %d users due for credit reset", len(users))

	// Process each user
	resetCount := 0
	for _, user := range users {
		if err := s.resetUserCredits(user); err != nil {
			log.Printf("Error resetting credits for user %s: %v", user.ID, err)
			continue
		}
		resetCount++
	}

	return resetCount, nil
}

// resetUserCredits resets the credits for a specific user
func (s *CreditResetService) resetUserCredits(user models.User) error {
	// Start transaction
	tx := s.db.Begin()

	// Get user's subscription
	var subscription models.Subscription
	if err := tx.Where("user_id = ? AND status = ?", user.ID, "active").First(&subscription).Error; err != nil {
		tx.Rollback()
		return err
	}

	// Reset credit balance to monthly allocation
	oldBalance := subscription.CreditBalance
	subscription.CreditBalance = user.MonthlyCredits

	if err := tx.Save(&subscription).Error; err != nil {
		tx.Rollback()
		return err
	}

	// Create transaction record
	transaction := models.Transaction{
		ID:             uuid.New(),
		UserID:         user.ID,
		SubscriptionID: subscription.ID,
		OrganizationID: user.OrganizationID,
		BranchID:       user.BranchID,
		Type:           "credit_reset",
		Amount:         subscription.CreditBalance - oldBalance,
		Description:    "Monthly credit reset",
	}

	if err := tx.Create(&transaction).Error; err != nil {
		tx.Rollback()
		return err
	}

	// Update next reset date
	nextResetDate := time.Now().AddDate(0, 1, 0) // Add 1 month
	user.NextCreditResetDate = &nextResetDate
	if err := tx.Save(&user).Error; err != nil {
		tx.Rollback()
		return err
	}

	// Commit transaction
	if err := tx.Commit().Error; err != nil {
		return err
	}

	// Send webhook notification
	go func() {
		webhookData := map[string]interface{}{
			"user_id":         user.ID.String(),
			"old_balance":     oldBalance,
			"new_balance":     subscription.CreditBalance,
			"transaction_id":  transaction.ID.String(),
			"subscription_id": subscription.ID.String(),
		}

		// Add organization and branch data if available
		if user.OrganizationID != nil {
			webhookData["organization_id"] = user.OrganizationID.String()
		}
		if user.BranchID != nil {
			webhookData["branch_id"] = user.BranchID.String()
		}

		s.webhookService.SendWebhook(user.ID, "credit.reset", webhookData)
	}()

	return nil
}

// GetNextResetDate returns the next reset date for a user
func (s *CreditResetService) GetNextResetDate(userID uuid.UUID) (*time.Time, error) {
	var user models.User
	if err := s.db.Where("id = ?", userID).First(&user).Error; err != nil {
		return nil, err
	}

	return user.NextCreditResetDate, nil
}

// SetMonthlyCredits sets the monthly credit allocation for a user
func (s *CreditResetService) SetMonthlyCredits(userID uuid.UUID, credits int) error {
	var user models.User
	if err := s.db.Where("id = ?", userID).First(&user).Error; err != nil {
		return err
	}

	user.MonthlyCredits = credits

	// If next reset date is not set, set it to one month from now
	if user.NextCreditResetDate == nil {
		nextResetDate := time.Now().AddDate(0, 1, 0)
		user.NextCreditResetDate = &nextResetDate
	}

	return s.db.Save(&user).Error
}
