"use client";

import { useState } from "react";
import Image from "next/image";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>er, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { useToast } from "@/components/ui/use-toast";
import { Download, Copy, Share2 } from "lucide-react";

interface QRCodeDisplayProps {
  code: string;
  amount: number;
  qrCodeUrl: string;
  description?: string;
  expiresAt?: string;
}

export function QRCodeDisplay({ code, amount, qrCodeUrl, description, expiresAt }: QRCodeDisplayProps) {
  const { toast } = useToast();
  const [isDownloading, setIsDownloading] = useState(false);

  const copyToClipboard = () => {
    navigator.clipboard.writeText(code);
    toast({
      title: "Copied to clipboard",
      description: "Credit code has been copied to clipboard",
    });
  };

  const downloadQRCode = () => {
    setIsDownloading(true);
    
    // Create a temporary link element
    const link = document.createElement("a");
    
    // If the QR code URL is a data URL (base64), we can download it directly
    if (qrCodeUrl.startsWith("data:")) {
      link.href = qrCodeUrl;
      link.download = `credit-code-${code}.png`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      setIsDownloading(false);
    } else {
      // If it's a regular URL, we need to fetch it first
      fetch(qrCodeUrl)
        .then(response => response.blob())
        .then(blob => {
          const url = window.URL.createObjectURL(blob);
          link.href = url;
          link.download = `credit-code-${code}.png`;
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
          window.URL.revokeObjectURL(url);
          setIsDownloading(false);
        })
        .catch(error => {
          console.error("Error downloading QR code:", error);
          toast({
            title: "Download failed",
            description: "Failed to download QR code image",
            variant: "destructive",
          });
          setIsDownloading(false);
        });
    }
  };

  const shareCode = async () => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: "Credit Code",
          text: `Here's your credit code: ${code} for ${amount} credits${description ? ` (${description})` : ""}`,
          url: window.location.href,
        });
        toast({
          title: "Shared successfully",
          description: "Credit code has been shared",
        });
      } catch (error) {
        console.error("Error sharing:", error);
      }
    } else {
      copyToClipboard();
    }
  };

  return (
    <Card className="w-full max-w-md mx-auto">
      <CardHeader>
        <CardTitle className="text-center">Credit Code: {amount} Credits</CardTitle>
      </CardHeader>
      <CardContent className="flex flex-col items-center">
        <div className="relative w-64 h-64 mb-4">
          <Image
            src={qrCodeUrl}
            alt={`QR Code for ${code}`}
            fill
            className="object-contain"
          />
        </div>
        
        <div className="w-full p-3 bg-muted rounded-md text-center mb-4">
          <p className="text-lg font-mono font-bold break-all">{code}</p>
        </div>
        
        {description && (
          <p className="text-muted-foreground text-center mb-2">{description}</p>
        )}
        
        {expiresAt && (
          <p className="text-sm text-muted-foreground text-center">
            Expires: {new Date(expiresAt).toLocaleDateString()}
          </p>
        )}
      </CardContent>
      <CardFooter className="flex justify-between">
        <Button variant="outline" size="sm" onClick={copyToClipboard}>
          <Copy className="h-4 w-4 mr-2" />
          Copy Code
        </Button>
        <Button variant="outline" size="sm" onClick={downloadQRCode} disabled={isDownloading}>
          <Download className="h-4 w-4 mr-2" />
          {isDownloading ? "Downloading..." : "Download QR"}
        </Button>
        <Button variant="outline" size="sm" onClick={shareCode}>
          <Share2 className="h-4 w-4 mr-2" />
          Share
        </Button>
      </CardFooter>
    </Card>
  );
}
