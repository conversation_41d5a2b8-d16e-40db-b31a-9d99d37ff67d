"use client";

import { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, Ta<PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { toast } from "sonner";
import { Skeleton } from "@/components/ui/skeleton";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  useGetAnalyticsSummaryQuery,
  useGetAnalyticsTrendsQuery,
  useGetEndpointAnalyticsQuery,
  useGetPerformanceMetricsQuery
} from "@/lib/api/apiSlice";
import {
  LineChart,
  Line,
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  AreaChart,
  Area
} from "recharts";

// Color themes for charts
const colorThemes = {
  default: {
    primary: "#8884d8",
    secondary: "#82ca9d",
    tertiary: "#ffc658",
    quaternary: "#ff8042",
    background: "rgba(136, 132, 216, 0.1)",
    grid: "rgba(0, 0, 0, 0.1)",
  },
  blue: {
    primary: "#2563eb",
    secondary: "#60a5fa",
    tertiary: "#93c5fd",
    quaternary: "#3b82f6",
    background: "rgba(37, 99, 235, 0.1)",
    grid: "rgba(0, 0, 0, 0.1)",
  },
  green: {
    primary: "#16a34a",
    secondary: "#4ade80",
    tertiary: "#86efac",
    quaternary: "#22c55e",
    background: "rgba(22, 163, 74, 0.1)",
    grid: "rgba(0, 0, 0, 0.1)",
  },
  purple: {
    primary: "#9333ea",
    secondary: "#c084fc",
    tertiary: "#d8b4fe",
    quaternary: "#a855f7",
    background: "rgba(147, 51, 234, 0.1)",
    grid: "rgba(0, 0, 0, 0.1)",
  },
  orange: {
    primary: "#ea580c",
    secondary: "#fb923c",
    tertiary: "#fdba74",
    quaternary: "#f97316",
    background: "rgba(234, 88, 12, 0.1)",
    grid: "rgba(0, 0, 0, 0.1)",
  }
};

// Chart types
const chartTypes = {
  area: "Area",
  line: "Line",
  bar: "Bar",
};

// Helper function to get a human-readable label for performance metrics
const getMetricLabel = (metric: string): string => {
  switch (metric) {
    case "response_time":
      return "Response Time";
    case "error_rate":
      return "Error Rate";
    case "p95":
      return "P95 Response Time";
    case "p99":
      return "P99 Response Time";
    default:
      return metric;
  }
};

// Filter options
const filterOptions = {
  endpoints: [
    { value: "all", label: "All Endpoints" },
    { value: "top5", label: "Top 5 by Usage" },
    { value: "top5error", label: "Top 5 by Error Rate" },
    { value: "top5latency", label: "Top 5 by Latency" },
  ],
  metrics: [
    { value: "requests", label: "Requests" },
    { value: "credits", label: "Credits" },
    { value: "latency", label: "Latency" },
    { value: "errors", label: "Errors" },
  ],
};

// Data export options
const exportOptions = [
  { value: "csv", label: "Export as CSV" },
  { value: "json", label: "Export as JSON" },
  { value: "png", label: "Export as PNG" },
];

export default function AnalyticsPage() {
  const [timeRange, setTimeRange] = useState<string>("30d");
  const [performanceMetric, setPerformanceMetric] = useState<string>("response_time");
  const [colorTheme, setColorTheme] = useState<keyof typeof colorThemes>("default");
  const [trendsChartType, setTrendsChartType] = useState<keyof typeof chartTypes>("area");
  const [endpointsChartType, setEndpointsChartType] = useState<keyof typeof chartTypes>("bar");
  const [performanceChartType, setPerformanceChartType] = useState<keyof typeof chartTypes>("line");
  const [endpointFilter, setEndpointFilter] = useState<string>("all");
  const [metricFilter, setMetricFilter] = useState<string>("requests");
  const [showComparison, setShowComparison] = useState<boolean>(false);
  const [previousPeriod, setPreviousPeriod] = useState<boolean>(false);

  // Get current theme colors
  const theme = colorThemes[colorTheme];

  // Calculate date range based on selected time range
  const endDate = new Date();
  const startDate = new Date();

  switch (timeRange) {
    case "7d":
      startDate.setDate(endDate.getDate() - 7);
      break;
    case "30d":
      startDate.setDate(endDate.getDate() - 30);
      break;
    case "90d":
      startDate.setDate(endDate.getDate() - 90);
      break;
  }

  const dateParams = {
    start_date: startDate.toISOString().split('T')[0],
    end_date: endDate.toISOString().split('T')[0],
  };

  // Use RTK Query hooks to fetch analytics data
  const {
    data: summary,
    isLoading: summaryLoading,
    error: summaryError
  } = useGetAnalyticsSummaryQuery(dateParams);

  const {
    data: trends = [],
    isLoading: trendsLoading
  } = useGetAnalyticsTrendsQuery({
    ...dateParams,
    interval: "day"
  });

  const {
    data: endpoints = [],
    isLoading: endpointsLoading
  } = useGetEndpointAnalyticsQuery(dateParams);

  const {
    data: performance,
    isLoading: performanceLoading
  } = useGetPerformanceMetricsQuery({
    ...dateParams,
    metric: performanceMetric
  });

  // Show error toast if analytics fetch fails
  useEffect(() => {
    if (summaryError) {
      toast.error("Failed to load analytics data. Your subscription may not include advanced analytics.");
      console.error("Error fetching analytics data:", summaryError);
    }
  }, [summaryError]);

  const handleTimeRangeChange = (range: string) => {
    setTimeRange(range);
  };

  const handlePerformanceMetricChange = (metric: string) => {
    setPerformanceMetric(metric);
  };

  // Show loading state when fetching initial data
  const isLoading = summaryLoading && trendsLoading && endpointsLoading && performanceLoading;

  if (isLoading) {
    return (
      <div className="flex h-[calc(100vh-4rem)] items-center justify-center">
        <div className="h-8 w-8 animate-spin rounded-full border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className="container py-10">
      <div className="flex flex-col gap-4 mb-8">
        <div className="flex justify-between items-center">
          <h1 className="text-3xl font-bold">Advanced Analytics</h1>
          <div className="flex space-x-2">
            <Button
              variant={timeRange === "7d" ? "default" : "outline"}
              size="sm"
              onClick={() => handleTimeRangeChange("7d")}
            >
              7 Days
            </Button>
            <Button
              variant={timeRange === "30d" ? "default" : "outline"}
              size="sm"
              onClick={() => handleTimeRangeChange("30d")}
            >
              30 Days
            </Button>
            <Button
              variant={timeRange === "90d" ? "default" : "outline"}
              size="sm"
              onClick={() => handleTimeRangeChange("90d")}
            >
              90 Days
            </Button>
          </div>
        </div>

        <div className="flex flex-col gap-3 bg-muted/30 p-3 rounded-lg">
          <div className="flex flex-wrap gap-4 items-center justify-between">
            <div className="flex items-center gap-2">
              <span className="text-sm font-medium">Color Theme:</span>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline" size="sm" className="h-8 gap-1 pl-3">
                    <div
                      className="w-4 h-4 rounded-full"
                      style={{
                        backgroundColor: theme.primary,
                        border: '1px solid rgba(0,0,0,0.1)'
                      }}
                    />
                    <span className="capitalize">{colorTheme}</span>
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="start" className="w-40">
                  {Object.keys(colorThemes).map((themeName) => (
                    <DropdownMenuItem
                      key={themeName}
                      className="flex items-center gap-2 cursor-pointer"
                      onClick={() => setColorTheme(themeName as keyof typeof colorThemes)}
                    >
                      <div
                        className="w-4 h-4 rounded-full"
                        style={{
                          backgroundColor: colorThemes[themeName as keyof typeof colorThemes].primary,
                          border: '1px solid rgba(0,0,0,0.1)'
                        }}
                      />
                      <span className="capitalize">{themeName}</span>
                      {colorTheme === themeName && (
                        <svg className="h-4 w-4 ml-auto" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                        </svg>
                      )}
                    </DropdownMenuItem>
                  ))}
                </DropdownMenuContent>
              </DropdownMenu>
            </div>

            <div className="flex items-center gap-2">
              <span className="text-sm font-medium">Chart Types:</span>
              <div className="flex gap-2">
                <div className="flex items-center gap-1">
                  <span className="text-xs font-medium">Trends:</span>
                  <Select
                    value={trendsChartType}
                    onValueChange={(value) => setTrendsChartType(value as keyof typeof chartTypes)}
                  >
                    <SelectTrigger className="h-7 w-24">
                      <SelectValue placeholder="Chart Type" />
                    </SelectTrigger>
                    <SelectContent>
                      {Object.entries(chartTypes).map(([key, label]) => (
                        <SelectItem key={key} value={key}>{label}</SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="flex items-center gap-1">
                  <span className="text-xs font-medium">Endpoints:</span>
                  <Select
                    value={endpointsChartType}
                    onValueChange={(value) => setEndpointsChartType(value as keyof typeof chartTypes)}
                  >
                    <SelectTrigger className="h-7 w-24">
                      <SelectValue placeholder="Chart Type" />
                    </SelectTrigger>
                    <SelectContent>
                      {Object.entries(chartTypes).map(([key, label]) => (
                        <SelectItem key={key} value={key}>{label}</SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="flex items-center gap-1">
                  <span className="text-xs font-medium">Performance:</span>
                  <Select
                    value={performanceChartType}
                    onValueChange={(value) => setPerformanceChartType(value as keyof typeof chartTypes)}
                  >
                    <SelectTrigger className="h-7 w-24">
                      <SelectValue placeholder="Chart Type" />
                    </SelectTrigger>
                    <SelectContent>
                      {Object.entries(chartTypes).map(([key, label]) => (
                        <SelectItem key={key} value={key}>{label}</SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </div>

            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                className="gap-1"
                onClick={() => setShowComparison(!showComparison)}
              >
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-bar-chart-2">
                  <line x1="18" x2="18" y1="20" y2="10"></line>
                  <line x1="12" x2="12" y1="20" y2="4"></line>
                  <line x1="6" x2="6" y1="20" y2="14"></line>
                </svg>
                Compare
              </Button>

              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline" size="sm" className="gap-1">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-download">
                      <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                      <polyline points="7 10 12 15 17 10"></polyline>
                      <line x1="12" x2="12" y1="15" y2="3"></line>
                    </svg>
                    Export
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  {exportOptions.map((option) => (
                    <DropdownMenuItem
                      key={option.value}
                      onClick={() => toast.info(`Exporting as ${option.label.split(' ')[2]}...`)}
                    >
                      {option.label}
                    </DropdownMenuItem>
                  ))}
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>

          <div className="flex flex-wrap gap-3 pt-2 border-t border-border/40">
            <div className="flex items-center gap-2">
              <span className="text-sm font-medium">Filters:</span>

              <Select
                value={endpointFilter}
                onValueChange={setEndpointFilter}
              >
                <SelectTrigger className="h-8 w-40">
                  <SelectValue placeholder="Filter Endpoints" />
                </SelectTrigger>
                <SelectContent>
                  {filterOptions.endpoints.map((option) => (
                    <SelectItem key={option.value} value={option.value}>{option.label}</SelectItem>
                  ))}
                </SelectContent>
              </Select>

              <Select
                value={metricFilter}
                onValueChange={setMetricFilter}
              >
                <SelectTrigger className="h-8 w-32">
                  <SelectValue placeholder="Metric" />
                </SelectTrigger>
                <SelectContent>
                  {filterOptions.metrics.map((option) => (
                    <SelectItem key={option.value} value={option.value}>{option.label}</SelectItem>
                  ))}
                </SelectContent>
              </Select>

              <Button
                variant="outline"
                size="sm"
                className={previousPeriod ? "bg-primary/10" : ""}
                onClick={() => setPreviousPeriod(!previousPeriod)}
              >
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="mr-1">
                  <rect width="18" height="18" x="3" y="3" rx="2" ry="2"></rect>
                  <line x1="9" x2="15" y1="9" y2="15"></line>
                  <line x1="15" x2="9" y1="9" y2="15"></line>
                </svg>
                Include Previous Period
              </Button>
            </div>
          </div>
        </div>
      </div>

      {summary ? (
        <>
          <div className="grid gap-6 md:grid-cols-4 mb-8">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">Total Requests</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-center justify-between">
                  <div className="text-2xl font-bold">{summary.total_requests.toLocaleString()}</div>
                  <div className="flex items-center text-xs font-medium text-green-600">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="mr-1">
                      <polyline points="22 7 13.5 15.5 8.5 10.5 2 17"></polyline>
                      <polyline points="16 7 22 7 22 13"></polyline>
                    </svg>
                    12.5%
                  </div>
                </div>
                <div className="mt-3 h-1 w-full bg-muted overflow-hidden rounded-full">
                  <div
                    className="h-full bg-green-500 rounded-full"
                    style={{ width: `${Math.min(summary.total_requests / 10000 * 100, 100)}%` }}
                  ></div>
                </div>
                <p className="text-xs text-muted-foreground mt-2 flex justify-between">
                  <span>{summary.start_date} to {summary.end_date}</span>
                  <span className="font-medium">{Math.round(summary.total_requests / 10000 * 100)}% of limit</span>
                </p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">Total Credits</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-center justify-between">
                  <div className="text-2xl font-bold">{summary.total_credits.toLocaleString()}</div>
                  <div className="flex items-center text-xs font-medium text-amber-600">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="mr-1">
                      <polyline points="22 17 13.5 8.5 8.5 13.5 2 7"></polyline>
                      <polyline points="16 17 22 17 22 11"></polyline>
                    </svg>
                    8.3%
                  </div>
                </div>
                <div className="mt-3 h-1 w-full bg-muted overflow-hidden rounded-full">
                  <div
                    className="h-full bg-amber-500 rounded-full"
                    style={{ width: `${Math.min(summary.total_credits / 5000 * 100, 100)}%` }}
                  ></div>
                </div>
                <p className="text-xs text-muted-foreground mt-2 flex justify-between">
                  <span>{summary.subscription_tier} tier</span>
                  <span className="font-medium">{Math.round(summary.total_credits / 5000 * 100)}% of allocation</span>
                </p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">Avg Response Time</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-center justify-between">
                  <div className="text-2xl font-bold">{summary.avg_response_time}ms</div>
                  <div className="flex items-center text-xs font-medium text-green-600">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="mr-1">
                      <polyline points="22 17 13.5 8.5 8.5 13.5 2 7"></polyline>
                      <polyline points="16 17 22 17 22 11"></polyline>
                    </svg>
                    5.2%
                  </div>
                </div>
                <div className="mt-3 flex items-center gap-1">
                  <div className="h-1.5 w-full bg-muted overflow-hidden rounded-full">
                    <div
                      className="h-full bg-blue-500 rounded-full"
                      style={{ width: `${Math.min(summary.avg_response_time / 500 * 100, 100)}%` }}
                    ></div>
                  </div>
                  <span className="text-xs font-medium">P95</span>
                  <div className="h-1.5 w-full bg-muted overflow-hidden rounded-full">
                    <div
                      className="h-full bg-indigo-500 rounded-full"
                      style={{ width: `${Math.min(summary.p95_response_time / 500 * 100, 100)}%` }}
                    ></div>
                  </div>
                  <span className="text-xs font-medium">P99</span>
                  <div className="h-1.5 w-full bg-muted overflow-hidden rounded-full">
                    <div
                      className="h-full bg-purple-500 rounded-full"
                      style={{ width: `${Math.min(summary.p99_response_time / 500 * 100, 100)}%` }}
                    ></div>
                  </div>
                </div>
                <p className="text-xs text-muted-foreground mt-2">
                  P95: {summary.p95_response_time}ms / P99: {summary.p99_response_time}ms
                </p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">Error Rate</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-center justify-between">
                  <div className="text-2xl font-bold">{summary.error_rate.toFixed(2)}%</div>
                  <div className={`flex items-center text-xs font-medium ${summary.error_rate < 1 ? "text-green-600" : summary.error_rate < 5 ? "text-amber-600" : "text-red-600"}`}>
                    {summary.error_rate < 1 ? (
                      <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="mr-1">
                        <polyline points="22 17 13.5 8.5 8.5 13.5 2 7"></polyline>
                        <polyline points="16 17 22 17 22 11"></polyline>
                      </svg>
                    ) : (
                      <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="mr-1">
                        <polyline points="22 7 13.5 15.5 8.5 10.5 2 17"></polyline>
                        <polyline points="16 7 22 7 22 13"></polyline>
                      </svg>
                    )}
                    0.3%
                  </div>
                </div>
                <div className="mt-3 h-2 w-full bg-muted overflow-hidden rounded-full">
                  <div
                    className={`h-full rounded-full ${summary.error_rate < 1 ? "bg-green-500" : summary.error_rate < 5 ? "bg-amber-500" : "bg-red-500"}`}
                    style={{ width: `${Math.min(summary.error_rate * 10, 100)}%` }}
                  ></div>
                </div>
                <p className="text-xs text-muted-foreground mt-2 flex justify-between">
                  <span className={`font-medium ${summary.error_rate < 1 ? "text-green-600" : summary.error_rate < 5 ? "text-amber-600" : "text-red-600"}`}>
                    {summary.error_rate < 1 ? "Excellent" : summary.error_rate < 5 ? "Good" : "Needs attention"}
                  </span>
                  <span>Target: &lt;1.0%</span>
                </p>
              </CardContent>
            </Card>
          </div>

          <Tabs defaultValue="trends" className="w-full">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="trends">Usage Trends</TabsTrigger>
              <TabsTrigger value="endpoints">Endpoint Analysis</TabsTrigger>
              <TabsTrigger value="performance">Performance Metrics</TabsTrigger>
            </TabsList>

            <TabsContent value="trends" className="mt-6">
              <Card>
                <CardHeader>
                  <CardTitle>Usage Trends</CardTitle>
                  <CardDescription>
                    API usage trends over time
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  {trendsLoading ? (
                    <div className="h-80 flex items-center justify-center">
                      <div className="h-8 w-8 animate-spin rounded-full border-b-2 border-primary"></div>
                    </div>
                  ) : trends.length > 0 ? (
                    <div className="h-80">
                      <ResponsiveContainer width="100%" height="100%">
                        {trendsChartType === 'area' ? (
                          <AreaChart
                            data={trends}
                            margin={{
                              top: 10,
                              right: 30,
                              left: 0,
                              bottom: 0,
                            }}
                          >
                            <CartesianGrid strokeDasharray="3 3" opacity={0.1} />
                            <XAxis
                              dataKey="period"
                              tick={{ fontSize: 12 }}
                              tickFormatter={(value) => {
                                // Format the date to be more readable
                                const date = new Date(value);
                                return date.toLocaleDateString(undefined, { month: 'short', day: 'numeric' });
                              }}
                            />
                            <YAxis tick={{ fontSize: 12 }} />
                            <Tooltip
                              formatter={(value: number | string, name) => {
                                if (name === "total_requests") return [`${Number(value).toLocaleString()} requests`, "Requests"];
                                if (name === "total_credits") return [`${Number(value).toLocaleString()} credits`, "Credits"];
                                if (name === "avg_response_time") return [`${value} ms`, "Avg Response Time"];
                                return [value, name];
                              }}
                              labelFormatter={(label) => {
                                const date = new Date(label);
                                return date.toLocaleDateString(undefined, { year: 'numeric', month: 'long', day: 'numeric' });
                              }}
                              contentStyle={{ backgroundColor: 'rgba(255, 255, 255, 0.9)', borderRadius: '6px', border: '1px solid #eee' }}
                            />
                            <Legend />
                            <Area
                              type="monotone"
                              dataKey="total_requests"
                              name="Requests"
                              stroke={theme.primary}
                              fill={theme.primary}
                              fillOpacity={0.3}
                            />
                            <Area
                              type="monotone"
                              dataKey="total_credits"
                              name="Credits"
                              stroke={theme.secondary}
                              fill={theme.secondary}
                              fillOpacity={0.3}
                            />
                          </AreaChart>
                        ) : trendsChartType === 'line' ? (
                          <LineChart
                            data={trends}
                            margin={{
                              top: 10,
                              right: 30,
                              left: 0,
                              bottom: 0,
                            }}
                          >
                            <CartesianGrid strokeDasharray="3 3" opacity={0.1} />
                            <XAxis
                              dataKey="period"
                              tick={{ fontSize: 12 }}
                              tickFormatter={(value) => {
                                const date = new Date(value);
                                return date.toLocaleDateString(undefined, { month: 'short', day: 'numeric' });
                              }}
                            />
                            <YAxis tick={{ fontSize: 12 }} />
                            <Tooltip
                              formatter={(value: number | string, name) => {
                                if (name === "total_requests") return [`${Number(value).toLocaleString()} requests`, "Requests"];
                                if (name === "total_credits") return [`${Number(value).toLocaleString()} credits`, "Credits"];
                                if (name === "avg_response_time") return [`${value} ms`, "Avg Response Time"];
                                return [value, name];
                              }}
                              labelFormatter={(label) => {
                                const date = new Date(label);
                                return date.toLocaleDateString(undefined, { year: 'numeric', month: 'long', day: 'numeric' });
                              }}
                              contentStyle={{ backgroundColor: 'rgba(255, 255, 255, 0.9)', borderRadius: '6px', border: '1px solid #eee' }}
                            />
                            <Legend />
                            <Line
                              type="monotone"
                              dataKey="total_requests"
                              name="Requests"
                              stroke={theme.primary}
                              strokeWidth={2}
                              dot={{ r: 3, fill: theme.primary }}
                              activeDot={{ r: 5 }}
                            />
                            <Line
                              type="monotone"
                              dataKey="total_credits"
                              name="Credits"
                              stroke={theme.secondary}
                              strokeWidth={2}
                              dot={{ r: 3, fill: theme.secondary }}
                              activeDot={{ r: 5 }}
                            />
                          </LineChart>
                        ) : (
                          <BarChart
                            data={trends}
                            margin={{
                              top: 10,
                              right: 30,
                              left: 0,
                              bottom: 0,
                            }}
                          >
                            <CartesianGrid strokeDasharray="3 3" opacity={0.1} />
                            <XAxis
                              dataKey="period"
                              tick={{ fontSize: 12 }}
                              tickFormatter={(value) => {
                                const date = new Date(value);
                                return date.toLocaleDateString(undefined, { month: 'short', day: 'numeric' });
                              }}
                            />
                            <YAxis tick={{ fontSize: 12 }} />
                            <Tooltip
                              formatter={(value: number | string, name) => {
                                if (name === "total_requests") return [`${Number(value).toLocaleString()} requests`, "Requests"];
                                if (name === "total_credits") return [`${Number(value).toLocaleString()} credits`, "Credits"];
                                if (name === "avg_response_time") return [`${value} ms`, "Avg Response Time"];
                                return [value, name];
                              }}
                              labelFormatter={(label) => {
                                const date = new Date(label);
                                return date.toLocaleDateString(undefined, { year: 'numeric', month: 'long', day: 'numeric' });
                              }}
                              contentStyle={{ backgroundColor: 'rgba(255, 255, 255, 0.9)', borderRadius: '6px', border: '1px solid #eee' }}
                            />
                            <Legend />
                            <Bar
                              dataKey="total_requests"
                              name="Requests"
                              fill={theme.primary}
                              radius={[4, 4, 0, 0]}
                            />
                            <Bar
                              dataKey="total_credits"
                              name="Credits"
                              fill={theme.secondary}
                              radius={[4, 4, 0, 0]}
                            />
                          </BarChart>
                        )}
                      </ResponsiveContainer>
                    </div>
                  ) : (
                    <div className="py-8 text-center text-muted-foreground">
                      No trend data available
                    </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="endpoints" className="mt-6">
              <Card>
                <CardHeader className="flex flex-row items-center justify-between">
                  <div>
                    <CardTitle>Endpoint Analysis</CardTitle>
                    <CardDescription>
                      Performance metrics by endpoint
                    </CardDescription>
                  </div>
                </CardHeader>
                <CardContent>
                  {endpointsLoading ? (
                    <div className="h-80 flex items-center justify-center">
                      <div className="h-8 w-8 animate-spin rounded-full border-b-2 border-primary"></div>
                    </div>
                  ) : endpoints.length > 0 ? (
                    <div>
                      <div className="mb-6 h-80">
                        <ResponsiveContainer width="100%" height="100%">
                          {endpointsChartType === 'bar' ? (
                            <BarChart
                              data={endpoints}
                              margin={{
                                top: 20,
                                right: 30,
                                left: 20,
                                bottom: 5,
                              }}
                            >
                              <CartesianGrid strokeDasharray="3 3" opacity={0.1} />
                              <XAxis
                                dataKey="endpoint"
                                tick={{ fontSize: 12 }}
                                interval={0}
                                tickFormatter={(value) => {
                                  // Truncate long endpoint names
                                  return value.length > 15 ? value.substring(0, 15) + '...' : value;
                                }}
                              />
                              <YAxis tick={{ fontSize: 12 }} />
                              <Tooltip
                                formatter={(value: number | string, name) => {
                                  if (name === "total_requests") return [`${Number(value).toLocaleString()} requests`, "Requests"];
                                  if (name === "total_credits") return [`${Number(value).toLocaleString()} credits`, "Credits"];
                                  if (name === "avg_response_time") return [`${value} ms`, "Avg Response Time"];
                                  if (name === "error_rate") return [`${typeof value === 'number' ? value.toFixed(2) : value}%`, "Error Rate"];
                                  return [value, name];
                                }}
                                contentStyle={{ backgroundColor: 'rgba(255, 255, 255, 0.9)', borderRadius: '6px', border: '1px solid #eee' }}
                              />
                              <Legend />
                              <Bar
                                dataKey="total_requests"
                                name="Requests"
                                fill={theme.primary}
                                radius={[4, 4, 0, 0]}
                              />
                              <Bar
                                dataKey="total_credits"
                                name="Credits"
                                fill={theme.secondary}
                                radius={[4, 4, 0, 0]}
                              />
                            </BarChart>
                          ) : endpointsChartType === 'line' ? (
                            <LineChart
                              data={endpoints}
                              margin={{
                                top: 20,
                                right: 30,
                                left: 20,
                                bottom: 5,
                              }}
                            >
                              <CartesianGrid strokeDasharray="3 3" opacity={0.1} />
                              <XAxis
                                dataKey="endpoint"
                                tick={{ fontSize: 12 }}
                                interval={0}
                                tickFormatter={(value) => {
                                  return value.length > 15 ? value.substring(0, 15) + '...' : value;
                                }}
                              />
                              <YAxis tick={{ fontSize: 12 }} />
                              <Tooltip
                                formatter={(value: number | string, name) => {
                                  if (name === "total_requests") return [`${Number(value).toLocaleString()} requests`, "Requests"];
                                  if (name === "total_credits") return [`${Number(value).toLocaleString()} credits`, "Credits"];
                                  if (name === "avg_response_time") return [`${value} ms`, "Avg Response Time"];
                                  if (name === "error_rate") return [`${typeof value === 'number' ? value.toFixed(2) : value}%`, "Error Rate"];
                                  return [value, name];
                                }}
                                contentStyle={{ backgroundColor: 'rgba(255, 255, 255, 0.9)', borderRadius: '6px', border: '1px solid #eee' }}
                              />
                              <Legend />
                              <Line
                                type="monotone"
                                dataKey="total_requests"
                                name="Requests"
                                stroke={theme.primary}
                                strokeWidth={2}
                                dot={{ r: 3, fill: theme.primary }}
                                activeDot={{ r: 5 }}
                              />
                              <Line
                                type="monotone"
                                dataKey="total_credits"
                                name="Credits"
                                stroke={theme.secondary}
                                strokeWidth={2}
                                dot={{ r: 3, fill: theme.secondary }}
                                activeDot={{ r: 5 }}
                              />
                            </LineChart>
                          ) : (
                            <AreaChart
                              data={endpoints}
                              margin={{
                                top: 20,
                                right: 30,
                                left: 20,
                                bottom: 5,
                              }}
                            >
                              <CartesianGrid strokeDasharray="3 3" opacity={0.1} />
                              <XAxis
                                dataKey="endpoint"
                                tick={{ fontSize: 12 }}
                                interval={0}
                                tickFormatter={(value) => {
                                  return value.length > 15 ? value.substring(0, 15) + '...' : value;
                                }}
                              />
                              <YAxis tick={{ fontSize: 12 }} />
                              <Tooltip
                                formatter={(value: number | string, name) => {
                                  if (name === "total_requests") return [`${Number(value).toLocaleString()} requests`, "Requests"];
                                  if (name === "total_credits") return [`${Number(value).toLocaleString()} credits`, "Credits"];
                                  if (name === "avg_response_time") return [`${value} ms`, "Avg Response Time"];
                                  if (name === "error_rate") return [`${typeof value === 'number' ? value.toFixed(2) : value}%`, "Error Rate"];
                                  return [value, name];
                                }}
                                contentStyle={{ backgroundColor: 'rgba(255, 255, 255, 0.9)', borderRadius: '6px', border: '1px solid #eee' }}
                              />
                              <Legend />
                              <Area
                                type="monotone"
                                dataKey="total_requests"
                                name="Requests"
                                stroke={theme.primary}
                                fill={theme.primary}
                                fillOpacity={0.3}
                              />
                              <Area
                                type="monotone"
                                dataKey="total_credits"
                                name="Credits"
                                stroke={theme.secondary}
                                fill={theme.secondary}
                                fillOpacity={0.3}
                              />
                            </AreaChart>
                          )}
                        </ResponsiveContainer>
                      </div>

                      <div className="rounded-md border">
                        <Table>
                          <TableHeader>
                            <TableRow>
                              <TableHead>Endpoint</TableHead>
                              <TableHead>Requests</TableHead>
                              <TableHead>Credits</TableHead>
                              <TableHead>Avg Response</TableHead>
                              <TableHead>Error Rate</TableHead>
                              <TableHead>P95</TableHead>
                            </TableRow>
                          </TableHeader>
                          <TableBody>
                            {endpoints.map((endpoint) => (
                              <TableRow key={endpoint.endpoint}>
                                <TableCell className="font-mono text-xs max-w-[200px] truncate">
                                  {endpoint.endpoint}
                                </TableCell>
                                <TableCell>{endpoint.total_requests.toLocaleString()}</TableCell>
                                <TableCell>{endpoint.total_credits.toLocaleString()}</TableCell>
                                <TableCell>{endpoint.avg_response_time}ms</TableCell>
                                <TableCell>{endpoint.error_rate.toFixed(2)}%</TableCell>
                                <TableCell>{endpoint.p95_response_time}ms</TableCell>
                              </TableRow>
                            ))}
                          </TableBody>
                        </Table>
                      </div>
                    </div>
                  ) : (
                    <div className="py-8 text-center text-muted-foreground">
                      No endpoint data available
                    </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="performance" className="mt-6">
              <Card>
                <CardHeader>
                  <CardTitle>Performance Metrics</CardTitle>
                  <CardDescription>
                    Performance metrics over time
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="mb-4 flex flex-wrap gap-2">
                    <Button
                      variant={performanceMetric === "response_time" ? "default" : "outline"}
                      size="sm"
                      onClick={() => handlePerformanceMetricChange("response_time")}
                    >
                      Response Time
                    </Button>
                    <Button
                      variant={performanceMetric === "error_rate" ? "default" : "outline"}
                      size="sm"
                      onClick={() => handlePerformanceMetricChange("error_rate")}
                    >
                      Error Rate
                    </Button>
                    <Button
                      variant={performanceMetric === "p95" ? "default" : "outline"}
                      size="sm"
                      onClick={() => handlePerformanceMetricChange("p95")}
                    >
                      P95 Response
                    </Button>
                    <Button
                      variant={performanceMetric === "p99" ? "default" : "outline"}
                      size="sm"
                      onClick={() => handlePerformanceMetricChange("p99")}
                    >
                      P99 Response
                    </Button>
                  </div>

                  {performanceLoading ? (
                    <div className="h-80 flex items-center justify-center">
                      <div className="h-8 w-8 animate-spin rounded-full border-b-2 border-primary"></div>
                    </div>
                  ) : performance && performance.data.length > 0 ? (
                    <div className="h-80">
                      <ResponsiveContainer width="100%" height="100%">
                        {performanceChartType === 'line' ? (
                          <LineChart
                            data={performance.data}
                            margin={{
                              top: 10,
                              right: 30,
                              left: 20,
                              bottom: 10,
                            }}
                          >
                            <CartesianGrid strokeDasharray="3 3" opacity={0.1} />
                            <XAxis
                              dataKey="date"
                              tick={{ fontSize: 12 }}
                              tickFormatter={(value) => {
                                // Format the date to be more readable
                                const date = new Date(value);
                                return date.toLocaleDateString(undefined, { month: 'short', day: 'numeric' });
                              }}
                            />
                            <YAxis
                              tick={{ fontSize: 12 }}
                              domain={['auto', 'auto']}
                              tickFormatter={(value) => {
                                if (performanceMetric === "error_rate") return `${value}%`;
                                return value;
                              }}
                            />
                            <Tooltip
                              formatter={(value: number | string, name) => {
                                if (name === "value") {
                                  if (performanceMetric === "response_time" || performanceMetric === "p95" || performanceMetric === "p99") {
                                    return [`${value} ms`, getMetricLabel(performanceMetric)];
                                  }
                                  if (performanceMetric === "error_rate") {
                                    return [`${value}%`, "Error Rate"];
                                  }
                                }
                                return [value, name];
                              }}
                              labelFormatter={(label) => {
                                const date = new Date(label);
                                return date.toLocaleDateString(undefined, { year: 'numeric', month: 'long', day: 'numeric' });
                              }}
                              contentStyle={{ backgroundColor: 'rgba(255, 255, 255, 0.9)', borderRadius: '6px', border: '1px solid #eee' }}
                            />
                            <Line
                              type="monotone"
                              dataKey="value"
                              name={getMetricLabel(performanceMetric)}
                              stroke={theme.primary}
                              strokeWidth={2}
                              dot={{ r: 3, fill: theme.primary }}
                              activeDot={{ r: 5 }}
                            />
                          </LineChart>
                        ) : performanceChartType === 'area' ? (
                          <AreaChart
                            data={performance.data}
                            margin={{
                              top: 10,
                              right: 30,
                              left: 20,
                              bottom: 10,
                            }}
                          >
                            <CartesianGrid strokeDasharray="3 3" opacity={0.1} />
                            <XAxis
                              dataKey="date"
                              tick={{ fontSize: 12 }}
                              tickFormatter={(value) => {
                                const date = new Date(value);
                                return date.toLocaleDateString(undefined, { month: 'short', day: 'numeric' });
                              }}
                            />
                            <YAxis
                              tick={{ fontSize: 12 }}
                              domain={['auto', 'auto']}
                              tickFormatter={(value) => {
                                if (performanceMetric === "error_rate") return `${value}%`;
                                return value;
                              }}
                            />
                            <Tooltip
                              formatter={(value: number | string, name) => {
                                if (name === "value") {
                                  if (performanceMetric === "response_time" || performanceMetric === "p95" || performanceMetric === "p99") {
                                    return [`${value} ms`, getMetricLabel(performanceMetric)];
                                  }
                                  if (performanceMetric === "error_rate") {
                                    return [`${value}%`, "Error Rate"];
                                  }
                                }
                                return [value, name];
                              }}
                              labelFormatter={(label) => {
                                const date = new Date(label);
                                return date.toLocaleDateString(undefined, { year: 'numeric', month: 'long', day: 'numeric' });
                              }}
                              contentStyle={{ backgroundColor: 'rgba(255, 255, 255, 0.9)', borderRadius: '6px', border: '1px solid #eee' }}
                            />
                            <Area
                              type="monotone"
                              dataKey="value"
                              name={getMetricLabel(performanceMetric)}
                              stroke={theme.primary}
                              fill={theme.primary}
                              fillOpacity={0.3}
                            />
                          </AreaChart>
                        ) : (
                          <BarChart
                            data={performance.data}
                            margin={{
                              top: 10,
                              right: 30,
                              left: 20,
                              bottom: 10,
                            }}
                          >
                            <CartesianGrid strokeDasharray="3 3" opacity={0.1} />
                            <XAxis
                              dataKey="date"
                              tick={{ fontSize: 12 }}
                              tickFormatter={(value) => {
                                const date = new Date(value);
                                return date.toLocaleDateString(undefined, { month: 'short', day: 'numeric' });
                              }}
                            />
                            <YAxis
                              tick={{ fontSize: 12 }}
                              domain={['auto', 'auto']}
                              tickFormatter={(value) => {
                                if (performanceMetric === "error_rate") return `${value}%`;
                                return value;
                              }}
                            />
                            <Tooltip
                              formatter={(value: number | string, name) => {
                                if (name === "value") {
                                  if (performanceMetric === "response_time" || performanceMetric === "p95" || performanceMetric === "p99") {
                                    return [`${value} ms`, getMetricLabel(performanceMetric)];
                                  }
                                  if (performanceMetric === "error_rate") {
                                    return [`${value}%`, "Error Rate"];
                                  }
                                }
                                return [value, name];
                              }}
                              labelFormatter={(label) => {
                                const date = new Date(label);
                                return date.toLocaleDateString(undefined, { year: 'numeric', month: 'long', day: 'numeric' });
                              }}
                              contentStyle={{ backgroundColor: 'rgba(255, 255, 255, 0.9)', borderRadius: '6px', border: '1px solid #eee' }}
                            />
                            <Bar
                              dataKey="value"
                              name={getMetricLabel(performanceMetric)}
                              fill={theme.primary}
                              radius={[4, 4, 0, 0]}
                            />
                          </BarChart>
                        )}
                      </ResponsiveContainer>
                    </div>
                  ) : (
                    <div className="py-8 text-center text-muted-foreground">
                      No performance data available
                    </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </>
      ) : (
        <Card>
          <CardHeader>
            <CardTitle>Advanced Analytics Not Available</CardTitle>
            <CardDescription>
              Advanced analytics are only available on Pro and Enterprise subscription tiers.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-muted-foreground">
              Upgrade your subscription to access detailed analytics and performance metrics.
            </p>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
