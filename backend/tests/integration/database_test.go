package integration

import (
	"testing"

	"github.com/adc-credit/backend/internal/database"
	"github.com/adc-credit/backend/internal/models"
	testutils "github.com/adc-credit/backend/internal/testing"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestDatabaseConnection(t *testing.T) {
	// Setup test database
	err := testutils.SetupTestDB()
	require.NoError(t, err, "Failed to setup test database")

	// Initialize database connection
	err = database.InitDB()
	require.NoError(t, err, "Failed to initialize database")

	// Test basic database operations
	db := database.DB
	require.NotNil(t, db, "Database connection should not be nil")

	// Test creating a simple subscription tier
	tier := &models.SubscriptionTier{
		Name:        "Test Tier",
		Description: "Test tier for database connection",
		Price:       9.99,
		CreditLimit: 1000,
		Features:    models.StringSlice{"test"},
	}

	err = db.Create(tier).Error
	require.NoError(t, err, "Failed to create subscription tier")

	// Test reading the tier back
	var retrievedTier models.SubscriptionTier
	err = db.First(&retrievedTier, "name = ?", "Test Tier").Error
	require.NoError(t, err, "Failed to retrieve subscription tier")

	assert.Equal(t, "Test Tier", retrievedTier.Name)
	assert.Equal(t, "Test tier for database connection", retrievedTier.Description)

	// Cleanup
	db.Delete(&retrievedTier)
	testutils.CleanupTestDB()
}

func TestUserCreation(t *testing.T) {
	// Setup test database
	err := testutils.SetupTestDB()
	require.NoError(t, err, "Failed to setup test database")

	// Initialize database connection
	err = database.InitDB()
	require.NoError(t, err, "Failed to initialize database")

	// Test creating a user
	user, err := testutils.CreateTestUser("<EMAIL>", "Test User")
	require.NoError(t, err, "Failed to create test user")
	require.NotNil(t, user, "User should not be nil")

	assert.Equal(t, "<EMAIL>", user.Email)
	assert.Equal(t, "Test User", user.Name)

	// Cleanup
	testutils.CleanupTestUser(t, user.ID)
	testutils.CleanupTestDB()
}

func TestSubscriptionTierCreation(t *testing.T) {
	// Setup test database
	err := testutils.SetupTestDB()
	require.NoError(t, err, "Failed to setup test database")

	// Initialize database connection
	err = database.InitDB()
	require.NoError(t, err, "Failed to initialize database")

	// Test creating subscription tiers
	db := database.DB

	starter := &models.SubscriptionTier{
		Name:                 "Starter",
		Description:          "Starter plan for testing",
		Price:                0.00,
		CreditLimit:          100000,
		Features:             models.StringSlice{"Basic API access"},
		MaxShops:             1,
		MaxCustomersPerShop:  50,
		MaxAPIKeysPerShop:    1,
		MaxBranchesPerShop:   0,
		MaxQRCodesPerMonth:   500,
		MaxWebhooks:          0,
		AnalyticsHistoryDays: 30,
		SupportLevel:         "community",
		AllowedShopTypes:     models.StringSlice{"retail"},
		UnlimitedShops:       false,
		UnlimitedCustomers:   false,
		UnlimitedQRCodes:     false,
		UnlimitedBranches:    false,
	}

	err = db.Create(starter).Error
	require.NoError(t, err, "Failed to create starter tier")

	// Test reading the tier back
	var retrievedTier models.SubscriptionTier
	err = db.First(&retrievedTier, "name = ?", "Starter").Error
	require.NoError(t, err, "Failed to retrieve subscription tier")

	assert.Equal(t, "Starter", retrievedTier.Name)
	assert.Equal(t, 1, retrievedTier.MaxShops)
	assert.Equal(t, 50, retrievedTier.MaxCustomersPerShop)
	assert.Equal(t, 500, retrievedTier.MaxQRCodesPerMonth)

	// Cleanup
	db.Delete(&retrievedTier)
	testutils.CleanupTestDB()
}
