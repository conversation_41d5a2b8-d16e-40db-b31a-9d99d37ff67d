package main

import (
	"log"

	"github.com/adc-credit/backend/internal/database"
	"github.com/joho/godotenv"
)

func main() {
	// Load environment variables
	if err := godotenv.Load(); err != nil {
		log.Println("No .env file found, using environment variables")
	}

	// Initialize database connection
	if err := database.InitDB(); err != nil {
		log.Fatalf("Failed to connect to database: %v", err)
	}

	// Fix the google_id constraint
	if err := fixGoogleIDConstraint(); err != nil {
		log.Fatalf("Failed to fix google_id constraint: %v", err)
	}

	log.Println("Successfully fixed google_id constraint")
}

func fixGoogleIDConstraint() error {
	// Drop the existing unique index on google_id
	if err := database.DB.Exec("DROP INDEX IF EXISTS idx_users_google_id").Error; err != nil {
		return err
	}

	// Create a new partial index that only applies when google_id is not null and not empty
	if err := database.DB.Exec("CREATE UNIQUE INDEX idx_users_google_id ON users (google_id) WHERE google_id IS NOT NULL AND google_id != ''").Error; err != nil {
		return err
	}

	return nil
}
