#!/bin/bash

# Load environment variables from .env file
if [ -f .env ]; then
  export $(grep -v '^#' .env | xargs)
fi

# Check if PROJECT_ID is set
if [ -z "$PROJECT_ID" ]; then
  echo "Error: PROJECT_ID environment variable is required"
  echo "Please set it in your .env file or pass it as an environment variable"
  exit 1
fi

echo "Deploying frontend to GCP Cloud Run..."
echo "Project ID: $PROJECT_ID"
echo "Region: ${REGION:-us-central1}"

# Deploy frontend to Cloud Run
gcloud run deploy adc-credit-frontend \
  --image gcr.io/$PROJECT_ID/adc-credit-frontend \
  --platform managed \
  --region ${REGION:-us-central1} \
  --allow-unauthenticated \
  --set-env-vars="NEXT_PUBLIC_BACKEND_URL=$BACKEND_URL,NEXTAUTH_URL=$FRONTEND_URL,NEXTAUTH_SECRET=$NEXTAUTH_SECRET,GOOG<PERSON>_CLIENT_ID=$GOOGLE_CLIENT_ID,GOOGLE_CLIENT_SECRET=$GOOGLE_CLIENT_SECRET,NODE_ENV=production"

# Check if frontend deployment was successful
if [ $? -eq 0 ]; then
  echo "Frontend deployment completed successfully!"
  
  # Get the frontend URL
  FRONTEND_URL=$(gcloud run services describe adc-credit-frontend --region=${REGION:-us-central1} --format="value(status.url)")
  
  echo "Frontend URL: $FRONTEND_URL"
  echo ""
  echo "You may need to update your .env file with the new frontend URL:"
  echo "FRONTEND_URL=$FRONTEND_URL"
  echo "NEXTAUTH_URL=$FRONTEND_URL"
else
  echo "Frontend deployment failed. Please check the logs for errors."
  exit 1
fi
