package middleware

import (
	"fmt"
	"net/http"
	"os"
	"strings"
	"sync"
	"time"

	"github.com/adc-credit/backend/internal/database"
	"github.com/adc-credit/backend/internal/models"
	"github.com/gin-gonic/gin"
	"github.com/golang-jwt/jwt/v5"
	"github.com/google/uuid"
)

// Simple in-memory cache for user data
type userCache struct {
	cache  map[uuid.UUID]userCacheEntry
	mutex  sync.RWMutex
	hits   int
	misses int
}

type userCacheEntry struct {
	user      models.User
	expiresAt time.Time
}

var (
	// Global user cache with 15-minute expiration
	userCacheInstance = &userCache{
		cache: make(map[uuid.UUID]userCacheEntry),
	}
)

// Get user from cache
func (c *userCache) get(id uuid.UUID) (models.User, bool) {
	c.mutex.RLock()
	defer c.mutex.RUnlock()

	entry, found := c.cache[id]
	if !found {
		c.misses++
		return models.User{}, false
	}

	// Check if entry has expired
	if time.Now().After(entry.expiresAt) {
		delete(c.cache, id)
		c.misses++
		return models.User{}, false
	}

	c.hits++
	return entry.user, true
}

// Set user in cache with 15-minute expiration (increased from 5 minutes)
func (c *userCache) set(user models.User) {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	c.cache[user.ID] = userCacheEntry{
		user:      user,
		expiresAt: time.Now().Add(15 * time.Minute),
	}
}

// GetCacheStats returns cache hit/miss statistics
func (c *userCache) getStats() (hits, misses int) {
	c.mutex.RLock()
	defer c.mutex.RUnlock()
	return c.hits, c.misses
}

// AuthMiddleware validates JWT tokens for protected routes
func AuthMiddleware() gin.HandlerFunc {
	// Create a local cache for tokens to reduce parsing overhead
	type tokenCacheEntry struct {
		userID    uuid.UUID
		expiresAt time.Time
	}
	tokenCache := make(map[string]tokenCacheEntry)
	var tokenCacheMutex sync.RWMutex

	return func(c *gin.Context) {
		startTime := time.Now()
		authHeader := c.GetHeader("Authorization")
		if authHeader == "" {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "Authorization header is required"})
			c.Abort()
			return
		}

		parts := strings.Split(authHeader, " ")
		if len(parts) != 2 || parts[0] != "Bearer" {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "Authorization header format must be Bearer {token}"})
			c.Abort()
			return
		}

		tokenString := parts[1]
		var userID uuid.UUID
		var cacheHit bool

		// Try to get token from cache first
		tokenCacheMutex.RLock()
		entry, found := tokenCache[tokenString]
		if found && time.Now().Before(entry.expiresAt) {
			userID = entry.userID
			cacheHit = true
		}
		tokenCacheMutex.RUnlock()

		if !cacheHit {
			// Token not in cache, parse and validate
			claims := &jwt.RegisteredClaims{}
			token, err := jwt.ParseWithClaims(tokenString, claims, func(token *jwt.Token) (any, error) {
				if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
					return nil, fmt.Errorf("unexpected signing method: %v", token.Header["alg"])
				}
				return []byte(os.Getenv("JWT_SECRET")), nil
			})

			if err != nil || !token.Valid {
				c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid or expired token"})
				c.Abort()
				return
			}

			// Get user ID from token
			var parseErr error
			userID, parseErr = uuid.Parse(claims.Subject)
			if parseErr != nil {
				c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid user ID in token"})
				c.Abort()
				return
			}

			// Store token in cache for 5 minutes
			tokenCacheMutex.Lock()
			tokenCache[tokenString] = tokenCacheEntry{
				userID:    userID,
				expiresAt: time.Now().Add(5 * time.Minute),
			}
			tokenCacheMutex.Unlock()
		}

		// Try to get user from cache first
		user, found := userCacheInstance.get(userID)
		if !found {
			// If not in cache, fetch from database with optimized query
			// Use Take() instead of First() to avoid the ORDER BY clause
			if err := database.DB.Table("users").Select("id, email, name, picture, role").
				Where("id = ?", userID).
				Where("deleted_at IS NULL").
				Take(&user).Error; err != nil {
				c.JSON(http.StatusUnauthorized, gin.H{"error": "User not found"})
				c.Abort()
				return
			}

			// Store in cache for future requests
			userCacheInstance.set(user)
		}

		// Set user in context
		c.Set("user", user)

		// Log cache performance if request took too long
		elapsed := time.Since(startTime)
		if elapsed > 100*time.Millisecond {
			hits, misses := userCacheInstance.getStats()
			fmt.Printf("Slow auth validation (%v): Cache hits: %d, misses: %d\n", elapsed, hits, misses)
		}

		c.Next()
	}
}

// AdminMiddleware ensures the user has admin role
func AdminMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		user, exists := c.Get("user")
		if !exists {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "User not found in context"})
			c.Abort()
			return
		}

		u, ok := user.(models.User)
		if !ok {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to parse user from context"})
			c.Abort()
			return
		}

		if u.Role != "admin" {
			c.JSON(http.StatusForbidden, gin.H{"error": "Admin access required"})
			c.Abort()
			return
		}

		c.Next()
	}
}

// ValidateAPIKey validates API keys for external API access
func ValidateAPIKey() gin.HandlerFunc {
	// Create a local cache for API keys to reduce database lookups
	type apiKeyCacheEntry struct {
		apiKey    models.APIKey
		expiresAt time.Time
	}
	apiKeyCache := make(map[string]apiKeyCacheEntry)
	var apiKeyCacheMutex sync.RWMutex

	return func(c *gin.Context) {
		startTime := time.Now()
		apiKeyHeader := c.GetHeader("X-API-Key")
		if apiKeyHeader == "" {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "API key is required"})
			c.Abort()
			return
		}

		var apiKey models.APIKey
		var cacheHit bool

		// Try to get API key from local cache first
		apiKeyCacheMutex.RLock()
		entry, found := apiKeyCache[apiKeyHeader]
		if found && time.Now().Before(entry.expiresAt) {
			apiKey = entry.apiKey
			cacheHit = true
		}
		apiKeyCacheMutex.RUnlock()

		if !cacheHit {
			// API key not in cache, fetch from database
			if err := database.DB.Select("id, user_id, name, key, enabled, permissions, rate_limit_max, rate_limit_rate").
				Where("key = ? AND enabled = ?", apiKeyHeader, true).First(&apiKey).Error; err != nil {
				c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid API key"})
				c.Abort()
				return
			}

			// Store API key in cache for 5 minutes
			apiKeyCacheMutex.Lock()
			apiKeyCache[apiKeyHeader] = apiKeyCacheEntry{
				apiKey:    apiKey,
				expiresAt: time.Now().Add(5 * time.Minute),
			}
			apiKeyCacheMutex.Unlock()

			// Update last used timestamp in a separate goroutine to avoid blocking
			go func(apiKeyID uuid.UUID) {
				now := time.Now()
				database.DB.Model(&models.APIKey{}).Where("id = ?", apiKeyID).Update("last_used", &now)
			}(apiKey.ID)
		}

		// Try to get user from cache first
		user, found := userCacheInstance.get(apiKey.UserID)
		if !found {
			// Get user associated with API key - optimized query to select only necessary fields
			// Remove the ORDER BY clause which is unnecessary and can slow down the query
			if err := database.DB.Table("users").Select("id, email, name, picture, role").
				Where("id = ?", apiKey.UserID).
				Where("deleted_at IS NULL").
				Take(&user).Error; err != nil {
				c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to find user associated with API key"})
				c.Abort()
				return
			}

			// Store in cache for future requests
			userCacheInstance.set(user)
		}

		// Set API key and user in context
		c.Set("apiKey", apiKey)
		c.Set("user", user)

		// Log cache performance if request took too long
		elapsed := time.Since(startTime)
		if elapsed > 100*time.Millisecond {
			hits, misses := userCacheInstance.getStats()
			fmt.Printf("Slow API key validation (%v): Cache hits: %d, misses: %d\n", elapsed, hits, misses)
		}

		c.Next()
	}
}
