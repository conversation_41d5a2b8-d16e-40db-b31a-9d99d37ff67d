package utils

import (
	"bytes"
	"encoding/base64"
	"fmt"
	"image/png"

	"github.com/boombuler/barcode"
	"github.com/boombuler/barcode/qr"
)

// GenerateQRCode generates a QR code for the given content
// Returns a base64 encoded PNG image
func GenerateQRCode(content string, width, height int) (string, error) {
	// Create the QR code
	qrCode, err := qr.Encode(content, qr.M, qr.Auto)
	if err != nil {
		return "", fmt.Errorf("failed to encode QR code: %w", err)
	}

	// Scale the QR code to the requested size
	qrCode, err = barcode.Scale(qrCode, width, height)
	if err != nil {
		return "", fmt.Errorf("failed to scale QR code: %w", err)
	}

	// Create a buffer to store the PNG image
	buffer := new(bytes.Buffer)
	if err := png.Encode(buffer, qrCode); err != nil {
		return "", fmt.Errorf("failed to encode QR code as PNG: %w", err)
	}

	// Encode the PNG image as base64
	base64Image := base64.StdEncoding.EncodeToString(buffer.Bytes())
	return base64Image, nil
}

// GenerateQRCodeWithPrefix generates a QR code and adds the data URI prefix
// Returns a base64 encoded PNG image with the data URI prefix
func GenerateQRCodeWithPrefix(content string, width, height int) (string, error) {
	base64Image, err := GenerateQRCode(content, width, height)
	if err != nil {
		return "", err
	}

	// Add the data URI prefix
	return "data:image/png;base64," + base64Image, nil
}
