"use client";

import React, { useState } from "react";
import { useRouter } from "next/navigation";
import Link from "next/link";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { useToast } from "@/components/ui/use-toast";
import { ArrowLeft } from "phosphor-react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import {
  useGetMerchantShopBySlugQuery,
  useGetShopCustomersQuery,
  useAddShopCreditMutation
} from "@/lib/api/apiSlice";
import { ShopCustomer } from "@/types";

// Define form schema with Zod
const formSchema = z.object({
  amount: z.coerce.number().positive({ message: "Amount must be greater than 0" }),
  description: z.string().optional(),
});

type FormValues = z.infer<typeof formSchema>;

export default function AddCreditPage({ params }: { params: { slug: string; customerId: string } }) {
  // Unwrap params using React.use() as recommended by Next.js
  const unwrappedParams = React.use(params as any) as { slug: string; customerId: string };
  const shopSlug = unwrappedParams.slug;
  const customerId = unwrappedParams.customerId;

  const router = useRouter();
  const { toast } = useToast();
  
  // Fetch shop and customer data using slug
  const { data: shop, isLoading: isShopLoading } = useGetMerchantShopBySlugQuery(shopSlug);
  const { data: customers, isLoading: isCustomersLoading } = 
    useGetShopCustomersQuery(shop?.id || "", { skip: !shop?.id });
  
  // Find the specific customer
  const customer = customers?.find(c => c.id === customerId);
  
  // Mutations
  const [addCredit, { isLoading: isAddingCredit }] = useAddShopCreditMutation();
  
  // Initialize React Hook Form
  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      amount: 0,
      description: "",
    },
  });
  
  const onSubmit = async (data: FormValues) => {
    if (!shop?.id) return;

    try {
      await addCredit({
        shopId: shop.id,
        customerId: customerId,
        amount: data.amount,
        description: data.description || ""
      }).unwrap();
      
      toast({
        title: "Success",
        description: "Credit added successfully",
      });
      
      // Navigate back to customers page
      router.push(`/dashboard/merchant/shop/${shopSlug}/customers`);
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.data?.message || "Failed to add credit",
        variant: "destructive",
      });
    }
  };
  
  if (isShopLoading || isCustomersLoading) {
    return <div className="p-4 text-center">Loading...</div>;
  }
  
  if (!shop) {
    return <div className="p-4 text-center">Shop not found</div>;
  }
  
  if (!customer) {
    return <div className="p-4 text-center">Customer not found</div>;
  }

  return (
    <div className="relative flex size-full min-h-screen flex-col bg-[#fbfaf9] justify-between group/design-root overflow-x-hidden">
      <div className="flex-1">
        <div className="flex items-center bg-[#fbfaf9] p-4 pb-2 justify-between">
          <Link href={`/dashboard/merchant/shop/${shopSlug}/customers`} className="text-[#181510] flex size-12 shrink-0 items-center">
            <ArrowLeft size={24} weight="regular" />
          </Link>
          <h2 className="text-[#181510] text-lg font-bold leading-tight tracking-[-0.015em] flex-1 text-center pr-12">Add Credit</h2>
        </div>
        
        <div className="flex items-center gap-4 bg-[#fbfaf9] px-4 min-h-[72px] py-2 border-b border-[#f1edea]">
          <div className="flex flex-col justify-center flex-1">
            <p className="text-[#181510] text-base font-medium leading-normal line-clamp-1">{customer.user?.name}</p>
            <p className="text-[#8a745c] text-sm font-normal leading-normal line-clamp-2">
              Current Balance: ${customer.credit_balance}
            </p>
          </div>
        </div>
        
        <div className="w-full max-w-[480px] mx-auto px-4 py-6">
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
              <FormField
                control={form.control}
                name="amount"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-[#181510] text-base font-medium leading-normal">Amount</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        placeholder="Enter amount"
                        className="form-input flex w-full min-w-0 flex-1 resize-none overflow-hidden rounded-lg text-[#181510] focus:outline-0 focus:ring-0 border-none bg-[#f1edea] focus:border-none h-14 placeholder:text-[#8a745c] p-4 text-base font-normal leading-normal"
                        {...field}
                      />
                    </FormControl>
                    <FormDescription>
                      Enter the amount of credit to add
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              <FormField
                control={form.control}
                name="description"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-[#181510] text-base font-medium leading-normal">Description (Optional)</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Enter description"
                        className="form-input flex w-full min-w-0 flex-1 resize-none overflow-hidden rounded-lg text-[#181510] focus:outline-0 focus:ring-0 border-none bg-[#f1edea] focus:border-none min-h-24 placeholder:text-[#8a745c] p-4 text-base font-normal leading-normal"
                        {...field}
                      />
                    </FormControl>
                    <FormDescription>
                      Add a note about this credit (e.g., "Birthday gift", "Loyalty reward")
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              <div className="pt-4">
                <Button 
                  type="submit"
                  className="w-full cursor-pointer items-center justify-center overflow-hidden rounded-lg h-12 px-5 bg-[#e5ccb2] text-[#181510] text-base font-bold leading-normal tracking-[0.015em] hover:bg-[#d9b99a]"
                  disabled={isAddingCredit}
                >
                  {isAddingCredit ? "Adding Credit..." : "Add Credit"}
                </Button>
              </div>
            </form>
          </Form>
        </div>
      </div>
    </div>
  );
}
