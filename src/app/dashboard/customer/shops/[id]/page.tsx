"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { useSession } from "next-auth/react";
import dynamic from "next/dynamic";
import { use } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Separator } from "@/components/ui/separator";
import { Skeleton } from "@/components/ui/skeleton";
import { <PERSON><PERSON>, <PERSON>ertDescription, AlertTitle } from "@/components/ui/alert";
import { useToast } from "@/components/ui/use-toast";
import {
  CreditCard,
  Storefront,
  ClockCounterClockwise,
  Receipt,
  Phone,
  EnvelopeSimple,
  MapPin,
  ArrowUp,
  ArrowDown,
  QrCode,
  Camera,
  Check,
  X
} from "phosphor-react";
import Link from "next/link";
import { CustomerBreadcrumbs } from "@/components/navigation/breadcrumbs";
import { CustomerPageHeader } from "@/components/layout/page-header-mobile";
import {
  useGetCustomerShopQuery,
  useGetCustomerTransactionsQuery,
  useUseShopCreditMutation
} from "@/lib/api/apiSlice";

// Dynamically import the HTML5QrcodeScanner to avoid SSR issues
const Html5QrcodePlugin = dynamic(() => import("@/components/scanner/html5-qrcode-plugin"), {
  ssr: false,
  loading: () => (
    <div className="flex flex-col items-center justify-center p-8 bg-[#f1edea] rounded-md h-[300px]">
      <QrCode size={64} className="text-[#8a745c] mb-4" />
      <p className="text-[#8a745c]">Loading scanner...</p>
    </div>
  ),
});

export default function CustomerShopDetailPage({ params }: { params: { id: string } }) {
  // Unwrap params using React.use()
  const unwrappedParams = use(params);
  const shopId = unwrappedParams.id;

  const { data: session } = useSession();
  const router = useRouter();
  const { toast } = useToast();

  // Fetch shop data and transactions
  const { data: shop, isLoading: isShopLoading, refetch: refetchShop } = useGetCustomerShopQuery(shopId);
  const { data: transactions, isLoading: isTransactionsLoading, refetch: refetchTransactions } = useGetCustomerTransactionsQuery(shopId);
  const [useCredit, { isLoading: isUsingCredit }] = useUseShopCreditMutation();

  // Dialog states
  const [isUseDialogOpen, setIsUseDialogOpen] = useState(false);
  const [isScanDialogOpen, setIsScanDialogOpen] = useState(false);
  const [creditAmount, setCreditAmount] = useState(0);
  const [creditDescription, setCreditDescription] = useState("");

  // QR code scanning states
  const [scanning, setScanning] = useState(false);
  const [scannedCode, setScannedCode] = useState<string | null>(null);
  const [scanResult, setScanResult] = useState<{
    success: boolean;
    message: string;
    amount?: number;
  } | null>(null);

  // Handle QR code scan success
  const onScanSuccess = (decodedText: string) => {
    setScanning(false);
    setScannedCode(decodedText);

    // Parse the QR code data
    try {
      // Expected format: shop:SHOP_ID;code:CODE;amount:AMOUNT
      const parts = decodedText.split(';');
      const codeData: Record<string, string> = {};

      parts.forEach(part => {
        const [key, value] = part.split(':');
        if (key && value) {
          codeData[key] = value;
        }
      });

      // Verify this QR code is for the current shop
      if (codeData.shop && codeData.shop !== shopId) {
        setScanResult({
          success: false,
          message: "This QR code is for a different shop"
        });
        return;
      }

      // Check if amount is specified
      if (codeData.amount) {
        const amount = parseInt(codeData.amount);
        if (!isNaN(amount) && amount > 0) {
          // Set the amount in the form
          setCreditAmount(amount);
          setScanResult({
            success: true,
            message: `Ready to use ${amount} credits`,
            amount: amount
          });
        } else {
          setScanResult({
            success: false,
            message: "Invalid credit amount in QR code"
          });
        }
      } else {
        setScanResult({
          success: false,
          message: "No credit amount found in QR code"
        });
      }
    } catch (error) {
      console.error("Error parsing QR code:", error);
      setScanResult({
        success: false,
        message: "Invalid QR code format"
      });
    }
  };

  // Handle scan error
  const onScanError = (error: any) => {
    console.error("QR code scan error:", error);
  };

  // Reset scanner state
  const resetScanner = () => {
    setScanning(false);
    setScannedCode(null);
    setScanResult(null);
  };

  // Close scan dialog and open use credits dialog if scan was successful
  const handleScanComplete = () => {
    if (scanResult?.success && scanResult.amount) {
      setIsScanDialogOpen(false);
      setIsUseDialogOpen(true);
    } else {
      resetScanner();
    }
  };

  // Handle using shop credits
  const handleUseCredit = async () => {
    if (creditAmount <= 0) {
      toast({
        title: "Error",
        description: "Credit amount must be greater than 0",
        variant: "destructive",
      });
      return;
    }

    if (shop && creditAmount > shop.credit_balance) {
      toast({
        title: "Error",
        description: "Not enough credits available",
        variant: "destructive",
      });
      return;
    }

    try {
      await useCredit({
        shopId: shopId,
        amount: creditAmount,
        description: creditDescription || "Used for purchase"
      }).unwrap();

      toast({
        title: "Success",
        description: `${creditAmount} credits used successfully`,
      });

      // Refresh data after successful credit use
      refetchShop();
      refetchTransactions();

      // Reset form and close dialog
      setIsUseDialogOpen(false);
      setCreditAmount(0);
      setCreditDescription("");
      resetScanner();
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.data?.message || "Failed to use credits. Please try again.",
        variant: "destructive",
      });
    }
  };

  // Breadcrumb segments
  const breadcrumbSegments = [
    { label: "Dashboard", href: "/dashboard/customer" },
    { label: "Shops", href: "/dashboard/customer/shops" },
    { label: shop?.name || "Shop Details", href: `/dashboard/customer/shops/${shopId}` },
  ];

  // Loading state
  if (isShopLoading) {
    return (
      <div className="space-y-6">
        {/* Breadcrumbs - Desktop only */}
        <div className="hidden md:block">
          <CustomerBreadcrumbs segments={breadcrumbSegments} />
        </div>

        {/* Mobile Header */}
        <div className="md:hidden">
          <CustomerPageHeader
            title="Shop Details"
            backHref="/dashboard/customer/shops"
          />
        </div>

        <div className="p-4">
          <Card>
            <CardHeader>
              <Skeleton className="h-8 w-3/4" />
              <Skeleton className="h-4 w-1/2" />
            </CardHeader>
            <CardContent>
              <Skeleton className="h-12 w-24" />
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  // Error state - shop not found
  if (!shop) {
    return (
      <div className="space-y-6">
        {/* Breadcrumbs - Desktop only */}
        <div className="hidden md:block">
          <CustomerBreadcrumbs segments={breadcrumbSegments} />
        </div>

        {/* Mobile Header */}
        <div className="md:hidden">
          <CustomerPageHeader
            title="Shop Not Found"
            backHref="/dashboard/customer/shops"
          />
        </div>

        <div className="p-4 text-center">
          <Card>
            <CardContent className="pt-6 pb-6">
              <Storefront size={48} className="mx-auto text-[#8a745c] mb-4" />
              <h3 className="text-[#181510] text-lg font-bold mb-2">Shop Not Found</h3>
              <p className="text-[#8a745c] mb-4">
                The shop you're looking for doesn't exist or you don't have access to it.
              </p>
              <Button
                className="bg-[#e5ccb2] text-[#181510] hover:bg-[#d9b99a]"
                onClick={() => router.push('/dashboard/customer/shops')}
              >
                Back to Shops
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Breadcrumbs - Desktop only */}
      <div className="hidden md:block">
        <CustomerBreadcrumbs segments={breadcrumbSegments} />
      </div>

      {/* Mobile Header */}
      <div className="md:hidden">
        <CustomerPageHeader
          title={shop.name}
          backHref="/dashboard/customer/shops"
        />
      </div>

      {/* Page Header - Desktop only */}
      <div className="hidden md:block px-4">
        <h1 className="text-2xl font-bold tracking-tight md:text-3xl text-[#181510]">
          {shop.name}
        </h1>
        <p className="text-[#8a745c] mt-1">
          {shop.description || "Shop details and credit information"}
        </p>
      </div>

      {/* Main Content */}
      <div className="md:max-w-6xl md:mx-auto md:px-6">
        {/* Shop Profile Card */}
        <Card className="mb-6 overflow-hidden">
          <div className="bg-gradient-to-r from-[#e5ccb2] to-[#f1edea] h-32 relative"></div>
          <div className="px-6 pb-6 pt-0 -mt-16 flex flex-col items-center">
            <div className="bg-center bg-no-repeat aspect-square bg-cover rounded-full min-h-32 w-32 border-4 border-white shadow-md"
              style={{
                backgroundImage: shop.image_url
                  ? `url("${shop.image_url}")`
                  : 'url("https://via.placeholder.com/128?text=Shop")'
              }}
            />
            <h2 className="text-[#181510] text-2xl font-bold mt-4">{shop.name}</h2>
            <p className="text-[#8a745c] text-center max-w-md mt-2">{shop.description || "No description available"}</p>

            {/* Contact Information */}
            <div className="flex flex-wrap justify-center gap-4 mt-4">
              {shop.contact_email && (
                <div className="flex items-center gap-2 text-[#8a745c]">
                  <EnvelopeSimple size={18} />
                  <span>{shop.contact_email}</span>
                </div>
              )}
              {shop.contact_phone && (
                <div className="flex items-center gap-2 text-[#8a745c]">
                  <Phone size={18} />
                  <span>{shop.contact_phone}</span>
                </div>
              )}
            </div>
          </div>
        </Card>

        {/* Credit Balance and Actions */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <CreditCard className="mr-2 h-5 w-5" />
                Credit Balance
              </CardTitle>
              <CardDescription>
                Your available credits at this shop
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex flex-col space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <div className="text-4xl font-bold text-[#181510]">{shop.credit_balance}</div>
                    <p className="text-[#8a745c] mt-1">Available credits</p>
                  </div>
                  <div className="flex flex-col space-y-2">
                    <Button
                      className="bg-[#e5ccb2] text-[#181510] hover:bg-[#d9b99a]"
                      onClick={() => setIsScanDialogOpen(true)}
                      disabled={shop.credit_balance <= 0}
                    >
                      <QrCode size={20} className="mr-2" />
                      Scan QR Code
                    </Button>
                    <Button
                      variant="outline"
                      onClick={() => setIsUseDialogOpen(true)}
                      disabled={shop.credit_balance <= 0}
                    >
                      <CreditCard size={20} className="mr-2" />
                      Enter Manually
                    </Button>
                  </div>
                </div>
                <p className="text-xs text-[#8a745c] text-center">
                  Scan a QR code from the merchant to use your credits
                </p>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Receipt className="mr-2 h-5 w-5" />
                Recent Activity
              </CardTitle>
              <CardDescription>
                Your recent transactions
              </CardDescription>
            </CardHeader>
            <CardContent>
              {isTransactionsLoading ? (
                <div className="space-y-2">
                  <Skeleton className="h-6 w-full" />
                  <Skeleton className="h-6 w-full" />
                  <Skeleton className="h-6 w-full" />
                </div>
              ) : transactions && transactions.length > 0 ? (
                <div className="space-y-2">
                  {transactions.slice(0, 3).map((transaction) => (
                    <div key={transaction.id} className="flex items-center justify-between p-2 rounded-md bg-[#f1edea]">
                      <div className="flex items-center gap-2">
                        {transaction.type === "credit_add" ? (
                          <div className="bg-green-100 p-1 rounded-full">
                            <ArrowUp size={16} className="text-green-600" />
                          </div>
                        ) : (
                          <div className="bg-red-100 p-1 rounded-full">
                            <ArrowDown size={16} className="text-red-600" />
                          </div>
                        )}
                        <div>
                          <p className="text-sm font-medium">
                            {transaction.type === "credit_add" ? "Added" : "Used"}
                          </p>
                          <p className="text-xs text-[#8a745c]">
                            {new Date(transaction.created_at).toLocaleDateString()}
                          </p>
                        </div>
                      </div>
                      <span className={transaction.amount < 0 ? "text-red-600 font-medium" : "text-green-600 font-medium"}>
                        {transaction.amount > 0 ? `+${transaction.amount}` : transaction.amount}
                      </span>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-4">
                  <p className="text-[#8a745c]">No transactions yet</p>
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Transaction History */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle className="flex items-center">
              <ClockCounterClockwise className="mr-2 h-5 w-5" />
              Transaction History
            </CardTitle>
            <CardDescription>
              Your complete credit transaction history with this shop
            </CardDescription>
          </CardHeader>
          <CardContent>
            {isTransactionsLoading ? (
              <div className="space-y-4">
                <Skeleton className="h-10 w-full" />
                <Skeleton className="h-10 w-full" />
                <Skeleton className="h-10 w-full" />
              </div>
            ) : transactions && transactions.length > 0 ? (
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Date</TableHead>
                    <TableHead>Type</TableHead>
                    <TableHead>Amount</TableHead>
                    <TableHead className="hidden md:table-cell">Description</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {transactions.map((transaction) => (
                    <TableRow key={transaction.id}>
                      <TableCell>{new Date(transaction.created_at).toLocaleDateString()}</TableCell>
                      <TableCell>
                        {transaction.type === "credit_add" ? "Added" :
                         transaction.type === "credit_use" ? "Used" : "Redeemed"}
                      </TableCell>
                      <TableCell className={transaction.amount < 0 ? "text-red-600 font-medium" : "text-green-600 font-medium"}>
                        {transaction.amount > 0 ? `+${transaction.amount}` : transaction.amount}
                      </TableCell>
                      <TableCell className="hidden md:table-cell">{transaction.description || "-"}</TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            ) : (
              <div className="text-center py-8">
                <ClockCounterClockwise size={32} className="mx-auto text-[#8a745c] mb-2" />
                <p className="text-[#8a745c]">No transaction history available</p>
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Use Credit Dialog */}
      <Dialog open={isUseDialogOpen} onOpenChange={setIsUseDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Use Credits</DialogTitle>
            <DialogDescription>
              Use credits from your balance at {shop.name}
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="p-4 border rounded-md bg-[#f1edea]">
              <p className="font-medium text-[#181510]">Available Balance:</p>
              <p className="text-2xl font-bold text-[#181510]">{shop.credit_balance} credits</p>
            </div>

            <div className="grid gap-2">
              <Label htmlFor="creditAmount">Amount to Use</Label>
              <Input
                id="creditAmount"
                type="number"
                min="1"
                max={shop.credit_balance}
                placeholder="Enter amount"
                value={creditAmount || ""}
                onChange={(e) => setCreditAmount(parseInt(e.target.value) || 0)}
                className="bg-[#f1edea]"
              />
            </div>

            <div className="grid gap-2">
              <Label htmlFor="creditDescription">Description (Optional)</Label>
              <Input
                id="creditDescription"
                placeholder="What are you using credits for?"
                value={creditDescription}
                onChange={(e) => setCreditDescription(e.target.value)}
                className="bg-[#f1edea]"
              />
            </div>
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setIsUseDialogOpen(false)}
            >
              Cancel
            </Button>
            <Button
              onClick={handleUseCredit}
              disabled={isUsingCredit || creditAmount <= 0 || creditAmount > shop.credit_balance}
              className="bg-[#e5ccb2] text-[#181510] hover:bg-[#d9b99a]"
            >
              {isUsingCredit ? "Processing..." : "Use Credits"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* QR Code Scanner Dialog */}
      <Dialog open={isScanDialogOpen} onOpenChange={(open) => {
        setIsScanDialogOpen(open);
        if (!open) {
          resetScanner();
        }
      }}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Scan Merchant QR Code</DialogTitle>
            <DialogDescription>
              Scan a QR code from the merchant to use your credits
            </DialogDescription>
          </DialogHeader>

          <div className="flex flex-col space-y-4 py-4">
            {!scannedCode && !scanResult ? (
              <>
                {scanning ? (
                  <Html5QrcodePlugin
                    fps={10}
                    qrbox={250}
                    disableFlip={false}
                    qrCodeSuccessCallback={onScanSuccess}
                    qrCodeErrorCallback={onScanError}
                  />
                ) : (
                  <div className="flex flex-col items-center justify-center p-8 bg-[#f1edea] rounded-md">
                    <Camera size={64} className="text-[#8a745c] mb-4" />
                    <p className="text-[#8a745c] mb-4">Camera access required to scan QR codes</p>
                    <Button
                      onClick={() => setScanning(true)}
                      className="bg-[#e5ccb2] text-[#181510] hover:bg-[#d9b99a]"
                    >
                      Start Scanner
                    </Button>
                  </div>
                )}
              </>
            ) : scanResult ? (
              <div className="py-4">
                {scanResult.success ? (
                  <Alert className="bg-green-50 border-green-200">
                    <Check className="h-5 w-5 text-green-600" />
                    <AlertTitle className="text-green-800">Success</AlertTitle>
                    <AlertDescription className="text-green-700">
                      {scanResult.message}
                    </AlertDescription>
                  </Alert>
                ) : (
                  <Alert className="bg-red-50 border-red-200">
                    <X className="h-5 w-5 text-red-600" />
                    <AlertTitle className="text-red-800">Error</AlertTitle>
                    <AlertDescription className="text-red-700">
                      {scanResult.message}
                    </AlertDescription>
                  </Alert>
                )}
              </div>
            ) : (
              <div className="py-4">
                <Alert>
                  <QrCode className="h-5 w-5" />
                  <AlertTitle>Processing Code</AlertTitle>
                  <AlertDescription>
                    Reading QR code...
                  </AlertDescription>
                </Alert>
              </div>
            )}
          </div>

          <DialogFooter className="flex justify-between sm:justify-end">
            {scanResult ? (
              <>
                <Button
                  variant="outline"
                  onClick={resetScanner}
                >
                  Scan Again
                </Button>
                {scanResult.success && (
                  <Button
                    onClick={handleScanComplete}
                    className="bg-[#e5ccb2] text-[#181510] hover:bg-[#d9b99a]"
                  >
                    Continue
                  </Button>
                )}
              </>
            ) : (
              <Button
                variant="outline"
                onClick={() => setIsScanDialogOpen(false)}
              >
                Cancel
              </Button>
            )}
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
