package integration

import (
	"bytes"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/adc-credit/backend/internal/database"
	"github.com/adc-credit/backend/internal/models"
	"github.com/adc-credit/backend/internal/routes"
	"github.com/adc-credit/backend/internal/services"
	testutils "github.com/adc-credit/backend/internal/testing"
	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/stretchr/testify/suite"
)

type SubscriptionGuardIntegrationTestSuite struct {
	suite.Suite
	router *gin.Engine
	guard  *services.SubscriptionGuard
}

func (suite *SubscriptionGuardIntegrationTestSuite) SetupSuite() {
	// Initialize test database
	err := testutils.SetupTestDB()
	suite.Require().NoError(err, "Failed to setup test database")

	// Initialize database connection
	err = database.InitDB()
	suite.Require().NoError(err, "Failed to initialize database")

	// Set Gin to test mode
	gin.SetMode(gin.TestMode)

	// Create router with all routes
	suite.router = gin.New()
	routes.RegisterRoutes(suite.router)

	// Initialize subscription guard
	suite.guard = services.NewSubscriptionGuard()
}

func (suite *SubscriptionGuardIntegrationTestSuite) TearDownSuite() {
	testutils.CleanupTestDB()
}

func (suite *SubscriptionGuardIntegrationTestSuite) SetupTest() {
	// Clean up before each test
	testutils.CleanupAllTestData()
}

// Helper function to create subscription tiers
func (suite *SubscriptionGuardIntegrationTestSuite) createTestSubscriptionTiers() (starter, business, enterprise *models.SubscriptionTier) {
	db := database.DB

	starter = &models.SubscriptionTier{
		Name:                 "Starter",
		Description:          "Starter plan for testing",
		Price:                0.00,
		CreditLimit:          100000,
		Features:             models.StringSlice{"Basic API access"},
		MaxShops:             1,
		MaxCustomersPerShop:  50,
		MaxAPIKeysPerShop:    1,
		MaxBranchesPerShop:   0,
		MaxQRCodesPerMonth:   500,
		MaxWebhooks:          0,
		AnalyticsHistoryDays: 30,
		SupportLevel:         "community",
		AllowedShopTypes:     models.StringSlice{"retail"},
		UnlimitedShops:       false,
		UnlimitedCustomers:   false,
		UnlimitedQRCodes:     false,
		UnlimitedBranches:    false,
	}

	business = &models.SubscriptionTier{
		Name:                 "Business",
		Description:          "Business plan for testing",
		Price:                49.00,
		CreditLimit:          1000000,
		Features:             models.StringSlice{"Full API access", "Multiple shops"},
		MaxShops:             5,
		MaxCustomersPerShop:  1000,
		MaxAPIKeysPerShop:    5,
		MaxBranchesPerShop:   10,
		MaxQRCodesPerMonth:   2000,
		MaxWebhooks:          3,
		AnalyticsHistoryDays: 365,
		SupportLevel:         "email",
		AllowedShopTypes:     models.StringSlice{"retail", "api_service"},
		UnlimitedShops:       false,
		UnlimitedCustomers:   false,
		UnlimitedQRCodes:     false,
		UnlimitedBranches:    false,
	}

	enterprise = &models.SubscriptionTier{
		Name:                 "Enterprise",
		Description:          "Enterprise plan for testing",
		Price:                199.00,
		CreditLimit:          5000000,
		Features:             models.StringSlice{"Full API access", "Unlimited everything"},
		MaxShops:             0, // Unlimited
		MaxCustomersPerShop:  0, // Unlimited
		MaxAPIKeysPerShop:    20,
		MaxBranchesPerShop:   0, // Unlimited
		MaxQRCodesPerMonth:   0, // Unlimited
		MaxWebhooks:          15,
		AnalyticsHistoryDays: 0, // Unlimited
		SupportLevel:         "priority",
		AllowedShopTypes:     models.StringSlice{"retail", "api_service", "enterprise"},
		UnlimitedShops:       true,
		UnlimitedCustomers:   true,
		UnlimitedQRCodes:     true,
		UnlimitedBranches:    true,
	}

	suite.Require().NoError(db.Create(starter).Error)
	suite.Require().NoError(db.Create(business).Error)
	suite.Require().NoError(db.Create(enterprise).Error)

	return starter, business, enterprise
}

// Helper function to create user with subscription
func (suite *SubscriptionGuardIntegrationTestSuite) createUserWithSubscription(email, name string, tier *models.SubscriptionTier) (*models.User, *models.Subscription, string) {
	user, token := testutils.CreateTestUserWithAuth(suite.T(), email, name)

	subscription := &models.Subscription{
		ID:                 uuid.New(),
		UserID:             user.ID,
		SubscriptionTierID: tier.ID,
		StartDate:          time.Now(),
		AutoRenew:          true,
		Status:             "active",
		CreditBalance:      tier.CreditLimit,
		SubscriptionType:   "personal",
	}

	suite.Require().NoError(database.DB.Create(subscription).Error)

	return user, subscription, token
}

// Test shop creation limits
func (suite *SubscriptionGuardIntegrationTestSuite) TestShopCreationLimits() {
	starter, business, _ := suite.createTestSubscriptionTiers()

	// Test Starter plan (1 shop limit)
	user1, _, token1 := suite.createUserWithSubscription("<EMAIL>", "Starter User", starter)

	// First shop should succeed
	shopData := map[string]interface{}{
		"name":        "Test Shop 1",
		"description": "Test shop description",
		"shop_type":   "retail",
	}

	body, _ := json.Marshal(shopData)
	req := httptest.NewRequest("POST", "/api/v1/shops", bytes.NewBuffer(body))
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", "Bearer "+token1)

	w := httptest.NewRecorder()
	suite.router.ServeHTTP(w, req)

	suite.Equal(http.StatusCreated, w.Code)

	// Second shop should fail
	shopData2 := map[string]interface{}{
		"name":        "Test Shop 2",
		"description": "Second test shop",
		"shop_type":   "retail",
	}

	body2, _ := json.Marshal(shopData2)
	req2 := httptest.NewRequest("POST", "/api/v1/shops", bytes.NewBuffer(body2))
	req2.Header.Set("Content-Type", "application/json")
	req2.Header.Set("Authorization", "Bearer "+token1)

	w2 := httptest.NewRecorder()
	suite.router.ServeHTTP(w2, req2)

	suite.Equal(http.StatusForbidden, w2.Code)

	var response map[string]interface{}
	json.Unmarshal(w2.Body.Bytes(), &response)
	suite.Contains(response["message"], "Shop limit reached")

	// Test Business plan (5 shop limit)
	user2, _, token2 := suite.createUserWithSubscription("<EMAIL>", "Business User", business)

	// Should be able to create 5 shops
	for i := 1; i <= 5; i++ {
		shopData := map[string]interface{}{
			"name":        fmt.Sprintf("Business Shop %d", i),
			"description": "Business test shop",
			"shop_type":   "retail",
		}

		body, _ := json.Marshal(shopData)
		req := httptest.NewRequest("POST", "/api/v1/shops", bytes.NewBuffer(body))
		req.Header.Set("Content-Type", "application/json")
		req.Header.Set("Authorization", "Bearer "+token2)

		w := httptest.NewRecorder()
		suite.router.ServeHTTP(w, req)

		suite.Equal(http.StatusCreated, w.Code, "Failed to create shop %d", i)
	}

	// 6th shop should fail
	shopData6 := map[string]interface{}{
		"name":        "Business Shop 6",
		"description": "Should fail",
		"shop_type":   "retail",
	}

	body6, _ := json.Marshal(shopData6)
	req6 := httptest.NewRequest("POST", "/api/v1/shops", bytes.NewBuffer(body6))
	req6.Header.Set("Content-Type", "application/json")
	req6.Header.Set("Authorization", "Bearer "+token2)

	w6 := httptest.NewRecorder()
	suite.router.ServeHTTP(w6, req6)

	suite.Equal(http.StatusForbidden, w6.Code)

	// Cleanup
	testutils.CleanupTestUser(suite.T(), user1.ID)
	testutils.CleanupTestUser(suite.T(), user2.ID)
}

// Test shop type restrictions
func (suite *SubscriptionGuardIntegrationTestSuite) TestShopTypeRestrictions() {
	starter, business, _ := suite.createTestSubscriptionTiers()

	// Test Starter plan (only retail allowed)
	user1, _, token1 := suite.createUserWithSubscription("<EMAIL>", "Starter User", starter)

	// Retail shop should succeed
	retailShop := map[string]interface{}{
		"name":        "Retail Shop",
		"description": "Retail test shop",
		"shop_type":   "retail",
	}

	body, _ := json.Marshal(retailShop)
	req := httptest.NewRequest("POST", "/api/v1/shops", bytes.NewBuffer(body))
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", "Bearer "+token1)

	w := httptest.NewRecorder()
	suite.router.ServeHTTP(w, req)

	suite.Equal(http.StatusCreated, w.Code)

	// API service shop should fail for starter
	user2, _, token2 := suite.createUserWithSubscription("<EMAIL>", "Starter User 2", starter)

	apiShop := map[string]interface{}{
		"name":        "API Shop",
		"description": "API test shop",
		"shop_type":   "api_service",
	}

	body2, _ := json.Marshal(apiShop)
	req2 := httptest.NewRequest("POST", "/api/v1/shops", bytes.NewBuffer(body2))
	req2.Header.Set("Content-Type", "application/json")
	req2.Header.Set("Authorization", "Bearer "+token2)

	w2 := httptest.NewRecorder()
	suite.router.ServeHTTP(w2, req2)

	suite.Equal(http.StatusForbidden, w2.Code)

	// Test Business plan (retail and api_service allowed)
	user3, _, token3 := suite.createUserWithSubscription("<EMAIL>", "Business User", business)

	// API service shop should succeed for business
	body3, _ := json.Marshal(apiShop)
	req3 := httptest.NewRequest("POST", "/api/v1/shops", bytes.NewBuffer(body3))
	req3.Header.Set("Content-Type", "application/json")
	req3.Header.Set("Authorization", "Bearer "+token3)

	w3 := httptest.NewRecorder()
	suite.router.ServeHTTP(w3, req3)

	suite.Equal(http.StatusCreated, w3.Code)

	// Cleanup
	testutils.CleanupTestUser(suite.T(), user1.ID)
	testutils.CleanupTestUser(suite.T(), user2.ID)
	testutils.CleanupTestUser(suite.T(), user3.ID)
}

// Test customer limits per shop
func (suite *SubscriptionGuardIntegrationTestSuite) TestCustomerLimits() {
	starter, _, _ := suite.createTestSubscriptionTiers()

	// Create user with starter plan (50 customers per shop)
	user, _, token := suite.createUserWithSubscription("<EMAIL>", "Starter User", starter)

	// Create a shop first
	shop, err := testutils.CreateTestShop("Test Shop", "test-shop", user.ID, "retail")
	suite.Require().NoError(err)

	// Add 50 customers (should succeed)
	for i := 1; i <= 50; i++ {
		customerData := map[string]interface{}{
			"name":  fmt.Sprintf("Customer %d", i),
			"email": fmt.Sprintf("<EMAIL>", i),
			"phone": fmt.Sprintf("+1234567%04d", i),
		}

		body, _ := json.Marshal(customerData)
		req := httptest.NewRequest("POST", fmt.Sprintf("/api/v1/shops/%s/customers", shop.ID), bytes.NewBuffer(body))
		req.Header.Set("Content-Type", "application/json")
		req.Header.Set("Authorization", "Bearer "+token)

		w := httptest.NewRecorder()
		suite.router.ServeHTTP(w, req)

		suite.Equal(http.StatusCreated, w.Code, "Failed to create customer %d", i)
	}

	// 51st customer should fail
	customerData51 := map[string]interface{}{
		"name":  "Customer 51",
		"email": "<EMAIL>",
		"phone": "+12345670051",
	}

	body51, _ := json.Marshal(customerData51)
	req51 := httptest.NewRequest("POST", fmt.Sprintf("/api/v1/shops/%s/customers", shop.ID), bytes.NewBuffer(body51))
	req51.Header.Set("Content-Type", "application/json")
	req51.Header.Set("Authorization", "Bearer "+token)

	w51 := httptest.NewRecorder()
	suite.router.ServeHTTP(w51, req51)

	suite.Equal(http.StatusForbidden, w51.Code)

	var response map[string]interface{}
	json.Unmarshal(w51.Body.Bytes(), &response)
	suite.Contains(response["message"], "Customer limit reached")

	// Cleanup
	testutils.CleanupTestUser(suite.T(), user.ID)
}

// Test API key limits per shop
func (suite *SubscriptionGuardIntegrationTestSuite) TestAPIKeyLimits() {
	starter, business, _ := suite.createTestSubscriptionTiers()

	// Test Starter plan (1 API key per shop)
	user1, _, token1 := suite.createUserWithSubscription("<EMAIL>", "Starter User", starter)
	shop1, err := testutils.CreateTestShop("Test Shop 1", "test-shop-1", user1.ID, "retail")
	suite.Require().NoError(err)

	// First API key should succeed
	apiKeyData := map[string]interface{}{
		"name":        "Test API Key 1",
		"description": "Test API key description",
	}

	body, _ := json.Marshal(apiKeyData)
	req := httptest.NewRequest("POST", fmt.Sprintf("/api/v1/shops/%s/apikeys", shop1.ID), bytes.NewBuffer(body))
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", "Bearer "+token1)

	w := httptest.NewRecorder()
	suite.router.ServeHTTP(w, req)

	suite.Equal(http.StatusCreated, w.Code)

	// Second API key should fail
	apiKeyData2 := map[string]interface{}{
		"name":        "Test API Key 2",
		"description": "Should fail",
	}

	body2, _ := json.Marshal(apiKeyData2)
	req2 := httptest.NewRequest("POST", fmt.Sprintf("/api/v1/shops/%s/apikeys", shop1.ID), bytes.NewBuffer(body2))
	req2.Header.Set("Content-Type", "application/json")
	req2.Header.Set("Authorization", "Bearer "+token1)

	w2 := httptest.NewRecorder()
	suite.router.ServeHTTP(w2, req2)

	suite.Equal(http.StatusForbidden, w2.Code)

	// Test Business plan (5 API keys per shop)
	user2, _, token2 := suite.createUserWithSubscription("<EMAIL>", "Business User", business)
	shop2, err := testutils.CreateTestShop("Test Shop 2", "test-shop-2", user2.ID, "retail")
	suite.Require().NoError(err)

	// Should be able to create 5 API keys
	for i := 1; i <= 5; i++ {
		apiKeyData := map[string]interface{}{
			"name":        fmt.Sprintf("Business API Key %d", i),
			"description": "Business API key",
		}

		body, _ := json.Marshal(apiKeyData)
		req := httptest.NewRequest("POST", fmt.Sprintf("/api/v1/shops/%s/apikeys", shop2.ID), bytes.NewBuffer(body))
		req.Header.Set("Content-Type", "application/json")
		req.Header.Set("Authorization", "Bearer "+token2)

		w := httptest.NewRecorder()
		suite.router.ServeHTTP(w, req)

		suite.Equal(http.StatusCreated, w.Code, "Failed to create API key %d", i)
	}

	// 6th API key should fail
	apiKeyData6 := map[string]interface{}{
		"name":        "Business API Key 6",
		"description": "Should fail",
	}

	body6, _ := json.Marshal(apiKeyData6)
	req6 := httptest.NewRequest("POST", fmt.Sprintf("/api/v1/shops/%s/apikeys", shop2.ID), bytes.NewBuffer(body6))
	req6.Header.Set("Content-Type", "application/json")
	req6.Header.Set("Authorization", "Bearer "+token2)

	w6 := httptest.NewRecorder()
	suite.router.ServeHTTP(w6, req6)

	suite.Equal(http.StatusForbidden, w6.Code)

	// Cleanup
	testutils.CleanupTestUser(suite.T(), user1.ID)
	testutils.CleanupTestUser(suite.T(), user2.ID)
}

// Test webhook limits
func (suite *SubscriptionGuardIntegrationTestSuite) TestWebhookLimits() {
	starter, business, _ := suite.createTestSubscriptionTiers()

	// Test Starter plan (0 webhooks allowed)
	user1, _, token1 := suite.createUserWithSubscription("<EMAIL>", "Starter User", starter)

	webhookData := map[string]interface{}{
		"url":    "https://example.com/webhook",
		"events": []string{"credit.added", "credit.used"},
	}

	body, _ := json.Marshal(webhookData)
	req := httptest.NewRequest("POST", "/api/v1/webhooks", bytes.NewBuffer(body))
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", "Bearer "+token1)

	w := httptest.NewRecorder()
	suite.router.ServeHTTP(w, req)

	suite.Equal(http.StatusForbidden, w.Code)

	// Test Business plan (3 webhooks allowed)
	user2, _, token2 := suite.createUserWithSubscription("<EMAIL>", "Business User", business)

	// Should be able to create 3 webhooks
	for i := 1; i <= 3; i++ {
		webhookData := map[string]interface{}{
			"url":    fmt.Sprintf("https://example.com/webhook%d", i),
			"events": []string{"credit.added", "credit.used"},
		}

		body, _ := json.Marshal(webhookData)
		req := httptest.NewRequest("POST", "/api/v1/webhooks", bytes.NewBuffer(body))
		req.Header.Set("Content-Type", "application/json")
		req.Header.Set("Authorization", "Bearer "+token2)

		w := httptest.NewRecorder()
		suite.router.ServeHTTP(w, req)

		suite.Equal(http.StatusCreated, w.Code, "Failed to create webhook %d", i)
	}

	// 4th webhook should fail
	webhookData4 := map[string]interface{}{
		"url":    "https://example.com/webhook4",
		"events": []string{"credit.added"},
	}

	body4, _ := json.Marshal(webhookData4)
	req4 := httptest.NewRequest("POST", "/api/v1/webhooks", bytes.NewBuffer(body4))
	req4.Header.Set("Content-Type", "application/json")
	req4.Header.Set("Authorization", "Bearer "+token2)

	w4 := httptest.NewRecorder()
	suite.router.ServeHTTP(w4, req4)

	suite.Equal(http.StatusForbidden, w4.Code)

	// Cleanup
	testutils.CleanupTestUser(suite.T(), user1.ID)
	testutils.CleanupTestUser(suite.T(), user2.ID)
}

func TestSubscriptionGuardIntegrationTestSuite(t *testing.T) {
	suite.Run(t, new(SubscriptionGuardIntegrationTestSuite))
}
