version: '3.8'

services:
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    ports:
      - "8400:8400"
    environment:
      - DATABASE_URL=${DATABASE_URL}
      - JWT_SECRET=${JWT_SECRET}
      - GOOGLE_CLIENT_ID=${GOOGLE_CLIENT_ID}
      - GOO<PERSON><PERSON>_CLIENT_SECRET=${GOOGLE_CLIENT_SECRET}
      - FRONTEND_URL=${FRONTEND_URL:-http://localhost:3800}
      - SCHEDULER_API_KEY=${SCHEDULER_API_KEY}
    restart: unless-stopped
    networks:
      - adc-network

  frontend:
    build:
      context: .
      dockerfile: Dockerfile.simple
    ports:
      - "3800:3800"
    environment:
      - NEXT_PUBLIC_BACKEND_URL=${BACKEND_URL:-http://backend:8400}
      - NEXTAUTH_URL=${FRONTEND_URL:-http://localhost:3800}
      - NEXTAUTH_SECRET=${NEXTAUTH_SECRET}
      - GO<PERSON><PERSON><PERSON>_CLIENT_ID=${GOOGLE_CLIENT_ID}
      - GOO<PERSON><PERSON>_CLIENT_SECRET=${GOOGLE_CLIENT_SECRET}
    depends_on:
      - backend
    restart: unless-stopped
    networks:
      - adc-network

networks:
  adc-network:
    driver: bridge
