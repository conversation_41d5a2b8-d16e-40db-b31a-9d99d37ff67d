"use client";

import { useState } from "react";
import { Card, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { ShopWithCredit } from "@/sdk/customer";
import { toast } from "sonner";
import { useRouter } from "next/navigation";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import {
  useGetCustomerShopsQuery,
  useCreateSubscriptionMutation
} from "@/lib/api/apiSlice";
import { ShoppingBag, CreditCard, QrCode, AlertCircle, Coffee, Check, Zap } from "lucide-react";
import { Progress } from "@/components/ui/progress";
import { Ta<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";

export default function CustomerSubscriptionPanel() {
  const router = useRouter();
  const [selectedShop, setSelectedShop] = useState<ShopWithCredit | null>(null);
  const [selectedTier, setSelectedTier] = useState<any | null>(null);
  const [confirmDialogOpen, setConfirmDialogOpen] = useState(false);
  const [isUpgrading, setIsUpgrading] = useState(false);

  // Fetch customer shops
  const {
    data: shops = [],
    isLoading: isLoadingShops,
  } = useGetCustomerShopsQuery();

  // Create subscription mutation
  const [createSubscription, { isLoading: isCreatingSubscription }] = useCreateSubscriptionMutation();

  // Define customer subscription tiers
  const customerTiers = [
    {
      id: 1,
      name: "Customer Basic",
      description: "Basic features for customers",
      price: 0,
      credit_limit: 1000,
      features: [
        "Access to merchant shops",
        "Basic credit management",
        "Standard QR code scanning",
        "Transaction history",
        "Email support"
      ],
      icon: <CreditCard className="h-5 w-5 text-blue-500" />
    },
    {
      id: 2,
      name: "Customer Premium",
      description: "Enhanced features for loyal customers",
      price: 4.99,
      credit_limit: 5000,
      features: [
        "All Basic features",
        "Priority customer service",
        "Exclusive promotions",
        "Advanced transaction analytics",
        "Credit alerts and notifications",
        "Multi-shop credit management"
      ],
      icon: <Zap className="h-5 w-5 text-purple-500" />
    }
  ];

  // Handle subscription confirmation
  const handleSubscribeConfirm = async () => {
    if (!selectedShop || !selectedTier) return;

    setIsUpgrading(true);
    try {
      // For free tiers, create subscription directly
      if (selectedTier.price === 0) {
        await createSubscription({
          subscription_tier_id: selectedTier.id,
          auto_renew: true,
          subscription_type: "customer",
          shop_customer_id: selectedShop.id
        }).unwrap();

        toast.success(`Successfully subscribed to ${selectedTier.name} plan for ${selectedShop.name}`);
        setConfirmDialogOpen(false);
      } else {
        // For paid tiers, create a Stripe checkout session using the proxy API
        const response = await fetch('/api/stripe/create-checkout-session', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            tier_id: selectedTier.id,
            subscription_type: 'customer',
            shop_customer_id: selectedShop.id,
          }),
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || 'Failed to create checkout session');
        }

        const data = await response.json();

        // Check if it's a free tier response from the backend
        if (data.free_tier) {
          // For free tiers, create subscription directly
          await createSubscription({
            subscription_tier_id: selectedTier.id,
            auto_renew: true,
            subscription_type: "customer",
            shop_customer_id: selectedShop.id
          }).unwrap();

          toast.success(`Successfully subscribed to ${selectedTier.name} plan for ${selectedShop.name}`);
          setConfirmDialogOpen(false);
          return;
        }

        if (!data.checkout_url) {
          throw new Error('Failed to create checkout session');
        }

        // Close the dialog
        setConfirmDialogOpen(false);

        // Redirect to Stripe Checkout
        window.location.href = data.checkout_url;
      }
    } catch (error) {
      toast.error("Failed to create subscription");
      console.error("Error creating subscription:", error);
    } finally {
      setIsUpgrading(false);
    }
  };

  // Loading state
  if (isLoadingShops) {
    return (
      <Card className="mb-8 border shadow-sm">
        <CardHeader className="pb-3">
          <CardTitle>Customer Credit Balances</CardTitle>
          <CardDescription>
            View your credit balances at merchant shops
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex justify-center py-8">
            <div className="h-8 w-8 animate-spin rounded-full border-b-2 border-primary"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  // No shops state
  if (shops.length === 0) {
    return (
      <Card className="mb-8 border shadow-sm">
        <CardHeader className="pb-3">
          <CardTitle>Customer Credit Balances</CardTitle>
          <CardDescription>
            View your credit balances at merchant shops
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Alert className="mb-4">
            <AlertCircle className="h-4 w-4 mr-2" />
            <AlertDescription>
              You are not a customer of any merchant shops yet. Scan a QR code from a merchant to add credits.
            </AlertDescription>
          </Alert>
          <Button onClick={() => router.push('/dashboard/customer/scan')}>
            <QrCode className="h-4 w-4 mr-2" />
            Scan QR Code
          </Button>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="mb-8 border shadow-sm">
      <CardHeader className="pb-3">
        <CardTitle>Customer Subscriptions</CardTitle>
        <CardDescription>
          Manage your customer subscriptions and credit balances
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="balances" className="w-full">
          <TabsList className="grid w-full grid-cols-2 mb-6">
            <TabsTrigger value="balances">
              <CreditCard className="h-4 w-4 mr-2" />
              Credit Balances
            </TabsTrigger>
            <TabsTrigger value="subscriptions">
              <Zap className="h-4 w-4 mr-2" />
              Subscription Plans
            </TabsTrigger>
          </TabsList>

          <TabsContent value="balances">
            <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
              {shops.map((shop) => (
                <Card key={shop.id} className="border shadow-sm hover:shadow-md transition-shadow">
                  <CardHeader className="pb-2">
                    <CardTitle className="text-lg">{shop.name}</CardTitle>
                    <CardDescription className="line-clamp-2">{shop.description}</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div>
                        <div className="flex justify-between mb-1">
                          <span className="text-sm font-medium">Credit Balance</span>
                          <span className="text-sm font-medium">{shop.credit_balance}</span>
                        </div>
                        <Progress value={shop.credit_balance > 0 ? 100 : 0} className="h-2" />
                      </div>

                      <div className="grid grid-cols-2 gap-2 text-sm">
                        <div className="flex flex-col">
                          <span className="text-muted-foreground">Contact</span>
                          <span className="truncate">{shop.contact_email}</span>
                        </div>
                        <div className="flex flex-col">
                          <span className="text-muted-foreground">Phone</span>
                          <span>{shop.contact_phone || "N/A"}</span>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                  <CardFooter className="pt-0">
                    <div className="flex space-x-2 w-full">
                      <Button
                        variant="outline"
                        size="sm"
                        className="flex-1"
                        onClick={() => router.push(`/dashboard/customer/shops/${shop.slug}`)}
                      >
                        View Details
                      </Button>
                      <Button
                        size="sm"
                        className="flex-1"
                        onClick={() => router.push(`/dashboard/customer/shops/${shop.slug}/use`)}
                      >
                        <Coffee className="h-4 w-4 mr-2" />
                        Use Credits
                      </Button>
                    </div>
                  </CardFooter>
                </Card>
              ))}
            </div>

            <div className="mt-6 flex justify-center">
              <Button onClick={() => router.push('/dashboard/customer/scan')}>
                <QrCode className="h-4 w-4 mr-2" />
                Scan QR Code to Add Credits
              </Button>
            </div>
          </TabsContent>

          <TabsContent value="subscriptions">
            {shops.length === 0 ? (
              <Alert className="mb-4">
                <AlertCircle className="h-4 w-4 mr-2" />
                <AlertDescription>
                  You need to be a customer of at least one merchant shop to subscribe to a customer plan.
                </AlertDescription>
              </Alert>
            ) : (
              <>
                <div className="mb-6">
                  <h3 className="text-lg font-semibold mb-4">Select a Shop</h3>
                  <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                    {shops.map((shop) => (
                      <Card
                        key={shop.id}
                        className={`border cursor-pointer transition-all ${selectedShop?.id === shop.id ? 'border-primary ring-2 ring-primary/20' : 'hover:border-primary/50'}`}
                        onClick={() => setSelectedShop(shop)}
                      >
                        <CardHeader className="pb-2">
                          <CardTitle className="text-lg">{shop.name}</CardTitle>
                        </CardHeader>
                        <CardContent>
                          <div className="text-sm text-muted-foreground mb-2">Current Balance: {shop.credit_balance} credits</div>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                </div>

                {selectedShop && (
                  <>
                    <Separator className="my-6" />

                    <h3 className="text-lg font-semibold mb-4">Customer Subscription Plans for {selectedShop.name}</h3>
                    <div className="grid gap-6 md:grid-cols-2">
                      {customerTiers.map((tier) => (
                        <Card
                          key={tier.id}
                          className={`border cursor-pointer transition-all ${selectedTier?.id === tier.id ? 'border-primary ring-2 ring-primary/20' : 'hover:border-primary/50'}`}
                          onClick={() => setSelectedTier(tier)}
                        >
                          <CardHeader className="pb-2">
                            <div className="flex items-center justify-between">
                              <CardTitle className="text-lg">{tier.name}</CardTitle>
                              {tier.icon}
                            </div>
                            <CardDescription>{tier.description}</CardDescription>
                          </CardHeader>
                          <CardContent>
                            <div className="text-2xl font-bold mb-2">
                              ${tier.price.toFixed(2)}
                              <span className="text-sm font-normal text-muted-foreground">
                                /month
                              </span>
                            </div>
                            <div className="space-y-2 mb-4">
                              <div className="flex justify-between text-sm">
                                <span>Credits:</span>
                                <span className="font-medium">
                                  {tier.credit_limit.toLocaleString()}
                                </span>
                              </div>
                            </div>
                            <ul className="space-y-2 text-sm">
                              {tier.features.map((featureStr, index) => {
                                // Parse the feature string which is in JSON format
                                try {
                                  // Remove the curly braces and parse as an array
                                  const cleanedStr = featureStr.replace(/^\{|\}$/g, '');
                                  const featuresArray = JSON.parse(`[${cleanedStr}]`);

                                  return featuresArray.map((feature: string, innerIndex: number) => (
                                    <li key={`${index}-${innerIndex}`} className="flex items-start">
                                      <Check className="h-4 w-4 mr-2 text-green-500 mt-0.5" />
                                      <span>{feature}</span>
                                    </li>
                                  ));
                                } catch {
                                  // Fallback to displaying the raw string if parsing fails
                                  return (
                                    <li key={index} className="flex items-start">
                                      <Check className="h-4 w-4 mr-2 text-green-500 mt-0.5" />
                                      <span>{featureStr}</span>
                                    </li>
                                  );
                                }
                              })}
                            </ul>
                          </CardContent>
                        </Card>
                      ))}
                    </div>

                    {selectedTier && (
                      <div className="mt-6 flex justify-end">
                        <Button onClick={() => setConfirmDialogOpen(true)}>
                          <CreditCard className="h-4 w-4 mr-2" />
                          Subscribe to {selectedTier.name}
                        </Button>
                      </div>
                    )}
                  </>
                )}
              </>
            )}
          </TabsContent>
        </Tabs>
      </CardContent>

      <Dialog open={confirmDialogOpen} onOpenChange={setConfirmDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Confirm Customer Subscription</DialogTitle>
            <DialogDescription>
              Are you sure you want to subscribe to the {selectedTier?.name} plan for {selectedShop?.name}?
              {selectedTier?.price > 0 && ` This will be billed at $${selectedTier?.price.toFixed(2)}/month.`}
            </DialogDescription>
          </DialogHeader>
          <div className="py-4">
            <h4 className="font-medium mb-2">Plan Details:</h4>
            <ul className="space-y-2 text-sm">
              <li className="flex justify-between">
                <span>Monthly Price:</span>
                <span className="font-medium">${selectedTier?.price.toFixed(2)}</span>
              </li>
              <li className="flex justify-between">
                <span>Credits:</span>
                <span className="font-medium">{selectedTier?.credit_limit.toLocaleString()}</span>
              </li>
              <li className="flex justify-between">
                <span>Type:</span>
                <span className="font-medium">Customer Subscription</span>
              </li>
            </ul>

            <Alert className="mt-4">
              <AlertDescription>
                This subscription will be linked to your customer account at {selectedShop?.name} and will provide customer-specific features.
              </AlertDescription>
            </Alert>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setConfirmDialogOpen(false)}>Cancel</Button>
            <Button onClick={handleSubscribeConfirm} disabled={isUpgrading}>
              {isUpgrading ? "Processing..." : "Confirm Subscription"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </Card>
  );
}
