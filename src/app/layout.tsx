import type { Metada<PERSON> } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON>eist_Mono } from "next/font/google";
import "./globals.css";
import { Providers } from "./providers";
import { ReduxProvider } from "@/components/redux/redux-provider";
import { Toaster as SonnerToaster } from "@/components/ui/sonner";
import { Toaster } from "@/components/ui/toaster";
import { Navbar } from "@/components/layout/navbar";
import { Footer } from "@/components/layout/footer";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "ADC Credit System",
  description: "API Credit Management System",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased bg-background min-h-screen w-full`}
      >
        <Providers>
          <ReduxProvider>
            <div className="flex min-h-screen flex-col w-full items-center">
              <Navbar />
              <main className="flex-1 w-full">
                {children}
              </main>
              {/* <Footer /> */}
              <SonnerToaster />
              <Toaster />
            </div>
          </ReduxProvider>
        </Providers>
      </body>
    </html>
  );
}
