"use client";

import { CodeBlockCode } from "@/components/ui/code-block";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";

export default function ApiExamplesPage() {
  // Example code snippets for different languages
  const verifyApiKeyJs = `// JavaScript - Verify API Key
const verifyApiKey = async (apiKey, credits) => {
  try {
    const response = await fetch('https://api.example.com/v1/external/verify', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        api_key: apiKey,
        credits: credits
      })
    });

    const data = await response.json();

    if (!response.ok) {
      throw new Error(data.error || 'Failed to verify API key');
    }

    return data; // { valid: true, credit_balance: 8500 }
  } catch (error) {
    console.error('Error verifying API key:', error);
    throw error;
  }
};`;

  const consumeCreditsJs = `// JavaScript - Consume Credits
const consumeCredits = async (apiKey, endpoint, method, credits) => {
  try {
    const response = await fetch('https://api.example.com/v1/external/consume', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-API-Key': apiKey
      },
      body: JSON.stringify({
        endpoint: endpoint,
        method: method,
        credits: credits,
        ip_address: '***********',  // Optional
        user_agent: 'MyApp/1.0'     // Optional
      })
    });

    const data = await response.json();

    if (!response.ok) {
      throw new Error(data.error || 'Failed to consume credits');
    }

    return data; // { message: "Credits consumed successfully", credit_balance: 8499, usage: {...} }
  } catch (error) {
    console.error('Error consuming credits:', error);
    throw error;
  }
};`;

  const verifyApiKeyPython = `# Python - Verify API Key
import requests

def verify_api_key(api_key, credits):
    try:
        response = requests.post(
            'https://api.example.com/v1/external/verify',
            json={
                'api_key': api_key,
                'credits': credits
            }
        )

        response.raise_for_status()  # Raise exception for 4XX/5XX responses
        return response.json()  # { "valid": true, "credit_balance": 8500 }

    except requests.exceptions.HTTPError as e:
        error_data = e.response.json()
        print(f"Error verifying API key: {error_data.get('error')}")
        raise`;

  const consumeCreditsPython = `# Python - Consume Credits
import requests

def consume_credits(api_key, endpoint, method, credits):
    try:
        response = requests.post(
            'https://api.example.com/v1/external/consume',
            headers={
                'X-API-Key': api_key
            },
            json={
                'endpoint': endpoint,
                'method': method,
                'credits': credits,
                'ip_address': '***********',  # Optional
                'user_agent': 'MyApp/1.0'     # Optional
            }
        )

        response.raise_for_status()
        return response.json()

    except requests.exceptions.HTTPError as e:
        error_data = e.response.json()
        print(f"Error consuming credits: {error_data.get('error')}")
        raise`;

  const verifyApiKeyGo = `// Go - Verify API Key
package main

import (
	"bytes"
	"encoding/json"
	"fmt"
	"net/http"
)

type VerifyRequest struct {
	APIKey  string \`json:"api_key"\`
	Credits int    \`json:"credits"\`
}

type VerifyResponse struct {
	Valid         bool \`json:"valid"\`
	CreditBalance int  \`json:"credit_balance"\`
}

func verifyAPIKey(apiKey string, credits int) (*VerifyResponse, error) {
	reqBody, err := json.Marshal(VerifyRequest{
		APIKey:  apiKey,
		Credits: credits,
	})
	if err != nil {
		return nil, err
	}

	resp, err := http.Post(
		"https://api.example.com/v1/external/verify",
		"application/json",
		bytes.NewBuffer(reqBody),
	)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		var errorResp map[string]interface{}
		json.NewDecoder(resp.Body).Decode(&errorResp)
		return nil, fmt.Errorf("API error: %v", errorResp["error"])
	}

	var verifyResp VerifyResponse
	if err := json.NewDecoder(resp.Body).Decode(&verifyResp); err != nil {
		return nil, err
	}

	return &verifyResp, nil
}`;

  const consumeCreditsGo = `// Go - Consume Credits
package main

import (
	"bytes"
	"encoding/json"
	"fmt"
	"net/http"
)

type ConsumeRequest struct {
	Endpoint  string \`json:"endpoint"\`
	Method    string \`json:"method"\`
	Credits   int    \`json:"credits"\`
	IPAddress string \`json:"ip_address,omitempty"\`
	UserAgent string \`json:"user_agent,omitempty"\`
}

type ConsumeResponse struct {
	Message       string      \`json:"message"\`
	CreditBalance int         \`json:"credit_balance"\`
	Usage         interface{} \`json:"usage"\`
}

func consumeCredits(apiKey, endpoint, method string, credits int) (*ConsumeResponse, error) {
	reqBody, err := json.Marshal(ConsumeRequest{
		Endpoint:  endpoint,
		Method:    method,
		Credits:   credits,
		IPAddress: "***********", // Optional
		UserAgent: "MyApp/1.0",   // Optional
	})
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("POST", "https://api.example.com/v1/external/consume", bytes.NewBuffer(reqBody))
	if err != nil {
		return nil, err
	}

	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("X-API-Key", apiKey)

	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		var errorResp map[string]interface{}
		json.NewDecoder(resp.Body).Decode(&errorResp)
		return nil, fmt.Errorf("API error: %v", errorResp["error"])
	}

	var consumeResp ConsumeResponse
	if err := json.NewDecoder(resp.Body).Decode(&consumeResp); err != nil {
		return nil, err
	}

	return &consumeResp, nil
}`;

  const webhookExample = `// Example Webhook Payload
{
  "id": "evt_123e4567-e89b-12d3-a456-426614174000",
  "event": "credit.consumed",
  "created_at": "2023-05-18T12:00:00Z",
  "data": {
    "user_id": "usr_123e4567-e89b-12d3-a456-426614174000",
    "api_key_id": "key_123e4567-e89b-12d3-a456-426614174000",
    "credits": 5,
    "endpoint": "/api/data",
    "method": "GET",
    "credit_balance": 8495,
    "transaction_id": "txn_123e4567-e89b-12d3-a456-426614174000",
    "subscription_id": "sub_123e4567-e89b-12d3-a456-426614174000"
  }
}`;

  const rateLimitExample = `// Rate Limit Response Example
// HTTP Status: 429 Too Many Requests
{
  "error": "Rate limit exceeded",
  "retry_after": 10  // Seconds until next token is available
}`;

  return (
    <div className="w-full">
      <main className="container py-10">
        <h1 className="text-3xl font-bold mb-8">API Integration Examples</h1>

        <Tabs defaultValue="javascript" className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="javascript">JavaScript</TabsTrigger>
            <TabsTrigger value="python">Python</TabsTrigger>
            <TabsTrigger value="go">Go</TabsTrigger>
          </TabsList>

          <TabsContent value="javascript" className="mt-6 space-y-8">
            <Card>
              <CardHeader>
                <CardTitle>Verify API Key</CardTitle>
                <CardDescription>
                  Check if an API key is valid and has enough credits
                </CardDescription>
              </CardHeader>
              <CardContent>
                <CodeBlockCode code={verifyApiKeyJs} language="javascript" />
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Consume Credits</CardTitle>
                <CardDescription>
                  Consume credits for an API operation
                </CardDescription>
              </CardHeader>
              <CardContent>
                <CodeBlockCode code={consumeCreditsJs} language="javascript" />
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="python" className="mt-6 space-y-8">
            <Card>
              <CardHeader>
                <CardTitle>Verify API Key</CardTitle>
                <CardDescription>
                  Check if an API key is valid and has enough credits
                </CardDescription>
              </CardHeader>
              <CardContent>
                <CodeBlockCode code={verifyApiKeyPython} language="python" />
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Consume Credits</CardTitle>
                <CardDescription>
                  Consume credits for an API operation
                </CardDescription>
              </CardHeader>
              <CardContent>
                <CodeBlockCode code={consumeCreditsPython} language="python" />
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="go" className="mt-6 space-y-8">
            <Card>
              <CardHeader>
                <CardTitle>Verify API Key</CardTitle>
                <CardDescription>
                  Check if an API key is valid and has enough credits
                </CardDescription>
              </CardHeader>
              <CardContent>
                <CodeBlockCode code={verifyApiKeyGo} language="go" />
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Consume Credits</CardTitle>
                <CardDescription>
                  Consume credits for an API operation
                </CardDescription>
              </CardHeader>
              <CardContent>
                <CodeBlockCode code={consumeCreditsGo} language="go" />
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

        <h2 className="text-2xl font-bold mt-12 mb-6">Additional Features</h2>

        <div className="grid gap-6 md:grid-cols-2">
          <Card>
            <CardHeader>
              <CardTitle>Webhook Events</CardTitle>
              <CardDescription>
                Example webhook payload for credit consumption
              </CardDescription>
            </CardHeader>
            <CardContent>
              <CodeBlockCode code={webhookExample} language="json" />
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Rate Limiting</CardTitle>
              <CardDescription>
                How to handle rate limit responses
              </CardDescription>
            </CardHeader>
            <CardContent>
              <CodeBlockCode code={rateLimitExample} language="json" />
              <p className="mt-4 text-sm text-muted-foreground">
                When you exceed your rate limit, the API will return a 429 status code with a <code>retry_after</code> header indicating how many seconds to wait before retrying.
              </p>
            </CardContent>
          </Card>
        </div>
      </main>
    </div>
  );
}
