# ADC Credit SDK

A JavaScript/TypeScript SDK for interacting with the ADC Credit API.

## Features

- **Complete API Coverage**: Access all ADC Credit API endpoints
- **TypeScript Support**: Full type definitions for all methods and responses
- **Authentication**: Support for both API key and JWT token authentication
- **Error Handling**: Consistent error handling across all API calls
- **Modular Design**: Organized into logical modules for different API functionalities
- **Debug Mode**: Optional verbose logging for debugging API interactions

## Installation

```bash
npm install adc-credit-sdk
```

## Quick Start

```typescript
import ADCCreditSDK from 'adc-credit-sdk';

// Initialize with API key
const sdk = new ADCCreditSDK({
  apiUrl: 'https://api.example.com', // Optional, defaults to http://localhost:8400
  apiKey: 'your-api-key',
  debug: true // Optional, enables verbose logging
});

// Get credit balance
const balanceResponse = await sdk.credits.getBalance();
if (!balanceResponse.error) {
  console.log(`Credit balance: ${balanceResponse.data.credit_balance}`);
}
```

## Authentication

The SDK supports two authentication methods:

### API Key Authentication

```typescript
const sdk = new ADCCreditSDK({
  apiUrl: 'https://api.example.com',
  apiKey: 'your-api-key'
});
```

### JWT Token Authentication

```typescript
const sdk = new ADCCreditSDK({
  apiUrl: 'https://api.example.com',
  token: 'your-jwt-token'
});

// Login and update token
const loginResponse = await sdk.auth.login('<EMAIL>', 'password');
if (!loginResponse.error) {
  sdk.updateConfig({ token: loginResponse.data.token });
}
```

## SDK Modules

The SDK provides the following modules:

- **auth**: Authentication and user registration
- **apiKeys**: API key management
- **credits**: Credit balance and transactions
- **usage**: Usage statistics and analytics
- **webhooks**: Webhook management
- **users**: User management
- **organizations**: Organization and branch management
- **merchant**: Merchant shop and customer management
- **customer**: Customer shop interactions and credit management
- **subscriptions**: Subscription plan management

### Authentication

```typescript
// Login with email and password
const loginResponse = await sdk.auth.login('<EMAIL>', 'password');

// Register a new user
const registerResponse = await sdk.auth.register('<EMAIL>', 'password', 'John Doe');

// Get current user
const userResponse = await sdk.auth.getCurrentUser();

// Update current user
const updateResponse = await sdk.auth.updateCurrentUser({
  name: 'New Name',
  picture: 'https://example.com/avatar.jpg'
});
```

### API Keys

```typescript
// Get all API keys
const apiKeysResponse = await sdk.apiKeys.getAll();

// Create a new API key
const createResponse = await sdk.apiKeys.create({
  name: 'My API Key',
  permissions: ['read', 'write']
});

// Update an API key
const updateResponse = await sdk.apiKeys.update('api-key-id', {
  name: 'Updated API Key',
  enabled: true
});

// Delete an API key
const deleteResponse = await sdk.apiKeys.delete('api-key-id');

// Verify an API key (external API)
const verifyResponse = await sdk.apiKeys.verify('api-key', 10);
```

### Credits

```typescript
// Get credit balance
const balanceResponse = await sdk.credits.getBalance();

// Get transaction history
const transactionsResponse = await sdk.credits.getTransactions();

// Add credits
const addResponse = await sdk.credits.add({
  amount: 100,
  description: 'Manual top-up'
});

// Consume credits (external API)
const consumeResponse = await sdk.credits.consume({
  endpoint: '/api/data',
  method: 'GET',
  credits: 1,
  ip_address: '***********', // Optional
  user_agent: 'MyApp/1.0' // Optional
});
```

### Usage Statistics

```typescript
// Get usage data
const usageResponse = await sdk.usage.getUsage({
  start_date: '2023-01-01',
  end_date: '2023-01-31'
});

// Get usage summary
const summaryResponse = await sdk.usage.getUsageSummary({
  start_date: '2023-01-01',
  end_date: '2023-01-31'
});

// Get analytics summary
const analyticsResponse = await sdk.usage.getAnalyticsSummary({
  start_date: '2023-01-01',
  end_date: '2023-01-31'
});
```

### Webhooks

```typescript
// Get all webhooks
const webhooksResponse = await sdk.webhooks.getAll();

// Create a new webhook
const createResponse = await sdk.webhooks.create({
  name: 'My Webhook',
  url: 'https://example.com/webhook',
  secret: 'webhook-secret',
  events: ['credit.consumed', 'credit.added']
});

// Update a webhook
const updateResponse = await sdk.webhooks.update('webhook-id', {
  name: 'Updated Webhook',
  active: true
});

// Delete a webhook
const deleteResponse = await sdk.webhooks.delete('webhook-id');

// Get webhook deliveries
const deliveriesResponse = await sdk.webhooks.getDeliveries('webhook-id');
```

### Users

```typescript
// Get current user
const userResponse = await sdk.users.getCurrentUser();

// Update current user
const updateResponse = await sdk.users.updateCurrentUser({
  name: 'New Name',
  picture: 'https://example.com/avatar.jpg'
});

// Admin: Get all users
const usersResponse = await sdk.users.getAllUsers();

// Admin: Get a specific user
const userResponse = await sdk.users.getUser('user-id');

// Admin: Update a user
const updateResponse = await sdk.users.updateUser('user-id', {
  name: 'New Name',
  role: 'admin'
});

// Admin: Delete a user
const deleteResponse = await sdk.users.deleteUser('user-id');

// Get external users
const externalUsersResponse = await sdk.users.getExternalUsers({
  organization_id: 'org-id',
  branch_id: 'branch-id'
});

// Create an external user
const createResponse = await sdk.users.createExternalUser({
  organization_id: 'org-id',
  branch_id: 'branch-id',
  name: 'External User',
  email: '<EMAIL>',
  monthly_credits: 100
});

// Add credits to an external user
const addCreditsResponse = await sdk.users.addCreditsToExternalUser(
  'user-id',
  50,
  'Manual credit addition'
);
```

### Organizations

```typescript
// Get all organizations
const orgsResponse = await sdk.organizations.getAll();

// Get a specific organization by slug
const orgResponse = await sdk.organizations.getBySlug('my-organization');

// Create a new organization
const createResponse = await sdk.organizations.create({
  name: 'My Organization',
  description: 'My organization description'
});

// Get all branches
const branchesResponse = await sdk.organizations.getBranches('org-id');

// Create a new branch
const createBranchResponse = await sdk.organizations.createBranch({
  organization_id: 'org-id',
  name: 'My Branch',
  description: 'My branch description'
});
```

### Merchant

```typescript
// Get all merchant shops
const shopsResponse = await sdk.merchant.getShops();

// Create a new shop
const createShopResponse = await sdk.merchant.createShop({
  name: 'My Coffee Shop',
  description: 'The best coffee in town',
  contact_email: '<EMAIL>',
  contact_phone: '+1234567890'
});

// Get shop customers
const customersResponse = await sdk.merchant.getShopCustomers(shopId);

// Add a customer to a shop
const addCustomerResponse = await sdk.merchant.addShopCustomer(shopId, {
  name: 'John Doe',
  email: '<EMAIL>',
  phone: '+1234567890'
});

// Add credit to a customer
const addCreditResponse = await sdk.merchant.addShopCredit(shopId, {
  customer_id: customerId,
  amount: 50,
  description: 'Loyalty bonus'
});

// Generate a credit code
const generateCodeResponse = await sdk.merchant.generateCreditCode(shopId, {
  amount: 25,
  description: 'Promotional code',
  expires_at: '2023-12-31T23:59:59Z'
});

// Create a shop API key
const createApiKeyResponse = await sdk.merchant.createShopApiKey(
  shopId,
  'POS Terminal',
  ['read', 'write']
);
```

### Customer

```typescript
// Get all shops where user is a customer
const shopsResponse = await sdk.customer.getShops();

// Get a specific shop with credit balance
const shopResponse = await sdk.customer.getShop(shopId);

// Redeem a credit code
const redeemResponse = await sdk.customer.redeemCreditCode({
  code: 'ABC123'
});

// Use shop credit
const useResponse = await sdk.customer.useShopCredit(shopId, {
  amount: 10,
  description: 'Coffee purchase'
});

// Get credit transaction history
const transactionsResponse = await sdk.customer.getCreditTransactions(shopId);
```

### Subscriptions

```typescript
// Get all subscription tiers
const tiersResponse = await sdk.subscriptions.getTiers();

// Get active subscription
const activeResponse = await sdk.subscriptions.getActive();

// Create a new subscription
const createResponse = await sdk.subscriptions.create({
  subscription_tier_id: 2,
  auto_renew: true
});

// Cancel a subscription
const cancelResponse = await sdk.subscriptions.cancel(subscriptionId);

// Create a checkout session for subscription payment
const checkoutResponse = await sdk.subscriptions.createCheckoutSession(tierId);
```

## Error Handling

All SDK methods return a consistent response object with the following structure:

```typescript
interface APIResponse<T> {
  data?: T;
  error?: string;
  status: number;
}
```

Example error handling:

```typescript
const response = await sdk.credits.getBalance();

if (response.error) {
  console.error(`Error (${response.status}): ${response.error}`);
} else {
  console.log(`Credit balance: ${response.data.credit_balance}`);
}
```

## Examples

### Node.js Example

See [examples/node-example.js](examples/node-example.js) for a complete Node.js example.

### Browser Example

See [examples/browser-example.html](examples/browser-example.html) for a complete browser example.

## Development

### Building the SDK

```bash
# Install dependencies
npm install

# Build the SDK
npm run build
```

### Running Tests

```bash
npm test
```

## License

MIT
