package main

import (
	"fmt"
	"log"

	"github.com/adc-credit/backend/internal/database"
	"github.com/adc-credit/backend/internal/models"
	"github.com/google/uuid"
	"github.com/joho/godotenv"
)

func main() {
	// Load environment variables
	if err := godotenv.Load(); err != nil {
		log.Println("No .env file found, using environment variables")
	}

	// Initialize database connection
	if err := database.InitDB(); err != nil {
		log.Fatalf("Failed to connect to database: %v", err)
	}

	// Create API key
	apiKey, err := createAPIKey()
	if err != nil {
		log.Fatalf("Failed to create API key: %v", err)
	}

	log.Printf("API key created successfully: %s", apiKey)
}

func createAPIKey() (string, error) {
	// Find the test user
	var user models.User
	if err := database.DB.Where("email = ?", "<EMAIL>").First(&user).Error; err != nil {
		return "", fmt.Errorf("failed to find test user: %w", err)
	}

	// Generate a unique API key
	apiKeyValue := fmt.Sprintf("test_key_%s", uuid.New().String())

	// Create the API key using raw SQL to avoid type issues
	apiKeyID := uuid.New()
	result := database.DB.Exec(`
		INSERT INTO api_keys
		(id, user_id, name, key, permissions, enabled, last_used, created_at, updated_at)
		VALUES
		(?, ?, 'Test API Key', ?, ARRAY['read', 'write', 'admin'], true, NULL, NOW(), NOW())
	`, apiKeyID, user.ID, apiKeyValue)

	if result.Error != nil {
		return "", fmt.Errorf("failed to create API key: %w", result.Error)
	}

	log.Printf("Created API key with ID: %s for user: %s", apiKeyID, user.ID)
	return apiKeyValue, nil
}
