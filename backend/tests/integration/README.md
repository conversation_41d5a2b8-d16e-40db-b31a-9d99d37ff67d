# Subscription Guard Integration Tests

This directory contains comprehensive integration tests for the subscription guard system that enforces subscription limits across the ADC Credit platform.

## 🎯 Overview

The subscription guard system ensures that users can only perform actions within their subscription tier limits. These tests verify that all limits are properly enforced at the API level.

## 📁 Test Files

### Core Test Files

- **`database_test.go`** - Basic database connectivity and model tests
- **`subscription_guard_basic_test.go`** - Basic subscription guard functionality tests
- **`subscription_guard_comprehensive_test.go`** - Comprehensive subscription guard feature tests

### Test Utilities

- **`../internal/testing/setup.go`** - Test database setup and utilities

## 🧪 Test Categories

### 1. Database Tests (`database_test.go`)
Basic database connectivity and model functionality.

**Tests:**
- ✅ Database connection and setup
- ✅ User creation and management
- ✅ Subscription tier creation and validation

### 2. Shop Creation Limits (`subscription_guard_basic_test.go`)
Tests that users cannot create more shops than their subscription allows.

**Scenarios:**
- ✅ Starter plan: 1 shop limit
- ✅ Business plan: 5 shop limit
- ✅ Proper limit enforcement and usage tracking

### 3. Webhook Limits (`subscription_guard_basic_test.go`)
Tests webhook creation limits.

**Scenarios:**
- ✅ Starter plan: 0 webhooks (not allowed)
- ✅ Business plan: 3 webhooks
- ✅ Proper zero-value handling (fixed GORM default issue)

### 4. Shop Type Restrictions (`subscription_guard_basic_test.go`)
Tests that users can only create allowed shop types.

**Scenarios:**
- ✅ Starter plan: Only "retail" shops
- ✅ Business plan: "retail" and "api_service" shops
- ✅ Enterprise plan: All shop types

### 5. QR Code Monthly Limits (`subscription_guard_comprehensive_test.go`)
Tests QR code generation limits on a monthly basis.

**Scenarios:**
- ✅ Starter plan: 100 QR codes per month
- ✅ Business plan: 1,000 QR codes per month
- ✅ Enterprise plan: Unlimited QR codes
- ✅ Proper usage tracking with credit codes

### 6. Credit Balance Checks (`subscription_guard_comprehensive_test.go`)
Tests credit balance validation.

**Scenarios:**
- ✅ Sufficient vs insufficient credit scenarios
- ✅ Dynamic credit balance updates
- ✅ Different credit limits per tier

### 7. Customer Limits per Shop (`subscription_guard_comprehensive_test.go`)
Tests customer addition limits for each shop.

**Scenarios:**
- ✅ Starter plan: 10 customers per shop
- ✅ Business plan: 100 customers per shop
- ✅ Enterprise plan: Unlimited customers (200+ tested)
- ✅ Real user creation for foreign key compliance

### 8. Unlimited Features (`subscription_guard_comprehensive_test.go`)
Tests enterprise plan unlimited features.

**Scenarios:**
- ✅ Unlimited QR codes with proper flag setting
- ✅ High credit balance limits
- ✅ All shop type access

### 9. Feature Restrictions (`subscription_guard_comprehensive_test.go`)
Tests subscription tier feature restrictions.

**Scenarios:**
- ✅ Shop type access control
- ✅ Proper restriction enforcement

### ⏸️ Temporarily Disabled Tests
- **API Key Limits per Shop**: Disabled due to Shop model foreign key constraints
- **Branch Limits per Shop**: Disabled due to Shop model foreign key constraints

## 🚀 Running Tests

### Prerequisites

1. **PostgreSQL Database** - Running locally or accessible
2. **Go 1.21+** - For running backend tests
3. **Environment Variables** - Properly configured

### Quick Start

```bash
# Run all integration tests
cd backend
go test -v ./tests/integration/...

# Run specific test files
go test -v ./tests/integration/database_test.go
go test -v ./tests/integration/subscription_guard_basic_test.go
go test -v ./tests/integration/subscription_guard_comprehensive_test.go

# Run both subscription guard test suites
go test -v ./tests/integration/subscription_guard_basic_test.go ./tests/integration/subscription_guard_comprehensive_test.go
```

### Manual Test Execution

```bash
# Run individual test methods
go test -v ./tests/integration/subscription_guard_comprehensive_test.go -run TestComprehensiveSubscriptionGuardTestSuite/TestQRCodeLimits

go test -v ./tests/integration/subscription_guard_basic_test.go -run TestBasicSubscriptionGuardTestSuite/TestWebhookLimits

# Run with coverage
go test -coverprofile=coverage.out ./tests/integration/...
go tool cover -html=coverage.out -o coverage.html
```

## 📊 Test Data

### Subscription Tiers Used in Tests

| Tier | Price | Shops | Customers/Shop | QR Codes/Month | Webhooks | Credit Limit |
|------|-------|-------|----------------|----------------|----------|--------------|
| **Starter** | $0 | 1 | 10 | 100 | 0 | 1,000 |
| **Business** | $49 | 3 | 100 | 1,000 | 3 | 10,000 |
| **Enterprise** | $199 | ∞ | ∞ | ∞ | 10 | 100,000 |

**Note**: API Key and Branch limits are temporarily disabled due to Shop model foreign key constraints.

### Test Users

Each test creates isolated users with specific subscription tiers to avoid conflicts.

## 🔧 Configuration

### Environment Variables

```bash
# Test Database
DB_NAME=adc_credit_test
DB_HOST=localhost
DB_PORT=5432
DB_USER=postgres
DB_PASSWORD=your_password

# Test Mode
GIN_MODE=test
```

### Test Database Setup

The tests automatically:
1. Create a test database
2. Run migrations
3. Seed subscription tiers
4. Clean up after each test

## 📈 Coverage Goals

- **Overall Coverage**: >90%
- **Subscription Guard Service**: >95%
- **API Endpoints**: >90%
- **Middleware**: >85%

## 🐛 Debugging Tests

### Common Issues

1. **Database Connection Errors**
   ```bash
   # Check PostgreSQL is running
   pg_isready -h localhost -p 5432
   
   # Verify test database exists
   psql -h localhost -U postgres -l | grep adc_credit_test
   ```

2. **Test Data Conflicts**
   ```bash
   # Clean test database
   make clean-test-db
   make setup-test-db
   ```

3. **Environment Variable Issues**
   ```bash
   # Check test environment file
   cat backend/.env.test
   ```

### Verbose Test Output

```bash
# Run with verbose output
go test -v -run TestSpecificTest ./tests/integration/...

# Run single test method
go test -v -run TestShopCreationLimits ./tests/integration/subscription_guard_test.go
```

## 🔄 Continuous Integration

### GitHub Actions Integration

```yaml
name: Integration Tests
on: [push, pull_request]
jobs:
  test:
    runs-on: ubuntu-latest
    services:
      postgres:
        image: postgres:13
        env:
          POSTGRES_PASSWORD: postgres
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
    steps:
      - uses: actions/checkout@v2
      - uses: actions/setup-go@v2
        with:
          go-version: 1.21
      - name: Run Integration Tests
        run: make test-subscription-guard
```

## 📝 Adding New Tests

### Test Structure

```go
func (suite *TestSuite) TestNewFeature() {
    // 1. Setup test data
    tier := suite.createTestSubscriptionTier(...)
    user, _, token := suite.createUserWithSubscription(...)
    
    // 2. Perform action that should be limited
    // ... make API request
    
    // 3. Assert expected behavior
    suite.Equal(expectedStatusCode, actualStatusCode)
    suite.Contains(response, expectedMessage)
    
    // 4. Cleanup
    testutils.CleanupTestUser(suite.T(), user.ID)
}
```

### Best Practices

1. **Isolation** - Each test should be independent
2. **Cleanup** - Always clean up test data
3. **Descriptive Names** - Test names should describe the scenario
4. **Edge Cases** - Test boundary conditions
5. **Error Messages** - Verify user-friendly error messages

## 🎯 Future Enhancements

- [ ] Performance tests for high-volume scenarios
- [ ] Concurrent access tests
- [ ] Rate limiting integration tests
- [ ] Subscription upgrade/downgrade tests
- [ ] Credit balance enforcement tests
- [ ] Analytics access control tests

## 📞 Support

For questions about the integration tests:

1. Check the test output for specific error messages
2. Review the test database setup
3. Verify environment configuration
4. Check the main README for general setup instructions
