"use client";

import { useState, useEffect } from "react";
import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Skeleton } from "@/components/ui/skeleton";
import { useToast } from "@/components/ui/use-toast";
import { MerchantBreadcrumbs } from "@/components/navigation/breadcrumbs";
import { MerchantPageHeader } from "@/components/layout/page-header-mobile";
import {
  useGetCurrentUserQuery,
  useUpdateUserMutation,
  useGetMerchantShopsQuery
} from "@/lib/api/apiSlice";
import {
  User,
  Storefront,
  Bell,
  Shield,
  CreditCard,
  Gear,
  EnvelopeSimple,
  Phone
} from "phosphor-react";

export default function MerchantSettingsPage() {
  const { update } = useSession();
  const { toast } = useToast();
  const router = useRouter();

  // Form state
  const [profileForm, setProfileForm] = useState({
    name: "",
    picture: "",
  });

  const [preferencesForm, setPreferencesForm] = useState({
    emailNotifications: true,
    pushNotifications: false,
    defaultShopId: "",
  });

  // Fetch user data
  const {
    data: user,
    isLoading: isLoadingUser,
    error: userError
  } = useGetCurrentUserQuery();

  // Fetch merchant shops
  const {
    data: shops,
    isLoading: isLoadingShops
  } = useGetMerchantShopsQuery();

  // Update user mutation
  const [updateUserMutation, { isLoading: isUpdating }] = useUpdateUserMutation();

  // Initialize form data when user data is loaded
  useEffect(() => {
    if (user) {
      setProfileForm({
        name: user.name || "",
        picture: user.picture || "",
      });

      // In a real implementation, these would come from user preferences in the API
      setPreferencesForm({
        emailNotifications: true,
        pushNotifications: false,
        defaultShopId: shops && shops.length > 0 ? shops[0].id : "",
      });
    }
  }, [user, shops]);

  // Show error toast if user fetch fails
  useEffect(() => {
    if (userError) {
      toast({
        title: "Error",
        description: "Failed to load user data",
        variant: "destructive",
      });
      console.error("Error fetching user data:", userError);
    }
  }, [userError, toast]);

  // Handle profile form input change
  const handleProfileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setProfileForm((prev) => ({ ...prev, [name]: value }));
  };

  // Handle preferences form input change
  const handlePreferencesChange = (field: string, value: any) => {
    setPreferencesForm((prev) => ({ ...prev, [field]: value }));
  };

  // Handle profile form submission
  const handleProfileSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    try {
      const updatedUser = await updateUserMutation(profileForm).unwrap();

      // Update the session with the new user data
      await update({
        user: {
          name: updatedUser.name,
          picture: updatedUser.picture,
        },
      });

      toast({
        title: "Success",
        description: "Profile updated successfully",
      });
    } catch (error) {
      console.error("Error updating user:", error);
      toast({
        title: "Error",
        description: "Failed to update profile",
        variant: "destructive",
      });
    }
  };

  // Handle preferences form submission
  const handlePreferencesSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // In a real implementation, this would call an API to update user preferences
    toast({
      title: "Success",
      description: "Preferences updated successfully",
    });
  };

  // Breadcrumb segments
  const breadcrumbSegments = [
    { label: "Dashboard", href: "/dashboard/merchant" },
    { label: "Settings", href: "/dashboard/merchant/settings" },
  ];

  return (
    <div className="space-y-6">
      {/* Breadcrumbs - Desktop only */}
      <div className="hidden md:block">
        <MerchantBreadcrumbs segments={breadcrumbSegments} />
      </div>

      {/* Mobile Header */}
      <div className="md:hidden">
        <MerchantPageHeader
          title="Settings"
          backHref="/dashboard/merchant"
        />
      </div>

      {/* Page Header - Desktop only */}
      <div className="hidden md:block">
        <h1 className="text-2xl font-bold tracking-tight md:text-3xl text-[#181510]">
          Settings
        </h1>
        <p className="text-[#8a745c] mt-1">
          Manage your account and preferences
        </p>
      </div>

      {/* Settings Tabs */}
      <Tabs defaultValue="profile" className="w-full">
        <TabsList className="w-full justify-start mb-6 bg-transparent p-0 h-auto overflow-x-auto flex-nowrap">
          <TabsTrigger
            value="profile"
            className="data-[state=active]:bg-[#e5ccb2] data-[state=active]:text-[#181510] rounded-lg mr-2 whitespace-nowrap"
          >
            <User className="mr-2 h-5 w-5" />
            Profile
          </TabsTrigger>
          <TabsTrigger
            value="preferences"
            className="data-[state=active]:bg-[#e5ccb2] data-[state=active]:text-[#181510] rounded-lg mr-2 whitespace-nowrap"
          >
            <Gear className="mr-2 h-5 w-5" />
            Preferences
          </TabsTrigger>
          <TabsTrigger
            value="account"
            className="data-[state=active]:bg-[#e5ccb2] data-[state=active]:text-[#181510] rounded-lg mr-2 whitespace-nowrap"
          >
            <Storefront className="mr-2 h-5 w-5" />
            Account
          </TabsTrigger>
          <TabsTrigger
            value="security"
            className="data-[state=active]:bg-[#e5ccb2] data-[state=active]:text-[#181510] rounded-lg whitespace-nowrap"
          >
            <Shield className="mr-2 h-5 w-5" />
            Security
          </TabsTrigger>
        </TabsList>

        {/* Profile Tab */}
        <TabsContent value="profile" className="space-y-6">
          <Card>
            <form onSubmit={handleProfileSubmit}>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <User className="mr-2 h-5 w-5" />
                  Profile Information
                </CardTitle>
                <CardDescription>
                  Update your personal information
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                {/* Profile Picture */}
                <div className="flex flex-col sm:flex-row items-center gap-4">
                  {isLoadingUser ? (
                    <Skeleton className="h-20 w-20 rounded-full" />
                  ) : (
                    <Avatar className="h-20 w-20">
                      <AvatarImage src={profileForm.picture} alt={profileForm.name} />
                      <AvatarFallback className="bg-[#e5ccb2] text-[#181510]">
                        {profileForm.name?.charAt(0) || "U"}
                      </AvatarFallback>
                    </Avatar>
                  )}
                  <div className="flex-1 space-y-2">
                    <Label htmlFor="picture">Profile Picture URL</Label>
                    <Input
                      id="picture"
                      name="picture"
                      value={profileForm.picture}
                      onChange={handleProfileInputChange}
                      placeholder="https://example.com/your-image.jpg"
                      disabled={isLoadingUser || isUpdating}
                    />
                    <p className="text-xs text-[#8a745c]">
                      Enter a URL for your profile picture
                    </p>
                  </div>
                </div>

                {/* Name */}
                <div className="space-y-2">
                  <Label htmlFor="name">Full Name</Label>
                  <Input
                    id="name"
                    name="name"
                    value={profileForm.name}
                    onChange={handleProfileInputChange}
                    placeholder="Your Name"
                    disabled={isLoadingUser || isUpdating}
                  />
                </div>

                {/* Email - Read Only */}
                <div className="space-y-2">
                  <Label htmlFor="email">Email Address</Label>
                  <Input
                    id="email"
                    value={user?.email || ""}
                    disabled
                    className="bg-[#f1edea]"
                  />
                  <p className="text-xs text-[#8a745c]">
                    Your email address cannot be changed
                  </p>
                </div>
              </CardContent>
              <CardFooter>
                <Button
                  type="submit"
                  disabled={isLoadingUser || isUpdating}
                  className="bg-[#e5ccb2] text-[#181510] hover:bg-[#d9b99a]"
                >
                  {isUpdating ? "Saving..." : "Save Changes"}
                </Button>
              </CardFooter>
            </form>
          </Card>
        </TabsContent>

        {/* Preferences Tab */}
        <TabsContent value="preferences" className="space-y-6">
          <Card>
            <form onSubmit={handlePreferencesSubmit}>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Gear className="mr-2 h-5 w-5" />
                  Merchant Preferences
                </CardTitle>
                <CardDescription>
                  Customize your merchant experience
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                {/* Default Shop */}
                <div className="space-y-2">
                  <Label htmlFor="defaultShop">Default Shop</Label>
                  <select
                    id="defaultShop"
                    value={preferencesForm.defaultShopId}
                    onChange={(e) => handlePreferencesChange("defaultShopId", e.target.value)}
                    className="w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                    disabled={isLoadingShops}
                  >
                    <option value="">Select a default shop</option>
                    {shops?.map((shop) => (
                      <option key={shop.id} value={shop.id}>
                        {shop.name}
                      </option>
                    ))}
                  </select>
                  <p className="text-xs text-[#8a745c]">
                    This shop will be selected by default when you log in
                  </p>
                </div>

                {/* Notification Preferences */}
                <div className="space-y-4">
                  <h3 className="text-sm font-medium flex items-center">
                    <Bell className="mr-2 h-4 w-4" />
                    Notification Preferences
                  </h3>

                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label htmlFor="emailNotifications">Email Notifications</Label>
                      <p className="text-xs text-[#8a745c]">
                        Receive notifications about transactions and customer activity
                      </p>
                    </div>
                    <Switch
                      id="emailNotifications"
                      checked={preferencesForm.emailNotifications}
                      onCheckedChange={(checked) => handlePreferencesChange("emailNotifications", checked)}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label htmlFor="pushNotifications">Push Notifications</Label>
                      <p className="text-xs text-[#8a745c]">
                        Receive push notifications on your device
                      </p>
                    </div>
                    <Switch
                      id="pushNotifications"
                      checked={preferencesForm.pushNotifications}
                      onCheckedChange={(checked) => handlePreferencesChange("pushNotifications", checked)}
                    />
                  </div>
                </div>

                {/* Contact Information */}
                <div className="space-y-4">
                  <h3 className="text-sm font-medium flex items-center">
                    <EnvelopeSimple className="mr-2 h-4 w-4" />
                    Contact Information
                  </h3>

                  <div className="space-y-2">
                    <Label htmlFor="contactEmail">Contact Email</Label>
                    <Input
                      id="contactEmail"
                      placeholder="<EMAIL>"
                      defaultValue={user?.email || ""}
                    />
                    <p className="text-xs text-[#8a745c]">
                      Email used for customer inquiries (different from account email)
                    </p>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="contactPhone">Contact Phone</Label>
                    <Input
                      id="contactPhone"
                      placeholder="+****************"
                    />
                  </div>
                </div>
              </CardContent>
              <CardFooter>
                <Button
                  type="submit"
                  className="bg-[#e5ccb2] text-[#181510] hover:bg-[#d9b99a]"
                >
                  Save Preferences
                </Button>
              </CardFooter>
            </form>
          </Card>
        </TabsContent>

        {/* Account Tab */}
        <TabsContent value="account" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Storefront className="mr-2 h-5 w-5" />
                Account Information
              </CardTitle>
              <CardDescription>
                Your merchant account details
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {isLoadingUser ? (
                <div className="space-y-4">
                  <Skeleton className="h-5 w-full" />
                  <Skeleton className="h-5 w-3/4" />
                  <Skeleton className="h-5 w-1/2" />
                </div>
              ) : (
                <>
                  <div className="space-y-1">
                    <p className="text-sm font-medium">Account ID</p>
                    <p className="text-sm font-mono text-[#8a745c]">
                      {user?.id}
                    </p>
                  </div>
                  <div className="space-y-1">
                    <p className="text-sm font-medium">Account Type</p>
                    <p className="text-sm text-[#8a745c]">
                      {user?.role === "admin" ? "Administrator" : "Merchant"}
                    </p>
                  </div>
                  <div className="space-y-1">
                    <p className="text-sm font-medium">Account Created</p>
                    <p className="text-sm text-[#8a745c]">
                      {/* This would come from the API in a real implementation */}
                      {new Date().toLocaleDateString()}
                    </p>
                  </div>
                </>
              )}
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <CreditCard className="mr-2 h-5 w-5" />
                Merchant Shops
              </CardTitle>
              <CardDescription>
                Shops associated with your account
              </CardDescription>
            </CardHeader>
            <CardContent>
              {isLoadingShops ? (
                <div className="space-y-4">
                  <Skeleton className="h-12 w-full" />
                  <Skeleton className="h-12 w-full" />
                </div>
              ) : shops && shops.length > 0 ? (
                <div className="space-y-3">
                  {shops.map((shop) => (
                    <div key={shop.id} className="flex items-center justify-between p-3 bg-[#f1edea] rounded-lg">
                      <div className="flex items-center">
                        <div className="bg-[#e5ccb2] rounded-full p-2 mr-3">
                          <Storefront size={20} className="text-[#181510]" />
                        </div>
                        <div>
                          <p className="font-medium text-[#181510]">{shop.name}</p>
                          <p className="text-xs text-[#8a745c]">{shop.contact_email}</p>
                        </div>
                      </div>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => router.push(`/dashboard/merchant/shop/${shop.slug}`)}
                      >
                        View
                      </Button>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-6">
                  <p className="text-[#8a745c]">No shops found</p>
                  <Button
                    className="mt-4 bg-[#e5ccb2] text-[#181510] hover:bg-[#d9b99a]"
                    onClick={() => router.push('/dashboard/merchant/new')}
                  >
                    Create a Shop
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Security Tab */}
        <TabsContent value="security" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Shield className="mr-2 h-5 w-5" />
                Security Settings
              </CardTitle>
              <CardDescription>
                Manage your account security
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-1">
                <p className="text-sm font-medium">Authentication Method</p>
                <p className="text-sm text-[#8a745c]">
                  Google OAuth
                </p>
              </div>
              <div className="space-y-1">
                <p className="text-sm font-medium">Last Sign In</p>
                <p className="text-sm text-[#8a745c]">
                  {new Date().toLocaleDateString()} {new Date().toLocaleTimeString()}
                </p>
              </div>
              <div className="space-y-1">
                <p className="text-sm font-medium">Two-Factor Authentication</p>
                <p className="text-sm text-[#8a745c]">
                  Not enabled
                </p>
              </div>
            </CardContent>
            <CardFooter className="flex flex-col sm:flex-row gap-3 items-start sm:items-center">
              <Button
                variant="outline"
                className="w-full sm:w-auto"
                disabled
              >
                Change Password
              </Button>
              <Button
                variant="outline"
                className="w-full sm:w-auto"
                disabled
              >
                Enable Two-Factor Authentication
              </Button>
            </CardFooter>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center text-red-500">
                <Shield className="mr-2 h-5 w-5" />
                Danger Zone
              </CardTitle>
              <CardDescription>
                Irreversible account actions
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-[#8a745c] mb-4">
                These actions cannot be undone. Please be certain.
              </p>
              <div className="space-y-3">
                <Button
                  variant="outline"
                  className="w-full sm:w-auto border-red-200 text-red-500 hover:bg-red-50"
                  disabled
                >
                  Delete Account
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
