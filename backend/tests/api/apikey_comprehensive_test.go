package api

import (
	"net/http"
	"testing"

	testutils "github.com/adc-credit/backend/internal/testing"
	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/suite"
)

type APIKeyComprehensiveTestSuite struct {
	suite.Suite
	router *gin.Engine
}

func (suite *APIKeyComprehensiveTestSuite) SetupSuite() {
	// Setup test environment
	err := testutils.SetupTestEnvironment()
	suite.Require().NoError(err)

	// Setup router with API key routes
	suite.router = gin.New()
	suite.router.Use(gin.Recovery())

	suite.setupAPIKeyRoutes()
}

func (suite *APIKeyComprehensiveTestSuite) TearDownSuite() {
	testutils.CleanupTestEnvironment()
}

func (suite *APIKeyComprehensiveTestSuite) SetupTest() {
	testutils.CleanTestData()
}

func (suite *APIKeyComprehensiveTestSuite) setupAPIKeyRoutes() {
	// Public routes for external API key validation
	public := suite.router.Group("/api/v1")
	{
		external := public.Group("/external")
		{
			external.POST("/verify", func(c *gin.Context) {
				apiKey := c.GetHeader("X-API-Key")
				if apiKey == "" {
					c.JSON(http.StatusUnauthorized, gin.H{"error": "API key required"})
					return
				}
				if apiKey == "valid-api-key" {
					c.JSON(http.StatusOK, gin.H{
						"valid":       true,
						"user_id":     "user-123",
						"permissions": []string{"read", "write"},
					})
				} else {
					c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid API key"})
				}
			})

			external.POST("/consume", func(c *gin.Context) {
				apiKey := c.GetHeader("X-API-Key")
				if apiKey == "" {
					c.JSON(http.StatusUnauthorized, gin.H{"error": "API key required"})
					return
				}
				if apiKey != "valid-api-key" {
					c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid API key"})
					return
				}

				var consumeData map[string]interface{}
				if err := c.ShouldBindJSON(&consumeData); err != nil {
					c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
					return
				}

				credits, ok := consumeData["credits"].(float64)
				if !ok || credits <= 0 {
					c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid credits amount"})
					return
				}

				c.JSON(http.StatusOK, gin.H{
					"message":           "Credits consumed successfully",
					"credits_consumed":  credits,
					"remaining_credits": 1000 - credits,
				})
			})
		}
	}

	// Protected routes for API key management
	protected := suite.router.Group("/api/v1")
	protected.Use(suite.mockAuthMiddleware())
	{
		apiKeys := protected.Group("/apikeys")
		{
			apiKeys.GET("", func(c *gin.Context) {
				c.JSON(http.StatusOK, gin.H{
					"data": []gin.H{
						{
							"id":          "key-1",
							"name":        "Production API Key",
							"enabled":     true,
							"permissions": []string{"read", "write"},
							"created_at":  "2023-01-01T00:00:00Z",
							"last_used":   "2023-01-15T12:00:00Z",
						},
						{
							"id":          "key-2",
							"name":        "Development API Key",
							"enabled":     false,
							"permissions": []string{"read"},
							"created_at":  "2023-01-02T00:00:00Z",
							"last_used":   nil,
						},
					},
					"total": 2,
					"page":  1,
					"limit": 10,
				})
			})

			apiKeys.POST("", func(c *gin.Context) {
				var createData map[string]interface{}
				if err := c.ShouldBindJSON(&createData); err != nil {
					c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
					return
				}

				name, ok := createData["name"].(string)
				if !ok || name == "" {
					c.JSON(http.StatusBadRequest, gin.H{"error": "Name is required"})
					return
				}

				c.JSON(http.StatusCreated, gin.H{
					"id":          "new-key-id",
					"name":        name,
					"key":         "ak_test_1234567890abcdef",
					"enabled":     true,
					"permissions": createData["permissions"],
					"created_at":  "2023-01-01T00:00:00Z",
				})
			})

			apiKeys.GET("/:id", func(c *gin.Context) {
				id := c.Param("id")
				if id == "nonexistent" {
					c.JSON(http.StatusNotFound, gin.H{"error": "API key not found"})
					return
				}

				c.JSON(http.StatusOK, gin.H{
					"id":          id,
					"name":        "Test API Key",
					"enabled":     true,
					"permissions": []string{"read", "write"},
					"created_at":  "2023-01-01T00:00:00Z",
					"last_used":   "2023-01-15T12:00:00Z",
					"usage_stats": gin.H{
						"total_requests":   1500,
						"credits_consumed": 750,
						"last_30_days":     300,
					},
				})
			})

			apiKeys.PUT("/:id", func(c *gin.Context) {
				id := c.Param("id")
				if id == "nonexistent" {
					c.JSON(http.StatusNotFound, gin.H{"error": "API key not found"})
					return
				}

				var updateData map[string]interface{}
				if err := c.ShouldBindJSON(&updateData); err != nil {
					c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
					return
				}

				c.JSON(http.StatusOK, gin.H{
					"id":             id,
					"message":        "API key updated successfully",
					"updated_fields": updateData,
				})
			})

			apiKeys.DELETE("/:id", func(c *gin.Context) {
				id := c.Param("id")
				if id == "nonexistent" {
					c.JSON(http.StatusNotFound, gin.H{"error": "API key not found"})
					return
				}

				c.JSON(http.StatusNoContent, nil)
			})

			// API key regeneration
			apiKeys.POST("/:id/regenerate", func(c *gin.Context) {
				id := c.Param("id")
				if id == "nonexistent" {
					c.JSON(http.StatusNotFound, gin.H{"error": "API key not found"})
					return
				}

				c.JSON(http.StatusOK, gin.H{
					"id":      id,
					"message": "API key regenerated successfully",
					"new_key": "ak_test_new_1234567890abcdef",
				})
			})

			// API key usage statistics
			apiKeys.GET("/:id/usage", func(c *gin.Context) {
				id := c.Param("id")
				if id == "nonexistent" {
					c.JSON(http.StatusNotFound, gin.H{"error": "API key not found"})
					return
				}

				c.JSON(http.StatusOK, gin.H{
					"api_key_id": id,
					"usage_stats": gin.H{
						"total_requests":    1500,
						"credits_consumed":  750,
						"success_rate":      98.5,
						"avg_response_time": 150,
						"last_30_days": gin.H{
							"requests": 300,
							"credits":  150,
						},
						"top_endpoints": []gin.H{
							{"endpoint": "/api/v1/data", "requests": 800, "credits": 400},
							{"endpoint": "/api/v1/search", "requests": 500, "credits": 250},
						},
					},
				})
			})

			// API key permissions management
			apiKeys.PUT("/:id/permissions", func(c *gin.Context) {
				id := c.Param("id")
				if id == "nonexistent" {
					c.JSON(http.StatusNotFound, gin.H{"error": "API key not found"})
					return
				}

				var permData map[string]interface{}
				if err := c.ShouldBindJSON(&permData); err != nil {
					c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
					return
				}

				c.JSON(http.StatusOK, gin.H{
					"id":          id,
					"message":     "Permissions updated successfully",
					"permissions": permData["permissions"],
				})
			})
		}
	}
}

// Mock authentication middleware
func (suite *APIKeyComprehensiveTestSuite) mockAuthMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		auth := c.GetHeader("Authorization")
		if auth == "" {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "Authorization required"})
			c.Abort()
			return
		}

		mockUser := gin.H{
			"id":    "user-123",
			"email": "<EMAIL>",
			"name":  "Test User",
			"role":  "user",
		}
		c.Set("user", mockUser)
		c.Next()
	}
}

// ============================================================================
// EXTERNAL API KEY VALIDATION TESTS
// ============================================================================

func (suite *APIKeyComprehensiveTestSuite) TestExternalAPIKeyVerification() {
	t := suite.T()

	// Test valid API key
	req := testutils.TestRequest{
		Method: "POST",
		URL:    "/api/v1/external/verify",
		Headers: map[string]string{
			"X-API-Key": "valid-api-key",
		},
	}

	resp := testutils.MakeRequest(t, suite.router, req)

	testutils.AssertSuccessResponse(t, resp)
	assert.Equal(t, 200, resp.StatusCode)
	testutils.AssertJSONResponse(t, resp, map[string]interface{}{
		"valid":   true,
		"user_id": "user-123",
	})
	assert.Contains(t, resp.Body, "permissions")
}

func (suite *APIKeyComprehensiveTestSuite) TestExternalAPIKeyVerificationInvalid() {
	t := suite.T()

	// Test invalid API key
	req := testutils.TestRequest{
		Method: "POST",
		URL:    "/api/v1/external/verify",
		Headers: map[string]string{
			"X-API-Key": "invalid-api-key",
		},
	}

	resp := testutils.MakeRequest(t, suite.router, req)

	testutils.AssertErrorResponse(t, resp, 401)
}

func (suite *APIKeyComprehensiveTestSuite) TestExternalAPIKeyVerificationMissing() {
	t := suite.T()

	// Test missing API key
	req := testutils.TestRequest{
		Method: "POST",
		URL:    "/api/v1/external/verify",
	}

	resp := testutils.MakeRequest(t, suite.router, req)

	testutils.AssertErrorResponse(t, resp, 401)
}

func (suite *APIKeyComprehensiveTestSuite) TestExternalCreditConsumption() {
	t := suite.T()

	// Test valid credit consumption
	consumeData := map[string]interface{}{
		"credits":     10,
		"endpoint":    "/api/v1/test",
		"method":      "GET",
		"description": "Test API call",
	}

	req := testutils.TestRequest{
		Method: "POST",
		URL:    "/api/v1/external/consume",
		Headers: map[string]string{
			"X-API-Key": "valid-api-key",
		},
		Body: consumeData,
	}

	resp := testutils.MakeRequest(t, suite.router, req)

	testutils.AssertSuccessResponse(t, resp)
	assert.Equal(t, 200, resp.StatusCode)
	testutils.AssertJSONResponse(t, resp, map[string]interface{}{
		"credits_consumed":  float64(10),
		"remaining_credits": float64(990),
	})
}

func (suite *APIKeyComprehensiveTestSuite) TestExternalCreditConsumptionInvalidKey() {
	t := suite.T()

	consumeData := map[string]interface{}{
		"credits": 10,
	}

	req := testutils.TestRequest{
		Method: "POST",
		URL:    "/api/v1/external/consume",
		Headers: map[string]string{
			"X-API-Key": "invalid-api-key",
		},
		Body: consumeData,
	}

	resp := testutils.MakeRequest(t, suite.router, req)

	testutils.AssertErrorResponse(t, resp, 401)
}

func (suite *APIKeyComprehensiveTestSuite) TestExternalCreditConsumptionInvalidCredits() {
	t := suite.T()

	// Test with negative credits
	consumeData := map[string]interface{}{
		"credits": -10,
	}

	req := testutils.TestRequest{
		Method: "POST",
		URL:    "/api/v1/external/consume",
		Headers: map[string]string{
			"X-API-Key": "valid-api-key",
		},
		Body: consumeData,
	}

	resp := testutils.MakeRequest(t, suite.router, req)

	testutils.AssertErrorResponse(t, resp, 400)
}

// Run the test suite
func TestAPIKeyComprehensiveTestSuite(t *testing.T) {
	suite.Run(t, new(APIKeyComprehensiveTestSuite))
}
