/**
 * ADC Credit SDK Subscription Module
 */
import { ApiClient } from './client';
import { APIResponse } from './types';
import { Subscription, SubscriptionTier } from '@/types';

/**
 * Create subscription request
 */
export interface CreateSubscriptionRequest {
  subscription_tier_id: number;
  auto_renew?: boolean;
}

/**
 * Update subscription request
 */
export interface UpdateSubscriptionRequest {
  auto_renew?: boolean;
}

/**
 * Subscription module for the ADC Credit SDK
 */
export class Subscriptions {
  private client: ApiClient;

  /**
   * Creates a new Subscriptions instance
   * @param client API client
   */
  constructor(client: ApiClient) {
    this.client = client;
  }

  /**
   * Gets all available subscription tiers
   * @returns List of subscription tiers
   */
  public async getTiers(): Promise<APIResponse<SubscriptionTier[]>> {
    return this.client.get<SubscriptionTier[]>('/subscriptions/tiers');
  }

  /**
   * Gets a specific subscription tier
   * @param id Tier ID
   * @returns Subscription tier details
   */
  public async getTier(id: number): Promise<APIResponse<SubscriptionTier>> {
    return this.client.get<SubscriptionTier>(`/subscriptions/tiers/${id}`);
  }

  /**
   * Gets all subscriptions for the current user
   * @returns List of subscriptions
   */
  public async getAll(): Promise<APIResponse<Subscription[]>> {
    return this.client.get<Subscription[]>('/subscriptions');
  }

  /**
   * Gets a specific subscription
   * @param id Subscription ID
   * @returns Subscription details
   */
  public async get(id: string): Promise<APIResponse<Subscription>> {
    return this.client.get<Subscription>(`/subscriptions/${id}`);
  }

  /**
   * Gets the current active subscription
   * @returns Active subscription details
   */
  public async getActive(): Promise<APIResponse<Subscription>> {
    return this.client.get<Subscription>('/subscriptions/active');
  }

  /**
   * Creates a new subscription
   * @param data Subscription data
   * @returns Created subscription
   */
  public async create(data: CreateSubscriptionRequest): Promise<APIResponse<Subscription>> {
    return this.client.post<Subscription>('/subscriptions', data);
  }

  /**
   * Updates a subscription
   * @param id Subscription ID
   * @param data Subscription data to update
   * @returns Updated subscription
   */
  public async update(id: string, data: UpdateSubscriptionRequest): Promise<APIResponse<Subscription>> {
    return this.client.put<Subscription>(`/subscriptions/${id}`, data);
  }

  /**
   * Cancels a subscription
   * @param id Subscription ID
   * @returns Cancelled subscription
   */
  public async cancel(id: string): Promise<APIResponse<Subscription>> {
    return this.client.post<Subscription>(`/subscriptions/${id}/cancel`, {});
  }

  /**
   * Creates a checkout session for a subscription
   * @param tierId Subscription tier ID
   * @returns Checkout session URL
   */
  public async createCheckoutSession(tierId: number): Promise<APIResponse<{ checkout_url: string }>> {
    return this.client.post<{ checkout_url: string }>('/subscriptions/checkout', { tier_id: tierId });
  }

  /**
   * Gets the subscription plan for a merchant shop
   * @param shopId Shop ID
   * @returns Subscription plan details
   */
  public async getMerchantPlan(shopId: string): Promise<APIResponse<{
    subscription: Subscription;
    tier: SubscriptionTier;
    features: string[];
  }>> {
    return this.client.get<{
      subscription: Subscription;
      tier: SubscriptionTier;
      features: string[];
    }>(`/merchant/shops/${shopId}/subscription`);
  }

  /**
   * Updates the subscription plan for a merchant shop
   * @param shopId Shop ID
   * @param tierId Subscription tier ID
   * @returns Updated subscription
   */
  public async updateMerchantPlan(shopId: string, tierId: number): Promise<APIResponse<Subscription>> {
    return this.client.post<Subscription>(`/merchant/shops/${shopId}/subscription`, { tier_id: tierId });
  }
}
