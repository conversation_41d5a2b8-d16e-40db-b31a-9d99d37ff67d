#!/bin/bash

# Load environment variables from .env file
if [ -f .env ]; then
  export $(grep -v '^#' .env | xargs)
fi

# Check if PROJECT_ID is set
if [ -z "$PROJECT_ID" ]; then
  echo "Error: PROJECT_ID environment variable is required"
  echo "Please set it in your .env file or pass it as an environment variable"
  exit 1
fi

echo "Deploying backend and scheduled credits to GCP..."
echo "Project ID: $PROJECT_ID"
echo "Region: ${REGION:-us-central1}"

# Deploy backend using Cloud Build
gcloud builds submit --config=cloudbuild.yaml \
  --substitutions=_REGION="${REGION:-us-central1}",\
_DATABASE_URL="$DATABASE_URL",\
_JWT_SECRET="$JWT_SECRET",\
_JWT_REFRESH_SECRET="$JWT_REFRESH_SECRET",\
_GOOGLE_CLIENT_ID="$GOOGLE_CLIENT_ID",\
_GOOGLE_CLIENT_SECRET="$GOOGLE_CLIENT_SECRET",\
_FRONTEND_URL="$FRONTEND_URL",\
_SCHEDULER_API_KEY="$SCHEDULER_API_KEY",\
_STRIPE_SECRET_KEY="$STRIPE_SECRET_KEY",\
_STRIPE_WEBHOOK_SECRET="$STRIPE_WEBHOOK_SECRET"

# Check if backend deployment was successful
if [ $? -eq 0 ]; then
  echo "Backend deployment completed successfully!"

  # Now provide instructions for frontend deployment
  echo ""
  echo "To deploy the frontend to Cloud Run, follow these steps:"
  echo ""
  echo "1. Build the frontend for production:"
  echo "   bun run build"
  echo ""
  echo "2. Create a Dockerfile for the frontend if not already present:"
  echo "   echo 'FROM node:18-alpine' > Dockerfile"
  echo "   echo 'WORKDIR /app' >> Dockerfile"
  echo "   echo 'COPY . .' >> Dockerfile"
  echo "   echo 'RUN npm install -g bun' >> Dockerfile"
  echo "   echo 'RUN bun install' >> Dockerfile"
  echo "   echo 'RUN bun run build' >> Dockerfile"
  echo "   echo 'EXPOSE 3000' >> Dockerfile"
  echo "   echo 'CMD [\"bun\", \"start\"]' >> Dockerfile"
  echo ""
  echo "3. Build and push the frontend Docker image:"
  echo "   gcloud builds submit --tag gcr.io/$PROJECT_ID/adc-credit-frontend"
  echo ""
  echo "4. Deploy the frontend to Cloud Run:"
  echo "   gcloud run deploy adc-credit-frontend \\"
  echo "     --image gcr.io/$PROJECT_ID/adc-credit-frontend \\"
  echo "     --platform managed \\"
  echo "     --region ${REGION:-us-central1} \\"
  echo "     --allow-unauthenticated \\"
  echo "     --set-env-vars=NEXT_PUBLIC_BACKEND_URL=$BACKEND_URL,\\"
  echo "NEXTAUTH_URL=$FRONTEND_URL,\\"
  echo "NEXTAUTH_SECRET=$NEXTAUTH_SECRET,\\"
  echo "GOOGLE_CLIENT_ID=$GOOGLE_CLIENT_ID,\\"
  echo "GOOGLE_CLIENT_SECRET=$GOOGLE_CLIENT_SECRET,\\"
  echo "NODE_ENV=production"
  echo ""
  echo "5. After deployment, update your .env file with the new frontend URL if it has changed"
else
  echo "Backend deployment failed. Please check the logs for errors."
  exit 1
fi
