"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { 
  Dialog, 
  DialogContent, 
  DialogDescription, 
  DialogFooter, 
  DialogHeader, 
  DialogTitle 
} from "@/components/ui/dialog";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import { Loader2, AlertCircle, ArrowUp, ArrowDown } from "lucide-react";
import { Subscription, SubscriptionTier } from "@/types";
import { useToast } from "@/components/ui/use-toast";

interface UpgradeSubscriptionDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  subscription: Subscription | null;
  newTier: SubscriptionTier | null;
  tiers: SubscriptionTier[];
  onUpgrade: (subscriptionId: string, newTierId: number, prorate: boolean) => Promise<void>;
  isProcessing: boolean;
}

export function UpgradeSubscriptionDialog({
  open,
  onOpenChange,
  subscription,
  newTier,
  tiers,
  onUpgrade,
  isProcessing
}: UpgradeSubscriptionDialogProps) {
  const { toast } = useToast();
  const router = useRouter();
  const [prorate, setProrate] = useState(true);

  // Find the current tier
  const currentTier = subscription 
    ? tiers.find(t => t.id === subscription.subscription_tier_id) 
    : null;

  // Determine if this is an upgrade or downgrade
  const isUpgrade = currentTier && newTier 
    ? newTier.credit_limit > currentTier.credit_limit 
    : false;

  // Calculate credit difference
  const creditDifference = currentTier && newTier 
    ? newTier.credit_limit - currentTier.credit_limit 
    : 0;

  // Calculate price difference
  const priceDifference = currentTier && newTier 
    ? newTier.price - currentTier.price 
    : 0;

  const handleUpgrade = async () => {
    if (!subscription || !newTier) return;
    
    try {
      await onUpgrade(subscription.id, newTier.id, prorate);
    } catch (error) {
      console.error("Error upgrading subscription:", error);
      toast({
        title: "Error",
        description: "Failed to upgrade subscription. Please try again.",
        variant: "destructive",
      });
    }
  };

  if (!subscription || !newTier || !currentTier) {
    return null;
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>
            {isUpgrade ? (
              <span className="flex items-center">
                <ArrowUp className="h-5 w-5 mr-2 text-green-500" />
                Upgrade Subscription
              </span>
            ) : (
              <span className="flex items-center">
                <ArrowDown className="h-5 w-5 mr-2 text-amber-500" />
                Downgrade Subscription
              </span>
            )}
          </DialogTitle>
          <DialogDescription>
            {isUpgrade
              ? `Upgrade from ${currentTier.name} to ${newTier.name} tier`
              : `Downgrade from ${currentTier.name} to ${newTier.name} tier`}
          </DialogDescription>
        </DialogHeader>

        <div className="py-4 space-y-4">
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div className="space-y-2 border rounded-md p-3 bg-muted/50">
              <h3 className="font-medium">Current Plan</h3>
              <div className="flex justify-between">
                <span className="text-muted-foreground">Tier:</span>
                <span className="font-medium">{currentTier.name}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-muted-foreground">Credits:</span>
                <span className="font-medium">{currentTier.credit_limit.toLocaleString()}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-muted-foreground">Price:</span>
                <span className="font-medium">${currentTier.price.toFixed(2)}/month</span>
              </div>
            </div>

            <div className="space-y-2 border rounded-md p-3 bg-muted/50">
              <h3 className="font-medium">New Plan</h3>
              <div className="flex justify-between">
                <span className="text-muted-foreground">Tier:</span>
                <span className="font-medium">{newTier.name}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-muted-foreground">Credits:</span>
                <span className="font-medium">{newTier.credit_limit.toLocaleString()}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-muted-foreground">Price:</span>
                <span className="font-medium">${newTier.price.toFixed(2)}/month</span>
              </div>
            </div>
          </div>

          <div className="border rounded-md p-3 bg-muted/20">
            <h3 className="font-medium mb-2">Changes</h3>
            <div className="space-y-1">
              <div className="flex justify-between">
                <span className="text-muted-foreground">Credit Difference:</span>
                <span className={`font-medium ${creditDifference > 0 ? 'text-green-600' : creditDifference < 0 ? 'text-red-600' : ''}`}>
                  {creditDifference > 0 ? '+' : ''}{creditDifference.toLocaleString()} credits
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-muted-foreground">Price Difference:</span>
                <span className={`font-medium ${priceDifference > 0 ? 'text-amber-600' : priceDifference < 0 ? 'text-green-600' : ''}`}>
                  {priceDifference > 0 ? '+' : ''}{priceDifference.toFixed(2)}/month
                </span>
              </div>
            </div>
          </div>

          <div className="flex items-center space-x-2">
            <Checkbox 
              id="prorate" 
              checked={prorate} 
              onCheckedChange={(checked) => setProrate(checked as boolean)} 
            />
            <Label htmlFor="prorate" className="text-sm">
              Prorate credits based on current usage
            </Label>
          </div>

          <Alert>
            <AlertCircle className="h-4 w-4 mr-2" />
            <AlertDescription>
              {isUpgrade
                ? "Upgrading will give you immediate access to more credits and features. You'll be charged the difference in price."
                : "Downgrading will adjust your credits proportionally. Your new plan will take effect immediately."}
            </AlertDescription>
          </Alert>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)} disabled={isProcessing}>
            Cancel
          </Button>
          <Button onClick={handleUpgrade} disabled={isProcessing}>
            {isProcessing ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Processing...
              </>
            ) : isUpgrade ? (
              "Upgrade Subscription"
            ) : (
              "Downgrade Subscription"
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
