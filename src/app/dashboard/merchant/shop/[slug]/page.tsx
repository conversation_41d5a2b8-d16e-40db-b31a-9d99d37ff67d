"use client";

import React from 'react';
import dynamic from 'next/dynamic';

// Import the ShopDetail component from the correct path (same directory)
const ShopDetail = dynamic(() => import('./shop-detail'), { ssr: false });

export default function ShopDetailPage({ params }: { params: { slug: string } }) {
  // Unwrap params using React.use() as recommended by Next.js
  const unwrappedParams = React.use(params as any) as { slug: string };

  return <ShopDetail shopSlug={unwrappedParams.slug} />;
}
