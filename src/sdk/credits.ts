/**
 * ADC Credit SDK Credits Module
 */
import { ApiClient } from './client';
import { 
  APIResponse, 
  AddCreditsRequest, 
  AddCreditsResponse, 
  ConsumeCreditsRequest, 
  ConsumeCreditsResponse, 
  Subscription, 
  Transaction 
} from './types';

/**
 * Credits module for the ADC Credit SDK
 */
export class Credits {
  private client: ApiClient;

  /**
   * Creates a new Credits instance
   * @param client API client
   */
  constructor(client: ApiClient) {
    this.client = client;
  }

  /**
   * Gets the current credit balance
   * @returns Credit balance information
   */
  public async getBalance(): Promise<APIResponse<{
    credit_balance: number;
    credit_limit: number;
    subscription: Subscription;
  }>> {
    return this.client.get<{
      credit_balance: number;
      credit_limit: number;
      subscription: Subscription;
    }>('/credits');
  }

  /**
   * Gets transaction history
   * @returns List of transactions
   */
  public async getTransactions(): Promise<APIResponse<Transaction[]>> {
    return this.client.get<Transaction[]>('/credits/transactions');
  }

  /**
   * Gets the next scheduled credit date
   * @returns Next scheduled credit date
   */
  public async getNextScheduledCreditDate(): Promise<APIResponse<{ next_scheduled_credit_date: string }>> {
    return this.client.get<{ next_scheduled_credit_date: string }>('/credits/scheduled/next');
  }

  /**
   * Gets scheduled credit history
   * @returns List of scheduled credit transactions
   */
  public async getScheduledCreditHistory(): Promise<APIResponse<Transaction[]>> {
    return this.client.get<Transaction[]>('/credits/scheduled/history');
  }

  /**
   * Adds credits to the current user's account
   * @param data Credit data
   * @returns Result of adding credits
   */
  public async add(data: AddCreditsRequest): Promise<APIResponse<AddCreditsResponse>> {
    return this.client.post<AddCreditsResponse>('/credits/add', data);
  }

  /**
   * Consumes credits (external API)
   * @param data Consumption data
   * @returns Result of consuming credits
   */
  public async consume(data: ConsumeCreditsRequest): Promise<APIResponse<ConsumeCreditsResponse>> {
    return this.client.post<ConsumeCreditsResponse>('/external/consume', data);
  }

  /**
   * Processes scheduled credits (admin only)
   * @returns Result of processing scheduled credits
   */
  public async processScheduledCredits(): Promise<APIResponse<{ message: string; count: number }>> {
    return this.client.post<{ message: string; count: number }>('/admin/credits/scheduled/process');
  }

  /**
   * Manually adds scheduled credits for a user (admin only)
   * @param userId User ID
   * @returns Result of adding scheduled credits
   */
  public async manuallyAddScheduledCredits(
    userId: string
  ): Promise<APIResponse<{ message: string; user_id: string; credit_balance: number }>> {
    return this.client.post<{ message: string; user_id: string; credit_balance: number }>(
      '/admin/credits/scheduled/manual',
      { user_id: userId }
    );
  }

  /**
   * Adds credits to an external user
   * @param userId External user ID
   * @param amount Amount of credits to add
   * @param description Description of the transaction
   * @returns Result of adding credits
   */
  public async addCreditsToExternalUser(
    userId: string,
    amount: number,
    description?: string
  ): Promise<APIResponse<{ message: string; credit_balance: number }>> {
    return this.client.post<{ message: string; credit_balance: number }>(
      `/org-users/${userId}/credits/add`,
      { amount, description }
    );
  }

  /**
   * Reduces credits from an external user
   * @param userId External user ID
   * @param amount Amount of credits to reduce
   * @param description Description of the transaction
   * @returns Result of reducing credits
   */
  public async reduceCreditsFromExternalUser(
    userId: string,
    amount: number,
    description?: string
  ): Promise<APIResponse<{ message: string; credit_balance: number }>> {
    return this.client.post<{ message: string; credit_balance: number }>(
      `/org-users/${userId}/credits/reduce`,
      { amount, description }
    );
  }
}
