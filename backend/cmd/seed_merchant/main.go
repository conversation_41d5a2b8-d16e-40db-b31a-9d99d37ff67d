package main

import (
	"fmt"
	"log"
	"time"

	"github.com/adc-credit/backend/internal/database"
	"github.com/adc-credit/backend/internal/models"
	"github.com/google/uuid"
	"github.com/joho/godotenv"
	"gorm.io/gorm"
)

func main() {
	// Load environment variables
	if err := godotenv.Load(); err != nil {
		log.Println("No .env file found, using environment variables")
	}

	// Initialize database connection
	if err := database.InitDB(); err != nil {
		log.Fatalf("Failed to connect to database: %v", err)
	}

	// Seed merchant test data
	if err := seedMerchantData(); err != nil {
		log.Fatalf("Failed to seed merchant data: %v", err)
	}

	log.Println("Merchant test data seeded successfully!")
}

func seedMerchantData() error {
	// Get the test user
	var testUser models.User
	if err := database.DB.Where("email = ?", "<EMAIL>").First(&testUser).Error; err != nil {
		return fmt.Errorf("test user not found. Please run 'make seed' first: %w", err)
	}

	log.Println("Using test user:", testUser.ID)

	// Create test merchant shops
	shops := []models.MerchantShop{
		{
			ID:           uuid.New(),
			OwnerUserID:  testUser.ID,
			Name:         "Coffee Corner",
			Description:  "Your neighborhood coffee shop",
			ContactEmail: "<EMAIL>",
			ContactPhone: "******-0101",
			Slug:         "coffee-corner",
			CreatedAt:    time.Now(),
			UpdatedAt:    time.Now(),
		},
		{
			ID:           uuid.New(),
			OwnerUserID:  testUser.ID,
			Name:         "Tech Gadgets Store",
			Description:  "Latest technology and gadgets",
			ContactEmail: "<EMAIL>",
			ContactPhone: "******-0102",
			Slug:         "tech-gadgets-store",
			CreatedAt:    time.Now(),
			UpdatedAt:    time.Now(),
		},
		{
			ID:           uuid.New(),
			OwnerUserID:  testUser.ID,
			Name:         "Bookworm Paradise",
			Description:  "Books for every reader",
			ContactEmail: "<EMAIL>",
			ContactPhone: "******-0103",
			Slug:         "bookworm-paradise",
			CreatedAt:    time.Now(),
			UpdatedAt:    time.Now(),
		},
	}

	// Create or update shops
	for _, shop := range shops {
		var existingShop models.MerchantShop
		if err := database.DB.Where("slug = ?", shop.Slug).First(&existingShop).Error; err != nil {
			if err == gorm.ErrRecordNotFound {
				if err := database.DB.Create(&shop).Error; err != nil {
					return fmt.Errorf("failed to create shop %s: %w", shop.Name, err)
				}
				log.Printf("Created shop: %s (ID: %s)", shop.Name, shop.ID)
			} else {
				return fmt.Errorf("failed to check for existing shop: %w", err)
			}
		} else {
			log.Printf("Shop already exists: %s (ID: %s)", existingShop.Name, existingShop.ID)
			shop.ID = existingShop.ID // Use existing ID for credit codes
		}
	}

	// Create test customers for each shop
	for _, shop := range shops {
		customers := []models.ShopCustomer{
			{
				ID:            uuid.New(),
				ShopID:        shop.ID,
				UserID:        testUser.ID,
				Phone:         "******-1001",
				CreditBalance: 50,
				CreatedAt:     time.Now(),
				UpdatedAt:     time.Now(),
			},
			{
				ID:            uuid.New(),
				ShopID:        shop.ID,
				UserID:        testUser.ID,
				Phone:         "******-1002",
				CreditBalance: 75,
				CreatedAt:     time.Now(),
				UpdatedAt:     time.Now(),
			},
		}

		for _, customer := range customers {
			var existingCustomer models.ShopCustomer
			if err := database.DB.Where("shop_id = ? AND phone = ?", shop.ID, customer.Phone).First(&existingCustomer).Error; err != nil {
				if err == gorm.ErrRecordNotFound {
					if err := database.DB.Create(&customer).Error; err != nil {
						return fmt.Errorf("failed to create customer %s for shop %s: %w", customer.Phone, shop.Name, err)
					}
					log.Printf("Created customer: %s for shop %s", customer.Phone, shop.Name)
				} else {
					return fmt.Errorf("failed to check for existing customer: %w", err)
				}
			} else {
				log.Printf("Customer already exists: %s for shop %s", existingCustomer.Phone, shop.Name)
			}
		}
	}

	// Create test credit codes for each shop
	for _, shop := range shops {
		creditCodes := []models.CreditCode{
			{
				ID:          uuid.New(),
				ShopID:      shop.ID,
				Code:        fmt.Sprintf("TEST%s01", shop.Slug[:4]),
				Amount:      10,
				Description: "Test credit code - $10",
				IsRedeemed:  false,
				CreatedAt:   time.Now(),
				UpdatedAt:   time.Now(),
			},
			{
				ID:          uuid.New(),
				ShopID:      shop.ID,
				Code:        fmt.Sprintf("TEST%s02", shop.Slug[:4]),
				Amount:      25,
				Description: "Test credit code - $25",
				IsRedeemed:  false,
				CreatedAt:   time.Now(),
				UpdatedAt:   time.Now(),
			},
			{
				ID:          uuid.New(),
				ShopID:      shop.ID,
				Code:        fmt.Sprintf("TEST%s03", shop.Slug[:4]),
				Amount:      50,
				Description: "Test credit code - $50",
				IsRedeemed:  false,
				CreatedAt:   time.Now(),
				UpdatedAt:   time.Now(),
			},
		}

		for _, creditCode := range creditCodes {
			var existingCode models.CreditCode
			if err := database.DB.Where("code = ?", creditCode.Code).First(&existingCode).Error; err != nil {
				if err == gorm.ErrRecordNotFound {
					if err := database.DB.Create(&creditCode).Error; err != nil {
						return fmt.Errorf("failed to create credit code %s: %w", creditCode.Code, err)
					}
					log.Printf("Created credit code: %s for shop %s (Amount: $%d)", creditCode.Code, shop.Name, creditCode.Amount)
				} else {
					return fmt.Errorf("failed to check for existing credit code: %w", err)
				}
			} else {
				log.Printf("Credit code already exists: %s", existingCode.Code)
			}
		}
	}

	// Print QR code data for testing
	log.Println("\n=== QR CODE TEST DATA ===")
	for _, shop := range shops {
		var codes []models.CreditCode
		database.DB.Where("shop_id = ? AND is_redeemed = ?", shop.ID, false).Find(&codes)

		log.Printf("\nShop: %s (ID: %s)", shop.Name, shop.ID)
		for _, code := range codes {
			qrData := fmt.Sprintf("shop:%s;code:%s;amount:%d", shop.ID, code.Code, code.Amount)
			log.Printf("  Code: %s | Amount: $%d | QR Data: %s", code.Code, code.Amount, qrData)
		}
	}

	return nil
}
