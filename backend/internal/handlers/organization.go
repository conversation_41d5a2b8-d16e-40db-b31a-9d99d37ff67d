package handlers

import (
	"net/http"

	"github.com/adc-credit/backend/internal/database"
	"github.com/adc-credit/backend/internal/models"
	"github.com/adc-credit/backend/internal/utils"
	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

// GetOrganizations returns all organizations for the current user
func GetOrganizations(c *gin.Context) {
	user := c.MustGet("user").(models.User)

	var organizations []models.Organization
	if user.Role == "admin" {
		// Admin can see all organizations
		if err := database.DB.Find(&organizations).Error; err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch organizations"})
			return
		}
	} else {
		// Regular users can only see organizations they own
		if err := database.DB.Where("owner_user_id = ?", user.ID).Find(&organizations).Error; err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch organizations"})
			return
		}
	}

	c.<PERSON>(http.StatusOK, organizations)
}

// GetOrganizationBySlug returns a specific organization by slug
func GetOrganizationBySlug(c *gin.Context) {
	user := c.MustGet("user").(models.User)
	slug := c.Param("slug")

	var organization models.Organization
	if user.Role == "admin" {
		// Admin can see any organization
		if err := database.DB.First(&organization, "slug = ?", slug).Error; err != nil {
			c.JSON(http.StatusNotFound, gin.H{"error": "Organization not found"})
			return
		}
	} else {
		// Regular users can only see organizations they own
		if err := database.DB.First(&organization, "slug = ? AND owner_user_id = ?", slug, user.ID).Error; err != nil {
			c.JSON(http.StatusNotFound, gin.H{"error": "Organization not found"})
			return
		}
	}

	c.JSON(http.StatusOK, organization)
}

// GetOrganization returns a specific organization by ID (for backward compatibility)
func GetOrganization(c *gin.Context) {
	user := c.MustGet("user").(models.User)
	id := c.Param("id")

	orgID, err := uuid.Parse(id)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid organization ID"})
		return
	}

	var organization models.Organization
	if user.Role == "admin" {
		// Admin can see any organization
		if err := database.DB.First(&organization, "id = ?", orgID).Error; err != nil {
			c.JSON(http.StatusNotFound, gin.H{"error": "Organization not found"})
			return
		}
	} else {
		// Regular users can only see organizations they own
		if err := database.DB.First(&organization, "id = ? AND owner_user_id = ?", orgID, user.ID).Error; err != nil {
			c.JSON(http.StatusNotFound, gin.H{"error": "Organization not found"})
			return
		}
	}

	c.JSON(http.StatusOK, organization)
}

// CreateOrganization creates a new organization
func CreateOrganization(c *gin.Context) {
	user := c.MustGet("user").(models.User)

	type CreateOrganizationRequest struct {
		Name        string `json:"name" binding:"required"`
		Description string `json:"description"`
	}

	var req CreateOrganizationRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Generate slug from name
	baseSlug := utils.GenerateSlug(req.Name)
	
	// Ensure slug is unique
	slug := utils.EnsureUniqueSlug(baseSlug, func(s string) bool {
		var count int64
		database.DB.Model(&models.Organization{}).Where("slug = ?", s).Count(&count)
		return count > 0
	})

	organization := models.Organization{
		ID:          uuid.New(),
		Name:        req.Name,
		Slug:        slug,
		Description: req.Description,
		OwnerUserID: user.ID,
	}

	if err := database.DB.Create(&organization).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create organization"})
		return
	}

	c.JSON(http.StatusCreated, organization)
}

// UpdateOrganization updates an existing organization
func UpdateOrganization(c *gin.Context) {
	user := c.MustGet("user").(models.User)
	id := c.Param("slug")

	// Check if ID is a UUID or a slug
	var organization models.Organization
	var err error
	
	orgID, uuidErr := uuid.Parse(id)
	if uuidErr == nil {
		// ID is a valid UUID
		if user.Role == "admin" {
			err = database.DB.First(&organization, "id = ?", orgID).Error
		} else {
			err = database.DB.First(&organization, "id = ? AND owner_user_id = ?", orgID, user.ID).Error
		}
	} else {
		// ID might be a slug
		if user.Role == "admin" {
			err = database.DB.First(&organization, "slug = ?", id).Error
		} else {
			err = database.DB.First(&organization, "slug = ? AND owner_user_id = ?", id, user.ID).Error
		}
	}
	
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Organization not found"})
		return
	}

	type UpdateOrganizationRequest struct {
		Name        string `json:"name"`
		Description string `json:"description"`
	}

	var req UpdateOrganizationRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Update slug if name is changed
	if req.Name != "" && req.Name != organization.Name {
		organization.Name = req.Name
		
		// Generate new slug from updated name
		baseSlug := utils.GenerateSlug(req.Name)
		
		// Ensure slug is unique, excluding the current organization
		slug := utils.EnsureUniqueSlug(baseSlug, func(s string) bool {
			var count int64
			database.DB.Model(&models.Organization{}).Where("slug = ? AND id != ?", s, organization.ID).Count(&count)
			return count > 0
		})
		
		organization.Slug = slug
	}
	
	if req.Description != "" {
		organization.Description = req.Description
	}

	if err := database.DB.Save(&organization).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update organization"})
		return
	}

	c.JSON(http.StatusOK, organization)
}

// DeleteOrganization deletes an organization
func DeleteOrganization(c *gin.Context) {
	user := c.MustGet("user").(models.User)
	id := c.Param("slug")

	// Check if ID is a UUID or a slug
	var organization models.Organization
	var err error
	
	orgID, uuidErr := uuid.Parse(id)
	if uuidErr == nil {
		// ID is a valid UUID
		if user.Role == "admin" {
			err = database.DB.First(&organization, "id = ?", orgID).Error
		} else {
			err = database.DB.First(&organization, "id = ? AND owner_user_id = ?", orgID, user.ID).Error
		}
	} else {
		// ID might be a slug
		if user.Role == "admin" {
			err = database.DB.First(&organization, "slug = ?", id).Error
		} else {
			err = database.DB.First(&organization, "slug = ? AND owner_user_id = ?", id, user.ID).Error
		}
	}
	
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Organization not found"})
		return
	}

	// Check if organization has branches
	var branchCount int64
	if err := database.DB.Model(&models.Branch{}).Where("organization_id = ?", organization.ID).Count(&branchCount).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to check branches"})
		return
	}

	if branchCount > 0 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Cannot delete organization with branches"})
		return
	}

	if err := database.DB.Delete(&organization).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete organization"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Organization deleted successfully"})
}
