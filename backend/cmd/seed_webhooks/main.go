package main

import (
	"fmt"
	"log"
	"time"

	"github.com/adc-credit/backend/internal/database"
	"github.com/google/uuid"
	"github.com/joho/godotenv"
)

func main() {
	// Load environment variables
	if err := godotenv.Load(); err != nil {
		log.Println("No .env file found, using environment variables")
	}

	// Initialize database connection
	if err := database.InitDB(); err != nil {
		log.Fatalf("Failed to connect to database: %v", err)
	}

	// Get all users
	var users []struct {
		ID    uuid.UUID
		Email string
	}

	if err := database.DB.Model(&struct {
		ID    uuid.UUID
		Email string
	}{}).Table("users").Select("id, email").Scan(&users).Error; err != nil {
		log.Fatalf("Failed to get users: %v", err)
	}

	if len(users) == 0 {
		log.Fatalf("No users found in the database")
	}

	// Use the first user
	userID := users[0].ID
	log.Printf("Using user: %s (%s)", userID, users[0].Email)

	// Create test webhooks
	if err := seedWebhooks(userID); err != nil {
		log.Fatalf("Failed to seed webhooks: %v", err)
	}

	log.Println("Webhook data seeded successfully!")
}

func seedWebhooks(userID uuid.UUID) error {
	// Delete existing webhooks for this user
	if err := database.DB.Exec("DELETE FROM webhook_deliveries WHERE webhook_id IN (SELECT id FROM webhooks WHERE user_id = ?)", userID).Error; err != nil {
		return fmt.Errorf("failed to delete existing webhook deliveries: %w", err)
	}

	if err := database.DB.Exec("DELETE FROM webhooks WHERE user_id = ?", userID).Error; err != nil {
		return fmt.Errorf("failed to delete existing webhooks: %w", err)
	}

	log.Println("Deleted existing webhooks for user:", userID)

	// Create test webhooks using raw SQL
	webhooks := []struct {
		ID     uuid.UUID
		Name   string
		URL    string
		Secret string
		Events []string
		Active bool
	}{
		{
			ID:     uuid.New(),
			Name:   "Credit Notifications",
			URL:    "https://webhook.site/12345678-1234-1234-1234-123456789012",
			Secret: "secret123",
			Events: []string{"credit.consumed", "credit.added"},
			Active: true,
		},
		{
			ID:     uuid.New(),
			Name:   "API Key Events",
			URL:    "https://webhook.site/*************-4321-4321-************",
			Secret: "apisecret456",
			Events: []string{"api_key.created", "api_key.updated", "api_key.deleted"},
			Active: true,
		},
		{
			ID:     uuid.New(),
			Name:   "Subscription Events",
			URL:    "https://webhook.site/abcdef12-3456-7890-abcd-ef1234567890",
			Secret: "subsecret789",
			Events: []string{"subscription.created", "subscription.updated", "subscription.cancelled"},
			Active: false,
		},
	}

	for _, webhook := range webhooks {
		// Convert events to a string array representation
		eventsStr := "ARRAY["
		for i, event := range webhook.Events {
			if i > 0 {
				eventsStr += ", "
			}
			eventsStr += fmt.Sprintf("'%s'", event)
		}
		eventsStr += "]"

		// Insert webhook using raw SQL with direct array syntax
		query := fmt.Sprintf(`
			INSERT INTO webhooks (id, user_id, name, url, secret, events, active, fail_count, created_at, updated_at)
			VALUES ('%s', '%s', '%s', '%s', '%s', %s, %t, %d, '%s', '%s')
		`, webhook.ID, userID, webhook.Name, webhook.URL, webhook.Secret,
			eventsStr, webhook.Active, 0, time.Now().Format("2006-01-02 15:04:05"),
			time.Now().Format("2006-01-02 15:04:05"))

		result := database.DB.Exec(query)

		if result.Error != nil {
			return fmt.Errorf("failed to create webhook %s: %w", webhook.Name, result.Error)
		}

		log.Printf("Created webhook: %s (ID: %s)", webhook.Name, webhook.ID)

		// Create webhook deliveries
		if err := seedWebhookDeliveries(webhook.ID); err != nil {
			return err
		}
	}

	return nil
}

func seedWebhookDeliveries(webhookID uuid.UUID) error {
	// Create test webhook deliveries
	deliveries := []struct {
		ID         uuid.UUID
		Event      string
		Payload    string
		StatusCode int
		Response   string
		Success    bool
		Duration   int
		CreatedAt  time.Time
	}{
		{
			ID:         uuid.New(),
			Event:      "credit.consumed",
			Payload:    `{"id":"12345","event":"credit.consumed","created_at":"2025-05-18T12:00:00Z","data":{"user_id":"user123","credits":10,"endpoint":"/api/v1/data"}}`,
			StatusCode: 200,
			Response:   `{"status":"ok"}`,
			Success:    true,
			Duration:   150,
			CreatedAt:  time.Now().Add(-48 * time.Hour),
		},
		{
			ID:         uuid.New(),
			Event:      "credit.added",
			Payload:    `{"id":"67890","event":"credit.added","created_at":"2025-05-18T14:30:00Z","data":{"user_id":"user123","credits":100,"source":"purchase"}}`,
			StatusCode: 200,
			Response:   `{"status":"ok"}`,
			Success:    true,
			Duration:   120,
			CreatedAt:  time.Now().Add(-24 * time.Hour),
		},
		{
			ID:         uuid.New(),
			Event:      "api_key.created",
			Payload:    `{"id":"abcde","event":"api_key.created","created_at":"2025-05-18T16:45:00Z","data":{"user_id":"user123","api_key_id":"key123","name":"New API Key"}}`,
			StatusCode: 500,
			Response:   `{"error":"Internal server error"}`,
			Success:    false,
			Duration:   350,
			CreatedAt:  time.Now().Add(-12 * time.Hour),
		},
		{
			ID:         uuid.New(),
			Event:      "subscription.updated",
			Payload:    `{"id":"fghij","event":"subscription.updated","created_at":"2025-05-18T18:15:00Z","data":{"user_id":"user123","subscription_id":"sub123","tier":"pro"}}`,
			StatusCode: 404,
			Response:   `{"error":"Not found"}`,
			Success:    false,
			Duration:   200,
			CreatedAt:  time.Now().Add(-6 * time.Hour),
		},
		{
			ID:         uuid.New(),
			Event:      "credit.consumed",
			Payload:    `{"id":"klmno","event":"credit.consumed","created_at":"2025-05-18T20:00:00Z","data":{"user_id":"user123","credits":5,"endpoint":"/api/v1/analyze"}}`,
			StatusCode: 200,
			Response:   `{"status":"ok"}`,
			Success:    true,
			Duration:   180,
			CreatedAt:  time.Now().Add(-1 * time.Hour),
		},
	}

	for _, delivery := range deliveries {
		// Insert webhook delivery using raw SQL with direct formatting
		query := fmt.Sprintf(`
			INSERT INTO webhook_deliveries (id, webhook_id, event, payload, status_code, response, success, duration, created_at, updated_at)
			VALUES ('%s', '%s', '%s', '%s', %d, '%s', %t, %d, '%s', '%s')
		`, delivery.ID, webhookID, delivery.Event, delivery.Payload,
			delivery.StatusCode, delivery.Response, delivery.Success,
			delivery.Duration, delivery.CreatedAt.Format("2006-01-02 15:04:05"),
			delivery.CreatedAt.Format("2006-01-02 15:04:05"))

		result := database.DB.Exec(query)

		if result.Error != nil {
			return fmt.Errorf("failed to create webhook delivery: %w", result.Error)
		}
	}

	log.Printf("Created %d webhook deliveries for webhook ID: %s", len(deliveries), webhookID)
	return nil
}
