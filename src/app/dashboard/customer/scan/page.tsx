"use client";

import { useState, useEffect, useRef, use<PERSON>allback } from "react";
import { useRouter } from "next/navigation";
import Link from "next/link";
import dynamic from "next/dynamic";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { useToast } from "@/components/ui/use-toast";
import { Camera, QrCode, CreditCard, Check, X } from "phosphor-react";
import { CustomerBreadcrumbs } from "@/components/navigation/breadcrumbs";
import { CustomerPageHeader } from "@/components/layout/page-header-mobile";
import { useRedeemCreditCodeMutation } from "@/lib/api/apiSlice";

// Dynamically import the HTML5QrcodeScanner to avoid SSR issues
const Html5QrcodePlugin = dynamic(() => import("@/components/scanner/html5-qrcode-plugin"), {
  ssr: false,
  loading: () => (
    <div className="flex flex-col items-center justify-center p-8 bg-muted rounded-md h-[300px]">
      <QrCode size={64} className="text-muted-foreground mb-4" />
      <p className="text-muted-foreground">Loading scanner...</p>
    </div>
  ),
});

export default function ScanPage() {
  const router = useRouter();
  const { toast } = useToast();
  const [scanning, setScanning] = useState(false);
  const [scannedCode, setScannedCode] = useState<string | null>(null);
  const [redeemCode, { isLoading }] = useRedeemCreditCodeMutation();
  const [redemptionResult, setRedemptionResult] = useState<{
    success: boolean;
    message: string;
    creditBalance?: number;
    shopName?: string;
  } | null>(null);

  // Handle redeeming the code
  const handleRedeemCode = useCallback(async (code: string) => {
    try {
      const result = await redeemCode({ code }).unwrap();
      const successMessage = "Credit code redeemed successfully!";

      setRedemptionResult({
        success: true,
        message: successMessage,
        creditBalance: result.credit_balance,
        shopName: result.shop_name,
      });

      // Show success toast
      toast({
        title: "Success!",
        description: `${successMessage} Added ${result.credit_balance} credits from ${result.shop_name}.`,
        variant: "default",
      });
    } catch (error: any) {
      console.error("Error redeeming code:", error);

      // Handle different types of errors
      let errorMessage = "Failed to redeem code. Please try again.";

      if (error.data?.error) {
        errorMessage = error.data.error;
      } else if (error.data?.message) {
        errorMessage = error.data.message;
      } else if (error.message) {
        errorMessage = error.message;
      }

      // Provide more specific error messages
      if (errorMessage.includes("not found") || errorMessage.includes("already redeemed")) {
        errorMessage = "This credit code is invalid or has already been used.";
      } else if (errorMessage.includes("expired")) {
        errorMessage = "This credit code has expired.";
      } else if (errorMessage.includes("unauthorized")) {
        errorMessage = "You are not authorized to redeem this code.";
      }

      setRedemptionResult({
        success: false,
        message: errorMessage,
      });

      // Show error toast
      toast({
        title: "Redemption Failed",
        description: errorMessage,
        variant: "destructive",
      });
    }
  }, [redeemCode, toast]);

  // Handle successful scan
  const onScanSuccess = useCallback((decodedText: string) => {
    setScanning(false);

    // Parse the QR code data
    try {
      // Expected format: shop:SHOP_ID;code:CODE;amount:AMOUNT
      const parts = decodedText.split(';');
      const codeData: Record<string, string> = {};

      parts.forEach(part => {
        const [key, value] = part.split(':');
        if (key && value) {
          codeData[key] = value;
        }
      });

      if (codeData.code) {
        setScannedCode(codeData.code);

        // Show immediate feedback that QR code was scanned
        toast({
          title: "QR Code Scanned",
          description: `Processing credit code: ${codeData.code}`,
          variant: "default",
        });

        handleRedeemCode(codeData.code);
      } else {
        toast({
          title: "Invalid QR Code",
          description: "The scanned QR code doesn't contain a valid credit code",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error("Error parsing QR code:", error);
      toast({
        title: "Invalid QR Code",
        description: "The scanned QR code format is not recognized",
        variant: "destructive",
      });
    }
  }, [toast, handleRedeemCode]);

  // Handle scan error - only log errors, don't show toast for every scan attempt
  const onScanError = useCallback((error: any) => {
    // Only log actual errors, not routine "no QR code found" messages
    if (error && !error.toString().includes("No QR code found")) {
      console.error("QR code scan error:", error);
    }
  }, []);

  // Reset the scanner
  const resetScanner = () => {
    setScannedCode(null);
    setRedemptionResult(null);
    setScanning(true);
  };

  return (
    <div className="relative flex size-full min-h-screen flex-col bg-[#fbfaf9] justify-between overflow-x-hidden">
      {/* Header */}
      <CustomerPageHeader
        title="Scan QR Code"
        backHref="/dashboard/customer"
      />

      {/* Breadcrumbs */}
      <CustomerBreadcrumbs
        segments={[
          { label: "Scan", href: "/dashboard/customer/scan", isCurrentPage: true }
        ]}
      />

      {/* Main Content */}
      <div className="flex-1 p-4">
        <div className="w-full max-w-md mx-auto">
          <Card>
            <CardHeader>
              <CardTitle>Scan Credit Code</CardTitle>
              <CardDescription>
                Scan a QR code to redeem credits for your account
              </CardDescription>
            </CardHeader>
            <CardContent>
              {!scannedCode && !redemptionResult ? (
                <>
                  {scanning ? (
                    <Html5QrcodePlugin
                      fps={10}
                      qrbox={250}
                      disableFlip={false}
                      qrCodeSuccessCallback={onScanSuccess}
                      qrCodeErrorCallback={onScanError}
                    />
                  ) : (
                    <div className="flex flex-col items-center justify-center p-8 bg-muted rounded-md">
                      <Camera size={64} className="text-muted-foreground mb-4" />
                      <p className="text-muted-foreground mb-4">Camera access required to scan QR codes</p>
                      <Button onClick={() => setScanning(true)}>
                        Start Scanner
                      </Button>
                    </div>
                  )}
                </>
              ) : redemptionResult ? (
                <div className="py-4">
                  {redemptionResult.success ? (
                    <Alert className="bg-green-50 border-green-200">
                      <Check className="h-5 w-5 text-green-600" />
                      <AlertTitle className="text-green-800">Success!</AlertTitle>
                      <AlertDescription className="text-green-700">
                        {redemptionResult.message}
                        {redemptionResult.shopName && (
                          <p className="mt-2">Shop: {redemptionResult.shopName}</p>
                        )}
                        {redemptionResult.creditBalance !== undefined && (
                          <p className="mt-1">New Balance: {redemptionResult.creditBalance} credits</p>
                        )}
                      </AlertDescription>
                    </Alert>
                  ) : (
                    <Alert className="bg-red-50 border-red-200">
                      <X className="h-5 w-5 text-red-600" />
                      <AlertTitle className="text-red-800">Error</AlertTitle>
                      <AlertDescription className="text-red-700">
                        {redemptionResult.message}
                      </AlertDescription>
                    </Alert>
                  )}
                </div>
              ) : (
                <div className="py-4">
                  <Alert>
                    <QrCode className="h-5 w-5" />
                    <AlertTitle>Processing Code</AlertTitle>
                    <AlertDescription>
                      Redeeming code: {scannedCode}
                    </AlertDescription>
                  </Alert>
                </div>
              )}
            </CardContent>
            <CardFooter className="flex justify-between">
              {redemptionResult ? (
                <>
                  <Button variant="outline" onClick={resetScanner}>
                    Scan Another Code
                  </Button>
                  <Button onClick={() => router.push("/dashboard/customer")}>
                    View My Credits
                  </Button>
                </>
              ) : (
                <Button
                  variant="outline"
                  className="w-full"
                  onClick={() => router.push("/dashboard/customer/redeem")}
                >
                  Enter Code Manually
                </Button>
              )}
            </CardFooter>
          </Card>
        </div>
      </div>

      {/* Footer */}
      <div className="mt-auto">
        <div className="flex justify-stretch">
          <div className="flex flex-1 gap-3 flex-wrap px-4 py-3 justify-between">
            <Button
              className="flex w-full cursor-pointer items-center justify-center overflow-hidden rounded-lg h-12 px-5 bg-[#e5ccb2] text-[#181510] text-base font-bold leading-normal tracking-[0.015em] hover:bg-[#d9b99a]"
              onClick={() => router.push("/dashboard/customer")}
            >
              <CreditCard className="mr-2 h-5 w-5" />
              <span className="truncate">My Credits</span>
            </Button>
          </div>
        </div>
        <div className="h-5 bg-[#fbfaf9]"></div>
      </div>
    </div>
  );
}
