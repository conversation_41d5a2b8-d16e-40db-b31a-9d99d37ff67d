"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { useRouter } from "next/navigation";
import { Skeleton } from "@/components/ui/skeleton";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Plus, Storefront, CurrencyDollar, User, House, CaretRight, QrCode, ChartBar } from "phosphor-react";
import Link from "next/link";
import { useGetMerchantShopsQuery, useGetMerchantCreditStatsQuery } from "@/lib/api/apiSlice";
import { MerchantShop } from "@/types";

export default function MerchantDashboardPage() {
  const { data: shops, isLoading: isShopsLoading } = useGetMerchantShopsQuery();
  const {
    data: creditStats,
    isLoading: isStatsLoading
  } = useGetMerchantCreditStatsQuery();
  const router = useRouter();

  // Get credit statistics from API
  const totalCreditsIssued = creditStats?.total_credits_issued || 0;
  const totalCreditsRedeemed = creditStats?.total_credits_redeemed || 0;

  return (
    <div className="relative flex size-full min-h-screen flex-col bg-background justify-between group/design-root overflow-x-hidden">
      <div className="flex-1">
        {/* Header - Mobile & Desktop */}
        <div className="flex items-center bg-background p-4 pb-2 justify-between border-b">
          <h2 className="text-foreground text-lg md:text-2xl font-bold leading-tight tracking-[-0.015em] flex-1 text-center pl-12 md:pl-0">Shop Dashboard</h2>
          <div className="flex w-12 items-center justify-end">
            <Link href="/dashboard/merchant/new">
              <Button
                className="flex max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-lg h-12 gap-2 text-base font-bold leading-normal tracking-[0.015em] min-w-0 p-0 md:px-4 md:py-2"
                variant="ghost"
              >
                <Plus size={24} weight="regular" />
                <span className="hidden md:inline">New Shop</span>
              </Button>
            </Link>
          </div>
        </div>

        {/* Main Content - Responsive Layout */}
        <div className="container py-4 md:py-8">
          {/* Stats Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 md:gap-6 mb-6 md:mb-8">
            <Card>
              <CardHeader className="pb-2 space-y-1">
                <div className="flex items-center justify-between">
                  <CardTitle className="text-base font-medium flex items-center gap-2">
                    <CurrencyDollar className="h-4 w-4 text-primary" />
                    Total Credits Issued
                  </CardTitle>
                  <Badge variant="outline" className="text-xs font-normal">
                    Issued
                  </Badge>
                </div>
                <CardDescription>Credits distributed to customers</CardDescription>
              </CardHeader>
              <CardContent>
                {isStatsLoading ? (
                  <Skeleton className="h-10 w-32" />
                ) : (
                  <div className="text-2xl md:text-3xl font-bold">
                    ${totalCreditsIssued.toLocaleString()}
                  </div>
                )}
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2 space-y-1">
                <div className="flex items-center justify-between">
                  <CardTitle className="text-base font-medium flex items-center gap-2">
                    <QrCode className="h-4 w-4 text-primary" />
                    Total Credits Redeemed
                  </CardTitle>
                  <Badge variant="outline" className="text-xs font-normal">
                    Redeemed
                  </Badge>
                </div>
                <CardDescription>Credits used by customers</CardDescription>
              </CardHeader>
              <CardContent>
                {isStatsLoading ? (
                  <Skeleton className="h-10 w-32" />
                ) : (
                  <div className="text-2xl md:text-3xl font-bold">
                    ${totalCreditsRedeemed.toLocaleString()}
                  </div>
                )}
              </CardContent>
            </Card>
          </div>

          {/* Tabs for different sections */}
          <Tabs defaultValue="shops" className="w-full">
            <TabsList className="grid w-full grid-cols-4 mb-6">
              <TabsTrigger value="shops" className="text-sm py-2">
                <Storefront className="mr-2 h-4 w-4" />
                My Shops
              </TabsTrigger>
              <TabsTrigger value="analytics" className="text-sm py-2">
                <ChartBar className="mr-2 h-4 w-4" />
                Analytics
              </TabsTrigger>
              <TabsTrigger value="usage" className="text-sm py-2">
                <QrCode className="mr-2 h-4 w-4" />
                Usage
              </TabsTrigger>
              <TabsTrigger value="quick-actions" className="text-sm py-2">
                <CurrencyDollar className="mr-2 h-4 w-4" />
                Quick Actions
              </TabsTrigger>
            </TabsList>

            <TabsContent value="shops" className="space-y-4">
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-[#181510] flex items-center">
                    <Storefront className="mr-2 h-5 w-5" />
                    My Shops
                  </CardTitle>
                  <CardDescription>Manage your merchant shops</CardDescription>
                </CardHeader>
                <CardContent>
                  {isShopsLoading ? (
                    <div className="space-y-4">
                      {[1, 2, 3].map((i) => (
                        <div key={i} className="flex items-center gap-4 p-4 border rounded-lg">
                          <Skeleton className="h-14 w-14 rounded-lg bg-gray-200" />
                          <div className="flex-1">
                            <Skeleton className="h-5 w-32 bg-gray-200 mb-2" />
                            <Skeleton className="h-4 w-48 bg-gray-200" />
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : shops && shops.length > 0 ? (
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {shops.map((shop: MerchantShop) => (
                        <Link href={`/dashboard/merchant/shop/${shop.slug}`} key={shop.id} className="block">
                          <Card className="hover:shadow-md transition-shadow overflow-hidden h-full">
                            <div className="flex items-center gap-4 p-4">
                              <div className="bg-muted rounded-lg size-14 flex items-center justify-center flex-shrink-0">
                                <Storefront size={32} weight="regular" className="text-muted-foreground" />
                              </div>
                              <div className="flex flex-col justify-center flex-1 min-w-0">
                                <p className="text-foreground text-base font-medium leading-normal line-clamp-1">{shop.name}</p>
                                <p className="text-muted-foreground text-sm font-normal leading-normal line-clamp-2">{shop.description || "No description"}</p>
                              </div>
                              <div className="shrink-0">
                                <div className="text-foreground flex size-7 items-center justify-center">
                                  <CaretRight size={24} weight="regular" />
                                </div>
                              </div>
                            </div>
                          </Card>
                        </Link>
                      ))}
                    </div>
                  ) : (
                    <div className="p-8 text-center">
                      <Storefront size={48} weight="regular" className="text-muted-foreground mx-auto mb-4" />
                      <p className="text-muted-foreground mb-4">You don&apos;t have any shops yet.</p>
                      <Button
                        onClick={() => router.push("/dashboard/merchant/new")}
                      >
                        Create Your First Shop
                      </Button>
                    </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="analytics" className="space-y-4">
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-[#181510] flex items-center">
                    <ChartBar className="mr-2 h-5 w-5" />
                    Shop Analytics
                  </CardTitle>
                  <CardDescription>Performance metrics and insights for your shops</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
                    {/* Credit Performance */}
                    <Card>
                      <CardHeader className="pb-2">
                        <CardTitle className="text-sm font-medium">Credit Performance</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="space-y-2">
                          <div className="flex justify-between">
                            <span className="text-sm text-muted-foreground">Issued</span>
                            <span className="text-sm font-medium">${totalCreditsIssued.toLocaleString()}</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-sm text-muted-foreground">Redeemed</span>
                            <span className="text-sm font-medium">${totalCreditsRedeemed.toLocaleString()}</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-sm text-muted-foreground">Rate</span>
                            <span className="text-sm font-medium">
                              {totalCreditsIssued > 0 ? ((totalCreditsRedeemed / totalCreditsIssued) * 100).toFixed(1) : 0}%
                            </span>
                          </div>
                        </div>
                      </CardContent>
                    </Card>

                    {/* Shop Performance */}
                    <Card>
                      <CardHeader className="pb-2">
                        <CardTitle className="text-sm font-medium">Shop Performance</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="space-y-2">
                          <div className="flex justify-between">
                            <span className="text-sm text-muted-foreground">Total Shops</span>
                            <span className="text-sm font-medium">{shops?.length || 0}</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-sm text-muted-foreground">Active</span>
                            <span className="text-sm font-medium">{shops?.length || 0}</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-sm text-muted-foreground">Avg Credits/Shop</span>
                            <span className="text-sm font-medium">
                              {shops?.length ? Math.round(totalCreditsIssued / shops.length).toLocaleString() : 0}
                            </span>
                          </div>
                        </div>
                      </CardContent>
                    </Card>

                    {/* Growth Metrics */}
                    <Card>
                      <CardHeader className="pb-2">
                        <CardTitle className="text-sm font-medium">Growth Metrics</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="space-y-2">
                          <div className="flex justify-between">
                            <span className="text-sm text-muted-foreground">This Month</span>
                            <span className="text-sm font-medium text-green-600">+12%</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-sm text-muted-foreground">Last 7 Days</span>
                            <span className="text-sm font-medium text-green-600">+5%</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-sm text-muted-foreground">Trend</span>
                            <span className="text-sm font-medium text-green-600">↗ Growing</span>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  </div>

                  {/* Analytics Chart Placeholder */}
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-sm font-medium">Credit Usage Over Time</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="h-64 flex items-center justify-center bg-muted rounded-lg">
                        <div className="text-center">
                          <ChartBar size={48} className="text-muted-foreground mx-auto mb-2" />
                          <p className="text-muted-foreground">Analytics chart coming soon</p>
                          <p className="text-sm text-muted-foreground">Track your credit usage trends</p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="usage" className="space-y-4">
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-[#181510] flex items-center">
                    <QrCode className="mr-2 h-5 w-5" />
                    Usage Analytics
                  </CardTitle>
                  <CardDescription>Track how your shops and customers are using the credit system</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                    {/* Usage Summary */}
                    <Card>
                      <CardHeader className="pb-2">
                        <CardTitle className="text-sm font-medium">Usage Summary</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="space-y-3">
                          <div className="flex items-center justify-between">
                            <div className="flex items-center gap-2">
                              <QrCode size={16} className="text-muted-foreground" />
                              <span className="text-sm">QR Codes Generated</span>
                            </div>
                            <span className="text-sm font-medium">24</span>
                          </div>
                          <div className="flex items-center justify-between">
                            <div className="flex items-center gap-2">
                              <User size={16} className="text-muted-foreground" />
                              <span className="text-sm">Customer Scans</span>
                            </div>
                            <span className="text-sm font-medium">156</span>
                          </div>
                          <div className="flex items-center justify-between">
                            <div className="flex items-center gap-2">
                              <CurrencyDollar size={16} className="text-muted-foreground" />
                              <span className="text-sm">Credit Transactions</span>
                            </div>
                            <span className="text-sm font-medium">89</span>
                          </div>
                        </div>
                      </CardContent>
                    </Card>

                    {/* Popular Actions */}
                    <Card>
                      <CardHeader className="pb-2">
                        <CardTitle className="text-sm font-medium">Popular Actions</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="space-y-3">
                          <div className="flex items-center justify-between">
                            <span className="text-sm">Credit Redemption</span>
                            <div className="flex items-center gap-2">
                              <div className="w-16 h-2 bg-muted rounded-full">
                                <div className="w-3/4 h-full bg-primary rounded-full"></div>
                              </div>
                              <span className="text-sm font-medium">75%</span>
                            </div>
                          </div>
                          <div className="flex items-center justify-between">
                            <span className="text-sm">QR Code Scanning</span>
                            <div className="flex items-center gap-2">
                              <div className="w-16 h-2 bg-muted rounded-full">
                                <div className="w-3/5 h-full bg-primary rounded-full"></div>
                              </div>
                              <span className="text-sm font-medium">60%</span>
                            </div>
                          </div>
                          <div className="flex items-center justify-between">
                            <span className="text-sm">Credit Top-up</span>
                            <div className="flex items-center gap-2">
                              <div className="w-16 h-2 bg-muted rounded-full">
                                <div className="w-2/5 h-full bg-primary rounded-full"></div>
                              </div>
                              <span className="text-sm font-medium">40%</span>
                            </div>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  </div>

                  {/* Usage Timeline */}
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-sm font-medium">Recent Activity</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-4">
                        <div className="flex items-center gap-3 p-3 bg-muted rounded-lg">
                          <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                          <div className="flex-1">
                            <p className="text-sm font-medium">Customer redeemed $25 credit</p>
                            <p className="text-xs text-muted-foreground">Coffee Shop Downtown • 2 minutes ago</p>
                          </div>
                        </div>
                        <div className="flex items-center gap-3 p-3 bg-muted rounded-lg">
                          <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                          <div className="flex-1">
                            <p className="text-sm font-medium">New QR code generated</p>
                            <p className="text-xs text-muted-foreground">Bakery Main Street • 15 minutes ago</p>
                          </div>
                        </div>
                        <div className="flex items-center gap-3 p-3 bg-muted rounded-lg">
                          <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
                          <div className="flex-1">
                            <p className="text-sm font-medium">Customer added to shop</p>
                            <p className="text-xs text-muted-foreground">Restaurant Plaza • 1 hour ago</p>
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="quick-actions" className="space-y-4">
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-[#181510] flex items-center">
                    <CurrencyDollar className="mr-2 h-5 w-5" />
                    Quick Actions
                  </CardTitle>
                  <CardDescription>Common tasks and shortcuts</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <Card className="overflow-hidden hover:shadow-md transition-shadow">
                      <Link href="/dashboard/merchant/credits" className="block p-4">
                        <div className="flex items-center gap-4">
                          <div className="bg-[#e5ccb2] rounded-full p-3">
                            <CurrencyDollar size={24} className="text-[#181510]" />
                          </div>
                          <div>
                            <h3 className="font-medium text-[#181510]">Manage Credits</h3>
                            <p className="text-sm text-[#8a745c]">View and manage customer credits</p>
                          </div>
                        </div>
                      </Link>
                    </Card>

                    <Card className="overflow-hidden hover:shadow-md transition-shadow">
                      <Link href="/dashboard/merchant/codes" className="block p-4">
                        <div className="flex items-center gap-4">
                          <div className="bg-[#e5ccb2] rounded-full p-3">
                            <QrCode size={24} className="text-[#181510]" />
                          </div>
                          <div>
                            <h3 className="font-medium text-[#181510]">Credit Codes</h3>
                            <p className="text-sm text-[#8a745c]">Generate and manage credit codes</p>
                          </div>
                        </div>
                      </Link>
                    </Card>

                    <Card className="overflow-hidden hover:shadow-md transition-shadow">
                      <Link href="/dashboard/merchant/new" className="block p-4">
                        <div className="flex items-center gap-4">
                          <div className="bg-[#e5ccb2] rounded-full p-3">
                            <Plus size={24} className="text-[#181510]" />
                          </div>
                          <div>
                            <h3 className="font-medium text-[#181510]">Create Shop</h3>
                            <p className="text-sm text-[#8a745c]">Add a new merchant shop</p>
                          </div>
                        </div>
                      </Link>
                    </Card>

                    <Card className="overflow-hidden hover:shadow-md transition-shadow">
                      <Link href="/dashboard/merchant/profile" className="block p-4">
                        <div className="flex items-center gap-4">
                          <div className="bg-[#e5ccb2] rounded-full p-3">
                            <User size={24} className="text-[#181510]" />
                          </div>
                          <div>
                            <h3 className="font-medium text-[#181510]">Profile</h3>
                            <p className="text-sm text-[#8a745c]">View and edit your profile</p>
                          </div>
                        </div>
                      </Link>
                    </Card>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
      </div>

      {/* Mobile Navigation - Hidden on Desktop */}
      <div className="md:hidden">
        <div className="flex gap-1 border-t border-[#f1edea] bg-[#fbfaf9] px-2 pb-3 pt-2">
          <Link className="just flex flex-1 flex-col items-center justify-end gap-1 rounded-full text-[#181510]" href="/dashboard/merchant">
            <div className="text-[#181510] flex h-8 items-center justify-center">
              <House size={20} weight="fill" />
            </div>
            <p className="text-[#181510] text-xs font-medium leading-normal tracking-[0.015em]">Home</p>
          </Link>
          <Link className="just flex flex-1 flex-col items-center justify-end gap-1 text-[#8a745c]" href="/dashboard/merchant/analytics">
            <div className="text-[#8a745c] flex h-8 items-center justify-center">
              <ChartBar size={20} weight="regular" />
            </div>
            <p className="text-[#8a745c] text-xs font-medium leading-normal tracking-[0.015em]">Analytics</p>
          </Link>
          <Link className="just flex flex-1 flex-col items-center justify-end gap-1 text-[#8a745c]" href="/dashboard/merchant/credits">
            <div className="text-[#8a745c] flex h-8 items-center justify-center">
              <CurrencyDollar size={20} weight="regular" />
            </div>
            <p className="text-[#8a745c] text-xs font-medium leading-normal tracking-[0.015em]">Credits</p>
          </Link>
          <Link className="just flex flex-1 flex-col items-center justify-end gap-1 text-[#8a745c]" href="/dashboard/merchant/codes">
            <div className="text-[#8a745c] flex h-8 items-center justify-center">
              <QrCode size={20} weight="regular" />
            </div>
            <p className="text-[#8a745c] text-xs font-medium leading-normal tracking-[0.015em]">Codes</p>
          </Link>
          <Link className="just flex flex-1 flex-col items-center justify-end gap-1 text-[#8a745c]" href="/dashboard/merchant/profile">
            <div className="text-[#8a745c] flex h-8 items-center justify-center">
              <User size={20} weight="regular" />
            </div>
            <p className="text-[#8a745c] text-xs font-medium leading-normal tracking-[0.015em]">Profile</p>
          </Link>
        </div>
        <div className="h-5 bg-[#fbfaf9]"></div>
      </div>
    </div>
  );
}
