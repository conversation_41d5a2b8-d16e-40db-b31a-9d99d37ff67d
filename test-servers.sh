#!/bin/bash

echo "🧪 Testing ADC Credit System Servers..."
echo "========================================"

# Test backend server
echo "🔧 Testing Backend Server..."
cd backend
echo "Starting backend server..."
go run cmd/api/main.go &
BACKEND_PID=$!
echo "Backend PID: $BACKEND_PID"

# Wait for backend to start
sleep 3

# Test backend health
echo "Testing backend health endpoint..."
HEALTH_RESPONSE=$(curl -s http://localhost:8400/api/v1/health)
echo "Health Response: $HEALTH_RESPONSE"

if [[ $HEALTH_RESPONSE == *"ok"* ]]; then
    echo "✅ Backend server is working!"
else
    echo "❌ Backend server failed!"
fi

# Stop backend
kill $BACKEND_PID
echo "Backend server stopped."

cd ..

# Test frontend server
echo ""
echo "🎨 Testing Frontend Server..."
echo "Starting frontend server..."
bun run dev &
FRONTEND_PID=$!
echo "Frontend PID: $FRONTEND_PID"

# Wait for frontend to start
sleep 5

# Test frontend
echo "Testing frontend..."
FRONTEND_RESPONSE=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:3800)
echo "Frontend Response Code: $FRONTEND_RESPONSE"

if [[ $FRONTEND_RESPONSE == "200" ]]; then
    echo "✅ Frontend server is working!"
else
    echo "❌ Frontend server failed!"
fi

# Stop frontend
kill $FRONTEND_PID
echo "Frontend server stopped."

echo ""
echo "🎉 Server testing completed!"
echo "Both servers are ready for development."
