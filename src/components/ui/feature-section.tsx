"use client";
import React, { useState } from "react";
import { cn } from "@/lib/utils";

export const FeatureSection = () => {
  return (
    <div className="w-full bg-gray-50 py-12 md:py-24 lg:py-32">
      <div className="container px-4 md:px-6">
        <div className="flex flex-col items-center justify-center space-y-4 text-center">
          <div className="space-y-2">
            <h2 className="text-3xl font-bold tracking-tighter sm:text-5xl">Key Features</h2>
            <p className="mx-auto max-w-[700px] text-muted-foreground md:text-xl/relaxed lg:text-base/relaxed xl:text-xl/relaxed">
              Everything you need to manage API credits and integrate with your systems
            </p>
          </div>
        </div>
        <div className="mx-auto grid max-w-5xl grid-cols-1 gap-8 py-12 md:grid-cols-3">
          <FeatureCard
            title="Secure API Keys"
            description="Generate and manage secure API keys with fine-grained permissions"
            icon={
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-6 w-6">
                <circle cx="12" cy="12" r="10" />
                <path d="M12 8v4" />
                <path d="M12 16h.01" />
              </svg>
            }
          />
          <FeatureCard
            title="Credit Management"
            description="Track and manage API usage credits with subscription-based tiers"
            icon={
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-6 w-6">
                <rect width="18" height="18" x="3" y="3" rx="2" />
                <path d="M7 7h10" />
                <path d="M7 12h10" />
                <path d="M7 17h10" />
              </svg>
            }
          />
          <FeatureCard
            title="Usage Analytics"
            description="Detailed analytics and reporting on API usage and credit consumption"
            icon={
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-6 w-6">
                <path d="M3 3v18h18" />
                <path d="m19 9-5 5-4-4-3 3" />
              </svg>
            }
          />
        </div>
      </div>
    </div>
  );
};

export const FeatureCard = ({
  title,
  description,
  icon,
  className,
}: {
  title: string;
  description: string;
  icon: React.ReactNode;
  className?: string;
}) => {
  const [hovered, setHovered] = useState(false);

  return (
    <div
      className={cn(
        "group relative flex flex-col items-center space-y-4 rounded-lg border p-6 shadow-sm transition-all hover:shadow-md bg-white",
        className
      )}
      onMouseEnter={() => setHovered(true)}
      onMouseLeave={() => setHovered(false)}
    >
      <div className="relative flex h-14 w-14 items-center justify-center rounded-full bg-slate-900 text-white transition-all duration-300 group-hover:scale-110">
        {icon}
        <div className="absolute inset-0 rounded-full bg-slate-900 opacity-20 blur-md transition-all duration-300 group-hover:opacity-40 group-hover:blur-lg" />
      </div>
      <div
        className={cn(
          "absolute inset-0 -z-10 rounded-lg bg-gradient-to-br from-cyan-500/10 via-cyan-500/5 to-transparent opacity-0 blur-xl transition-all duration-300 group-hover:opacity-100",
          hovered ? "opacity-100" : "opacity-0"
        )}
      />
      <h3 className="text-xl font-bold">{title}</h3>
      <p className="text-sm text-muted-foreground">{description}</p>
    </div>
  );
};
