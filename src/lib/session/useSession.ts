'use client';

import { useSessionContext } from './sessionContext';

/**
 * Custom hook for accessing session data and functionality
 * This provides a simplified interface for components to interact with session data
 */
export function useAppSession() {
  const context = useSessionContext();
  
  if (!context) {
    throw new Error('useAppSession must be used within a SessionProvider');
  }
  
  return {
    // Session state
    isAuthenticated: context.isAuthenticated,
    accessToken: context.accessToken,
    user: context.user,
    loading: context.loading,
    error: context.error,
    
    // Session actions
    setAccessToken: context.setAccessToken,
    clearSession: context.clearSession,
    
    // Computed properties
    isAdmin: context.user?.role === 'admin',
    userId: context.user?.id,
    userEmail: context.user?.email,
    userName: context.user?.name,
  };
}

export default useAppSession;
