/**
 * ADC Credit SDK Merchant Module
 */
import { ApiClient } from './client';
import { APIResponse } from './types';
import { MerchantShop, ShopCustomer, CreditCode, ShopCreditTransaction } from '@/types';

/**
 * Create shop request
 */
export interface CreateShopRequest {
  name: string;
  description?: string;
  contact_email?: string;
  contact_phone?: string;
}

/**
 * Update shop request
 */
export interface UpdateShopRequest {
  name?: string;
  description?: string;
  contact_email?: string;
  contact_phone?: string;
}

/**
 * Add customer request
 */
export interface AddCustomerRequest {
  email: string;
  name: string;
  phone?: string;
}

/**
 * Add shop credit request
 */
export interface AddShopCreditRequest {
  customer_id: string;
  amount: number;
  description?: string;
}

/**
 * Generate credit code request
 */
export interface GenerateCreditCodeRequest {
  amount: number;
  description?: string;
  expires_at?: string;
}

/**
 * Merchant module for the ADC Credit SDK
 */
export class Merchant {
  private client: ApiClient;

  /**
   * Creates a new Merchant instance
   * @param client API client
   */
  constructor(client: ApiClient) {
    this.client = client;
  }

  /**
   * Gets all shops owned by the current merchant
   * @returns List of merchant shops
   */
  public async getShops(): Promise<APIResponse<MerchantShop[]>> {
    return this.client.get<MerchantShop[]>('/merchant/shops');
  }

  /**
   * Gets a specific shop by ID
   * @param id Shop ID
   * @returns Shop details
   */
  public async getShop(id: string): Promise<APIResponse<MerchantShop>> {
    return this.client.get<MerchantShop>(`/merchant/shops/${id}`);
  }

  /**
   * Gets a specific shop by slug
   * @param slug Shop slug
   * @returns Shop details
   */
  public async getShopBySlug(slug: string): Promise<APIResponse<MerchantShop>> {
    return this.client.get<MerchantShop>(`/merchant/shops/slug/${slug}`);
  }

  /**
   * Creates a new merchant shop
   * @param data Shop data
   * @returns Created shop
   */
  public async createShop(data: CreateShopRequest): Promise<APIResponse<MerchantShop>> {
    return this.client.post<MerchantShop>('/merchant/shops', data);
  }

  /**
   * Updates a merchant shop
   * @param id Shop ID
   * @param data Shop data to update
   * @returns Updated shop
   */
  public async updateShop(id: string, data: UpdateShopRequest): Promise<APIResponse<MerchantShop>> {
    return this.client.put<MerchantShop>(`/merchant/shops/${id}`, data);
  }

  /**
   * Deletes a merchant shop
   * @param id Shop ID
   * @returns Success message
   */
  public async deleteShop(id: string): Promise<APIResponse<{ message: string }>> {
    return this.client.delete<{ message: string }>(`/merchant/shops/${id}`);
  }

  /**
   * Gets all customers for a shop
   * @param shopId Shop ID
   * @returns List of shop customers
   */
  public async getShopCustomers(shopId: string): Promise<APIResponse<ShopCustomer[]>> {
    return this.client.get<ShopCustomer[]>(`/merchant/shops/${shopId}/customers`);
  }

  /**
   * Adds a customer to a shop
   * @param shopId Shop ID
   * @param data Customer data
   * @returns Created customer
   */
  public async addShopCustomer(shopId: string, data: AddCustomerRequest): Promise<APIResponse<ShopCustomer>> {
    return this.client.post<ShopCustomer>(`/merchant/shops/${shopId}/customers`, data);
  }

  /**
   * Adds credit to a customer's balance
   * @param shopId Shop ID
   * @param data Credit data
   * @returns Updated customer with new balance
   */
  public async addShopCredit(shopId: string, data: AddShopCreditRequest): Promise<APIResponse<ShopCustomer>> {
    return this.client.post<ShopCustomer>(`/merchant/shops/${shopId}/credits/add`, data);
  }

  /**
   * Generates a credit code for a shop
   * @param shopId Shop ID
   * @param data Credit code data
   * @returns Generated credit code
   */
  public async generateCreditCode(shopId: string, data: GenerateCreditCodeRequest): Promise<APIResponse<CreditCode>> {
    return this.client.post<CreditCode>(`/merchant/shops/${shopId}/codes/generate`, data);
  }

  /**
   * Gets all credit codes for a shop
   * @param shopId Shop ID
   * @returns List of credit codes
   */
  public async getCreditCodes(shopId: string): Promise<APIResponse<CreditCode[]>> {
    return this.client.get<CreditCode[]>(`/merchant/shops/${shopId}/codes`);
  }

  /**
   * Gets all credit transactions for a shop
   * @param shopId Shop ID
   * @returns List of credit transactions
   */
  public async getCreditTransactions(shopId: string): Promise<APIResponse<ShopCreditTransaction[]>> {
    return this.client.get<ShopCreditTransaction[]>(`/merchant/shops/${shopId}/transactions`);
  }

  /**
   * Creates an API key for a merchant shop
   * @param shopId Shop ID
   * @param name API key name
   * @param permissions Optional permissions
   * @returns Created API key
   */
  public async createShopApiKey(
    shopId: string, 
    name: string, 
    permissions?: string[]
  ): Promise<APIResponse<{ id: string; key: string; name: string }>> {
    return this.client.post<{ id: string; key: string; name: string }>(
      `/merchant/shops/${shopId}/apikeys`, 
      { name, permissions }
    );
  }

  /**
   * Gets all API keys for a merchant shop
   * @param shopId Shop ID
   * @returns List of API keys
   */
  public async getShopApiKeys(shopId: string): Promise<APIResponse<{ id: string; name: string; last_used?: string }[]>> {
    return this.client.get<{ id: string; name: string; last_used?: string }[]>(`/merchant/shops/${shopId}/apikeys`);
  }

  /**
   * Deletes an API key for a merchant shop
   * @param shopId Shop ID
   * @param keyId API key ID
   * @returns Success message
   */
  public async deleteShopApiKey(shopId: string, keyId: string): Promise<APIResponse<{ message: string }>> {
    return this.client.delete<{ message: string }>(`/merchant/shops/${shopId}/apikeys/${keyId}`);
  }
}
