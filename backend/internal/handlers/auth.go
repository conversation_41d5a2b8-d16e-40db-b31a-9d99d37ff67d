package handlers

import (
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"os"
	"time"

	"github.com/adc-credit/backend/internal/database"
	"github.com/adc-credit/backend/internal/models"
	"github.com/adc-credit/backend/internal/services"
	"github.com/gin-gonic/gin"
	"github.com/golang-jwt/jwt/v5"
	"github.com/google/uuid"
	"golang.org/x/crypto/bcrypt"
)

type GoogleAuthRequest struct {
	Token string `json:"token" binding:"required"`
}

type LoginResponse struct {
	Token        string      `json:"token"`
	RefreshToken string      `json:"refresh_token"`
	User         models.User `json:"user"`
}

// GoogleAuth handles Google OAuth authentication
func GoogleAuth(c *gin.Context) {
	var req GoogleAuthRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Log the received token for debugging
	fmt.Printf("Received Google token: %s\n", req.Token)

	// For Google OAuth tokens, we need to get user info from Google
	// In a production environment, you should verify the token with Google
	// For this implementation, we'll use the token to get user info from Google's userinfo endpoint

	// Get user info from Google
	googleUserInfo, err := getGoogleUserInfo(req.Token)
	if err != nil {
		c.JSON(http.StatusUnauthorized, gin.H{"error": fmt.Sprintf("Failed to verify Google token: %v", err)})
		return
	}

	// Extract user information
	googleID := googleUserInfo.Sub
	email := googleUserInfo.Email
	name := googleUserInfo.Name
	picture := googleUserInfo.Picture

	// If we couldn't get the Google ID, generate a fallback
	if googleID == "" {
		googleID = "google_" + uuid.New().String()
	}

	// Check if user exists by Google ID
	var user models.User
	result := database.DB.Where("google_id = ?", googleID).First(&user)

	// If user doesn't exist by Google ID, try to find by email
	if result.Error != nil {
		result = database.DB.Where("email = ?", email).First(&user)

		// If user exists by email but not Google ID, update the Google ID
		if result.Error == nil {
			user.GoogleID = googleID
			if err := database.DB.Save(&user).Error; err != nil {
				c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update user"})
				return
			}
		} else {
			// Create new user
			user = models.User{
				ID:       uuid.New(),
				Email:    email,
				Name:     name,
				Picture:  picture,
				GoogleID: googleID,
				Role:     "user",
			}

			if err := database.DB.Create(&user).Error; err != nil {
				c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create user"})
				return
			}

			// Create default subscription for new user
			var freeTier models.SubscriptionTier
			if err := database.DB.Where("name = ?", "Free").First(&freeTier).Error; err != nil {
				c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to find free tier"})
				return
			}

			subscription := models.Subscription{
				ID:                 uuid.New(),
				UserID:             user.ID,
				SubscriptionTierID: freeTier.ID,
				StartDate:          time.Now(),
				AutoRenew:          true,
				Status:             "active",
				CreditBalance:      freeTier.CreditLimit,
			}

			if err := database.DB.Create(&subscription).Error; err != nil {
				c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create subscription"})
				return
			}
		}
	}

	// Generate tokens
	token, refreshToken, err := generateTokens(user.ID.String())
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to generate tokens"})
		return
	}

	c.JSON(http.StatusOK, LoginResponse{
		Token:        token,
		RefreshToken: refreshToken,
		User:         user,
	})
}

// GoogleUserInfo represents the response from Google's userinfo endpoint
type GoogleUserInfo struct {
	Sub           string `json:"sub"`
	Name          string `json:"name"`
	GivenName     string `json:"given_name"`
	FamilyName    string `json:"family_name"`
	Profile       string `json:"profile"`
	Picture       string `json:"picture"`
	Email         string `json:"email"`
	EmailVerified bool   `json:"email_verified"`
	Locale        string `json:"locale"`
}

// getGoogleUserInfo gets user info from Google's userinfo endpoint
func getGoogleUserInfo(accessToken string) (*GoogleUserInfo, error) {
	// Make request to Google's userinfo endpoint
	req, err := http.NewRequest("GET", "https://www.googleapis.com/oauth2/v3/userinfo", nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %v", err)
	}

	// Add authorization header
	req.Header.Add("Authorization", fmt.Sprintf("Bearer %s", accessToken))

	// Send request
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to send request: %v", err)
	}
	defer resp.Body.Close()

	// Check response status
	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		return nil, fmt.Errorf("failed to get user info: %s", body)
	}

	// Parse response
	var userInfo GoogleUserInfo
	if err := json.NewDecoder(resp.Body).Decode(&userInfo); err != nil {
		return nil, fmt.Errorf("failed to parse response: %v", err)
	}

	return &userInfo, nil
}

// Login handles email/password login
func Login(c *gin.Context) {
	type LoginRequest struct {
		Email    string `json:"email" binding:"required,email"`
		Password string `json:"password" binding:"required"`
	}

	var req LoginRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Find user by email
	var user models.User
	if err := database.DB.Where("email = ?", req.Email).First(&user).Error; err != nil {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid email or password"})
		return
	}

	// Check if the user has a password (might be a Google-only user)
	if user.Password == "" {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "This account doesn't have a password set"})
		return
	}

	// Verify password
	if err := bcrypt.CompareHashAndPassword([]byte(user.Password), []byte(req.Password)); err != nil {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid email or password"})
		return
	}

	// Generate tokens
	token, refreshToken, err := generateTokens(user.ID.String())
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to generate tokens"})
		return
	}

	c.JSON(http.StatusOK, LoginResponse{
		Token:        token,
		RefreshToken: refreshToken,
		User:         user,
	})
}

// RegisterUser creates a new user with email and password
func RegisterUser(c *gin.Context) {
	type RegisterRequest struct {
		Email    string `json:"email" binding:"required,email"`
		Password string `json:"password" binding:"required,min=8"`
		Name     string `json:"name" binding:"required"`
	}

	var req RegisterRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Check if user already exists
	var existingUser models.User
	result := database.DB.Where("email = ?", req.Email).First(&existingUser)
	if result.Error == nil {
		c.JSON(http.StatusConflict, gin.H{"error": "User with this email already exists"})
		return
	}

	// Hash the password
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(req.Password), bcrypt.DefaultCost)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to hash password"})
		return
	}

	// Create new user
	user := models.User{
		ID:       uuid.New(),
		Email:    req.Email,
		Name:     req.Name,
		Password: string(hashedPassword),
		Role:     "user",
		GoogleID: "email_" + uuid.New().String(), // Set a unique google_id for email registrations
	}

	if err := database.DB.Create(&user).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create user"})
		return
	}

	// Create default subscription for new user
	var freeTier models.SubscriptionTier
	if err := database.DB.Where("name = ?", "Free").First(&freeTier).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to find free tier"})
		return
	}

	subscription := models.Subscription{
		ID:                 uuid.New(),
		UserID:             user.ID,
		SubscriptionTierID: freeTier.ID,
		StartDate:          time.Now(),
		AutoRenew:          true,
		Status:             "active",
		CreditBalance:      freeTier.CreditLimit,
	}

	if err := database.DB.Create(&subscription).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create subscription"})
		return
	}

	// Generate tokens
	token, refreshToken, err := generateTokens(user.ID.String())
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to generate tokens"})
		return
	}

	c.JSON(http.StatusCreated, LoginResponse{
		Token:        token,
		RefreshToken: refreshToken,
		User:         user,
	})
}

// ForgotPassword initiates the password reset process
func ForgotPassword(c *gin.Context) {
	type ForgotPasswordRequest struct {
		Email string `json:"email" binding:"required,email"`
	}

	var req ForgotPasswordRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Check if user exists
	var user models.User
	if err := database.DB.Where("email = ?", req.Email).First(&user).Error; err != nil {
		// Don't reveal that the email doesn't exist for security reasons
		c.JSON(http.StatusOK, gin.H{"message": "If your email is registered, you will receive a password reset link"})
		return
	}

	// Generate a reset token
	resetToken := uuid.New().String()

	// Set token expiration (1 hour from now)
	expiresAt := time.Now().Add(time.Hour)

	// Store the token in the database
	user.ResetToken = resetToken
	user.ResetTokenExpiresAt = &expiresAt

	if err := database.DB.Save(&user).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to process password reset"})
		return
	}

	// Send password reset email
	emailService := services.NewEmailService()
	if err := emailService.SendPasswordResetEmail(user.Email, resetToken); err != nil {
		// Log the error but don't fail the request for security reasons
		fmt.Printf("Failed to send password reset email: %v\n", err)
	}

	// For development, also include debug link
	var response gin.H
	if os.Getenv("ENVIRONMENT") == "development" {
		resetLink := fmt.Sprintf("%s/auth/reset-password?token=%s", os.Getenv("FRONTEND_URL"), resetToken)
		response = gin.H{
			"message":    "Password reset link has been sent to your email",
			"debug_link": resetLink, // Only in development
		}
	} else {
		response = gin.H{
			"message": "If your email is registered, you will receive a password reset link",
		}
	}

	c.JSON(http.StatusOK, response)
}

// ResetPassword completes the password reset process
func ResetPassword(c *gin.Context) {
	type ResetPasswordRequest struct {
		Token    string `json:"token" binding:"required"`
		Password string `json:"password" binding:"required,min=8"`
	}

	var req ResetPasswordRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Find user by reset token
	var user models.User
	if err := database.DB.Where("reset_token = ?", req.Token).First(&user).Error; err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid or expired reset token"})
		return
	}

	// Check if token is expired
	if user.ResetTokenExpiresAt == nil || user.ResetTokenExpiresAt.Before(time.Now()) {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Reset token has expired"})
		return
	}

	// Hash the new password
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(req.Password), bcrypt.DefaultCost)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to hash password"})
		return
	}

	// Update user's password and clear reset token
	user.Password = string(hashedPassword)
	user.ResetToken = ""
	user.ResetTokenExpiresAt = nil

	if err := database.DB.Save(&user).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update password"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Password has been reset successfully"})
}

// RefreshToken handles token refresh
func RefreshToken(c *gin.Context) {
	type RefreshRequest struct {
		RefreshToken string `json:"refresh_token" binding:"required"`
	}

	var req RefreshRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Parse the refresh token
	claims := &jwt.RegisteredClaims{}
	token, err := jwt.ParseWithClaims(req.RefreshToken, claims, func(token *jwt.Token) (interface{}, error) {
		return []byte(os.Getenv("JWT_REFRESH_SECRET")), nil
	})

	if err != nil || !token.Valid {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid refresh token"})
		return
	}

	// Get user ID from token
	userID := claims.Subject

	// Generate new tokens
	newToken, newRefreshToken, err := generateTokens(userID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to generate tokens"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"token":         newToken,
		"refresh_token": newRefreshToken,
	})
}

// generateTokens creates a new JWT token and refresh token
func generateTokens(userID string) (string, string, error) {
	// Create access token
	token := jwt.NewWithClaims(jwt.SigningMethodHS256, jwt.RegisteredClaims{
		Subject:   userID,
		ExpiresAt: jwt.NewNumericDate(time.Now().Add(time.Hour * 24)), // 24 hours
		IssuedAt:  jwt.NewNumericDate(time.Now()),
	})

	tokenString, err := token.SignedString([]byte(os.Getenv("JWT_SECRET")))
	if err != nil {
		return "", "", err
	}

	// Create refresh token
	refreshToken := jwt.NewWithClaims(jwt.SigningMethodHS256, jwt.RegisteredClaims{
		Subject:   userID,
		ExpiresAt: jwt.NewNumericDate(time.Now().Add(time.Hour * 24 * 30)), // 30 days
		IssuedAt:  jwt.NewNumericDate(time.Now()),
	})

	refreshTokenString, err := refreshToken.SignedString([]byte(os.Getenv("JWT_REFRESH_SECRET")))
	if err != nil {
		return "", "", err
	}

	return tokenString, refreshTokenString, nil
}
