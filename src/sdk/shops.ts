import { APIResponse, Shop, ShopBranch, ShopCustomer, CreditCode, APIKey, ExternalUser } from '../types';
import { ApiClient } from './client';

// Request types for shop operations
export interface CreateShopRequest {
  name: string;
  description?: string;
  shop_type: 'retail' | 'api_service' | 'enterprise';
  contact_email?: string;
  contact_phone?: string;
}

export interface UpdateShopRequest {
  name?: string;
  description?: string;
  shop_type?: 'retail' | 'api_service' | 'enterprise';
  contact_email?: string;
  contact_phone?: string;
}

export interface CreateShopBranchRequest {
  name: string;
  description?: string;
  branch_type: 'location' | 'department' | 'division';
  contact_email?: string;
  contact_phone?: string;
  address?: string;
}

export interface UpdateShopBranchRequest {
  name?: string;
  description?: string;
  branch_type?: 'location' | 'department' | 'division';
  contact_email?: string;
  contact_phone?: string;
  address?: string;
}

export interface CreateShopAPIKeyRequest {
  name: string;
  permissions?: string[];
}

export interface CreateShopExternalUserRequest {
  name: string;
  email: string;
  monthly_credits?: number;
}

export interface AddShopCreditRequest {
  customer_id: string;
  amount: number;
  description?: string;
}

export interface GenerateCreditCodeRequest {
  amount: number;
  description?: string;
  expires_at?: string;
}

/**
 * Unified Shops SDK Module
 * Handles all shop operations including retail, API service, and enterprise shops
 */
export class Shops {
  constructor(private client: ApiClient) {}

  // ==================== SHOP MANAGEMENT ====================

  /**
   * Get all shops for the current user
   */
  public async getShops(): Promise<APIResponse<Shop[]>> {
    return this.client.get<Shop[]>('/shops');
  }

  /**
   * Create a new shop
   */
  public async createShop(data: CreateShopRequest): Promise<APIResponse<Shop>> {
    return this.client.post<Shop>('/shops', data);
  }

  /**
   * Get a specific shop by ID
   */
  public async getShop(shopId: string): Promise<APIResponse<Shop>> {
    return this.client.get<Shop>(`/shops/${shopId}`);
  }

  /**
   * Get a specific shop by slug
   */
  public async getShopBySlug(slug: string): Promise<APIResponse<Shop>> {
    return this.client.get<Shop>(`/shops/slug/${slug}`);
  }

  /**
   * Update an existing shop
   */
  public async updateShop(shopId: string, data: UpdateShopRequest): Promise<APIResponse<Shop>> {
    return this.client.put<Shop>(`/shops/${shopId}`, data);
  }

  /**
   * Delete a shop
   */
  public async deleteShop(shopId: string): Promise<APIResponse<{ message: string }>> {
    return this.client.delete<{ message: string }>(`/shops/${shopId}`);
  }

  // ==================== BRANCH MANAGEMENT ====================

  /**
   * Get all branches for a shop
   */
  public async getShopBranches(shopId: string): Promise<APIResponse<ShopBranch[]>> {
    return this.client.get<ShopBranch[]>(`/shops/branches?shop_id=${shopId}`);
  }

  /**
   * Create a new branch for a shop
   */
  public async createShopBranch(shopId: string, data: CreateShopBranchRequest): Promise<APIResponse<ShopBranch>> {
    return this.client.post<ShopBranch>(`/shops/branches?shop_id=${shopId}`, data);
  }

  /**
   * Get a specific branch by ID
   */
  public async getShopBranch(shopId: string, branchId: string): Promise<APIResponse<ShopBranch>> {
    return this.client.get<ShopBranch>(`/shops/branches/${branchId}?shop_id=${shopId}`);
  }

  /**
   * Update an existing branch
   */
  public async updateShopBranch(shopId: string, branchId: string, data: UpdateShopBranchRequest): Promise<APIResponse<ShopBranch>> {
    return this.client.put<ShopBranch>(`/shops/branches/${branchId}?shop_id=${shopId}`, data);
  }

  /**
   * Delete a branch
   */
  public async deleteShopBranch(shopId: string, branchId: string): Promise<APIResponse<{ message: string }>> {
    return this.client.delete<{ message: string }>(`/shops/branches/${branchId}?shop_id=${shopId}`);
  }

  // ==================== API KEY MANAGEMENT ====================

  /**
   * Get all API keys for a shop
   */
  public async getShopAPIKeys(shopId: string): Promise<APIResponse<APIKey[]>> {
    return this.client.get<APIKey[]>(`/shops/${shopId}/apikeys`);
  }

  /**
   * Create a new API key for a shop
   */
  public async createShopAPIKey(shopId: string, data: CreateShopAPIKeyRequest): Promise<APIResponse<APIKey>> {
    return this.client.post<APIKey>(`/shops/${shopId}/apikeys`, data);
  }

  /**
   * Get all API keys for a branch
   */
  public async getShopBranchAPIKeys(branchId: string): Promise<APIResponse<APIKey[]>> {
    return this.client.get<APIKey[]>(`/shops/branches/${branchId}/apikeys`);
  }

  /**
   * Create a new API key for a branch
   */
  public async createShopBranchAPIKey(branchId: string, data: CreateShopAPIKeyRequest): Promise<APIResponse<APIKey>> {
    return this.client.post<APIKey>(`/shops/branches/${branchId}/apikeys`, data);
  }

  /**
   * Delete an API key
   */
  public async deleteShopAPIKey(shopId: string, keyId: string): Promise<APIResponse<{ message: string }>> {
    return this.client.delete<{ message: string }>(`/shops/${shopId}/apikeys/${keyId}`);
  }

  /**
   * Delete a branch API key
   */
  public async deleteShopBranchAPIKey(branchId: string, keyId: string): Promise<APIResponse<{ message: string }>> {
    return this.client.delete<{ message: string }>(`/shops/branches/${branchId}/apikeys/${keyId}`);
  }

  // ==================== CUSTOMER MANAGEMENT (RETAIL SHOPS) ====================

  /**
   * Get all customers for a shop
   */
  public async getShopCustomers(shopId: string): Promise<APIResponse<ShopCustomer[]>> {
    return this.client.get<ShopCustomer[]>(`/shops/${shopId}/customers`);
  }

  /**
   * Add a new customer to a shop
   */
  public async addShopCustomer(shopId: string, data: { name: string; email: string; phone?: string }): Promise<APIResponse<ShopCustomer>> {
    return this.client.post<ShopCustomer>(`/shops/${shopId}/customers`, data);
  }

  /**
   * Add credit to a customer's account
   */
  public async addShopCredit(shopId: string, data: AddShopCreditRequest): Promise<APIResponse<ShopCustomer>> {
    return this.client.post<ShopCustomer>(`/shops/${shopId}/customers/${data.customer_id}/credits`, data);
  }

  // ==================== EXTERNAL USER MANAGEMENT (API SERVICE SHOPS) ====================

  /**
   * Get all external users for a shop
   */
  public async getShopExternalUsers(shopId: string): Promise<APIResponse<ExternalUser[]>> {
    return this.client.get<ExternalUser[]>(`/shops/${shopId}/external-users`);
  }

  /**
   * Create a new external user for a shop
   */
  public async createShopExternalUser(shopId: string, data: CreateShopExternalUserRequest): Promise<APIResponse<ExternalUser>> {
    return this.client.post<ExternalUser>(`/shops/${shopId}/external-users`, data);
  }

  /**
   * Add credits to an external user
   */
  public async addCreditsToShopExternalUser(shopId: string, userId: string, amount: number): Promise<APIResponse<ExternalUser>> {
    return this.client.post<ExternalUser>(`/shops/${shopId}/external-users/${userId}/credits/add`, { amount });
  }

  /**
   * Reduce credits from an external user
   */
  public async reduceCreditsFromShopExternalUser(shopId: string, userId: string, amount: number): Promise<APIResponse<ExternalUser>> {
    return this.client.post<ExternalUser>(`/shops/${shopId}/external-users/${userId}/credits/reduce`, { amount });
  }

  // ==================== CREDIT CODE MANAGEMENT (RETAIL SHOPS) ====================

  /**
   * Get all credit codes for a shop
   */
  public async getCreditCodes(shopId: string): Promise<APIResponse<CreditCode[]>> {
    return this.client.get<CreditCode[]>(`/shops/${shopId}/credit-codes`);
  }

  /**
   * Generate a new credit code
   */
  public async generateCreditCode(shopId: string, data: GenerateCreditCodeRequest): Promise<APIResponse<CreditCode>> {
    return this.client.post<CreditCode>(`/shops/${shopId}/credit-codes`, data);
  }

  /**
   * Generate QR code for a credit code
   */
  public async generateQRCode(shopId: string, data: GenerateCreditCodeRequest): Promise<APIResponse<{ qr_code: string; credit_code: CreditCode }>> {
    return this.client.post<{ qr_code: string; credit_code: CreditCode }>(`/shops/${shopId}/credit-codes/qr`, data);
  }

  // ==================== SHOP STATISTICS ====================

  /**
   * Get shop statistics
   */
  public async getShopStats(shopId: string): Promise<APIResponse<{
    total_customers: number;
    total_credit_issued: number;
    total_credit_redeemed: number;
    total_transactions: number;
  }>> {
    return this.client.get(`/shops/${shopId}/stats`);
  }

  /**
   * Get shop transactions
   */
  public async getShopTransactions(shopId: string): Promise<APIResponse<any[]>> {
    return this.client.get<any[]>(`/shops/${shopId}/transactions`);
  }
}
