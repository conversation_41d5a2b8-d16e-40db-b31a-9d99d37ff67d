package database

import (
	"fmt"
	"regexp"
	"strings"

	"github.com/adc-credit/backend/internal/models"
	"github.com/google/uuid"
)

// RunMigrations runs all database migrations
func RunMigrations() error {
	// Run auto migrations in correct order (dependencies first)
	err := DB.AutoMigrate(
		// Core models first (no dependencies)
		&models.User{},
		&models.SubscriptionTier{},

		// Models that depend on User
		&models.Organization{},
		// &models.Shop{},         // Temporarily disabled - already migrated manually
		&models.MerchantShop{}, // Depends on User

		// Models that depend on Organization/Shop
		&models.Branch{}, // Depends on Organization
		// &models.ShopBranch{},            // Temporarily disabled - already migrated manually
		// &models.APIKey{},       // Temporarily disabled - depends on Shop, ShopBranch
		// &models.Subscription{}, // Temporarily disabled - depends on Shop
		// &models.ShopCustomer{},          // Temporarily disabled - depends on Shop
		// &models.CreditCode{},            // Temporarily disabled - depends on Shop
		// &models.ShopCreditTransaction{}, // Temporarily disabled - depends on Shop

		// Other models
		&models.Usage{},
		&models.Transaction{},
		&models.Webhook{},
		&models.WebhookDelivery{},
		&models.AnalyticsData{},
	)
	if err != nil {
		return fmt.Errorf("failed to run auto migrations: %w", err)
	}

	// Run custom migrations
	if err := migrateShopSlugs(); err != nil {
		return fmt.Errorf("failed to migrate shop slugs: %w", err)
	}

	return nil
}

// migrateShopSlugs adds slugs to existing shops
func migrateShopSlugs() error {
	// Check if we need to run this migration
	var count int64
	DB.Model(&models.MerchantShop{}).Where("slug = ?", "").Or("slug IS NULL").Count(&count)

	if count == 0 {
		// No shops without slugs, no need to run migration
		return nil
	}

	// Get all shops without slugs
	var shops []models.MerchantShop
	if err := DB.Where("slug = ?", "").Or("slug IS NULL").Find(&shops).Error; err != nil {
		return err
	}

	// Update each shop with a slug
	for _, shop := range shops {
		slug := generateSlug(shop.Name)
		slug = ensureUniqueSlug(slug, shop.ID)

		if err := DB.Model(&models.MerchantShop{}).Where("id = ?", shop.ID).Update("slug", slug).Error; err != nil {
			return err
		}
	}

	// Now make the slug column NOT NULL
	if err := DB.Exec("ALTER TABLE merchant_shops ALTER COLUMN slug SET NOT NULL").Error; err != nil {
		return err
	}

	return nil
}

// generateSlug creates a URL-friendly slug from a shop name
func generateSlug(name string) string {
	// Convert to lowercase
	slug := strings.ToLower(name)

	// Replace spaces with hyphens
	slug = strings.ReplaceAll(slug, " ", "-")

	// Remove any character that is not alphanumeric or hyphen
	reg := regexp.MustCompile("[^a-z0-9-]")
	slug = reg.ReplaceAllString(slug, "")

	// Remove multiple consecutive hyphens
	reg = regexp.MustCompile("-+")
	slug = reg.ReplaceAllString(slug, "-")

	// Trim hyphens from beginning and end
	slug = strings.Trim(slug, "-")

	// If slug is empty, use a default
	if slug == "" {
		slug = "shop"
	}

	return slug
}

// ensureUniqueSlug makes sure the slug is unique by appending a number if necessary
func ensureUniqueSlug(slug string, shopID uuid.UUID) string {
	baseSlug := slug
	counter := 1

	for {
		// Check if slug exists for another shop
		var existingShop models.MerchantShop
		err := DB.Where("slug = ? AND id != ?", slug, shopID).First(&existingShop).Error

		// If no shop found with this slug, it's unique
		if err != nil {
			return slug
		}

		// Append counter to make it unique
		slug = fmt.Sprintf("%s-%d", baseSlug, counter)
		counter++
	}
}
