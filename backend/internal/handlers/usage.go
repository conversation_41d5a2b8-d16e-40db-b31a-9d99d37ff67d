package handlers

import (
	"net/http"
	"time"

	"github.com/adc-credit/backend/internal/database"
	"github.com/adc-credit/backend/internal/models"
	"github.com/gin-gonic/gin"
)

// GetUsage returns usage data for the current user
func GetUsage(c *gin.Context) {
	user := c.Must<PERSON>et("user").(models.User)

	// Get query parameters for filtering
	startDateStr := c.Query("start_date")
	endDateStr := c.Query("end_date")
	apiKeyID := c.Query("api_key_id")

	// Default to last 30 days if no dates provided
	startDate := time.Now().AddDate(0, 0, -30)
	endDate := time.Now()

	// Parse dates if provided
	if startDateStr != "" {
		if parsedDate, err := time.Parse("2006-01-02", startDateStr); err == nil {
			startDate = parsedDate
		}
	}
	if endDateStr != "" {
		if parsedDate, err := time.Parse("2006-01-02", endDateStr); err == nil {
			endDate = parsedDate.Add(24 * time.Hour) // Include the entire end date
		}
	}

	// Build query
	query := database.DB.Table("usages").
		Joins("JOIN api_keys ON usages.api_key_id = api_keys.id").
		Where("api_keys.user_id = ?", user.ID).
		Where("usages.timestamp BETWEEN ? AND ?", startDate, endDate)

	// Filter by API key if provided
	if apiKeyID != "" {
		query = query.Where("api_keys.id = ?", apiKeyID)
	}

	// Get usage data
	var usages []models.Usage
	if err := query.Find(&usages).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch usage data"})
		return
	}

	c.JSON(http.StatusOK, usages)
}

// GetUsageSummary returns a summary of usage data
func GetUsageSummary(c *gin.Context) {
	user := c.MustGet("user").(models.User)

	// Get query parameters for filtering
	period := c.DefaultQuery("period", "month") // day, week, month, year
	startDateStr := c.Query("start_date")
	endDateStr := c.Query("end_date")

	// Default to last 30 days if no dates provided
	startDate := time.Now().AddDate(0, 0, -30)
	endDate := time.Now()

	// Parse dates if provided
	if startDateStr != "" {
		if parsedDate, err := time.Parse("2006-01-02", startDateStr); err == nil {
			startDate = parsedDate
		}
	}
	if endDateStr != "" {
		if parsedDate, err := time.Parse("2006-01-02", endDateStr); err == nil {
			endDate = parsedDate.Add(24 * time.Hour) // Include the entire end date
		}
	}

	// Determine time format based on period
	// Note: timeFormat is defined but not used directly - it's used indirectly in the SQL queries
	_ = func() string {
		switch period {
		case "day":
			return "2006-01-02 15:00" // Hourly
		case "week":
			return "2006-01-02" // Daily
		case "month":
			return "2006-01-02" // Daily
		case "year":
			return "2006-01" // Monthly
		default:
			return "2006-01-02" // Default to daily
		}
	}()

	// Get API keys for the user
	var apiKeys []models.APIKey
	if err := database.DB.Where("user_id = ?", user.ID).Find(&apiKeys).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch API keys"})
		return
	}

	// Prepare response
	type EndpointSummary struct {
		Endpoint     string `json:"endpoint"`
		TotalUsage   int    `json:"total_usage"`
		TotalCredits int    `json:"total_credits"`
	}

	type APIKeySummary struct {
		ID           string            `json:"id"`
		Name         string            `json:"name"`
		TotalUsage   int               `json:"total_usage"`
		TotalCredits int               `json:"total_credits"`
		Endpoints    []EndpointSummary `json:"endpoints"`
	}

	type TimePeriodSummary struct {
		Period       string `json:"period"`
		TotalUsage   int    `json:"total_usage"`
		TotalCredits int    `json:"total_credits"`
	}

	response := struct {
		TotalUsage   int                 `json:"total_usage"`
		TotalCredits int                 `json:"total_credits"`
		APIKeys      []APIKeySummary     `json:"api_keys"`
		TimeSeries   []TimePeriodSummary `json:"time_series"`
	}{
		APIKeys:    make([]APIKeySummary, 0),
		TimeSeries: make([]TimePeriodSummary, 0),
	}

	// Get total usage
	database.DB.Table("usages").
		Joins("JOIN api_keys ON usages.api_key_id = api_keys.id").
		Where("api_keys.user_id = ?", user.ID).
		Where("usages.timestamp BETWEEN ? AND ?", startDate, endDate).
		Select("COUNT(*) as total_usage, SUM(credits) as total_credits").
		Row().Scan(&response.TotalUsage, &response.TotalCredits)

	// Get usage by API key
	for _, apiKey := range apiKeys {
		apiKeySummary := APIKeySummary{
			ID:        apiKey.ID.String(),
			Name:      apiKey.Name,
			Endpoints: make([]EndpointSummary, 0),
		}

		// Get total usage for this API key
		database.DB.Table("usages").
			Where("api_key_id = ?", apiKey.ID).
			Where("timestamp BETWEEN ? AND ?", startDate, endDate).
			Select("COUNT(*) as total_usage, SUM(credits) as total_credits").
			Row().Scan(&apiKeySummary.TotalUsage, &apiKeySummary.TotalCredits)

		// Get usage by endpoint for this API key
		type EndpointResult struct {
			Endpoint     string
			TotalUsage   int
			TotalCredits int
		}
		var endpointResults []EndpointResult
		database.DB.Table("usages").
			Where("api_key_id = ?", apiKey.ID).
			Where("timestamp BETWEEN ? AND ?", startDate, endDate).
			Select("endpoint, COUNT(*) as total_usage, SUM(credits) as total_credits").
			Group("endpoint").
			Find(&endpointResults)

		for _, er := range endpointResults {
			apiKeySummary.Endpoints = append(apiKeySummary.Endpoints, EndpointSummary{
				Endpoint:     er.Endpoint,
				TotalUsage:   er.TotalUsage,
				TotalCredits: er.TotalCredits,
			})
		}

		response.APIKeys = append(response.APIKeys, apiKeySummary)
	}

	// Get time series data
	type TimeResult struct {
		Period       string
		TotalUsage   int
		TotalCredits int
	}
	var timeResults []TimeResult

	timeQuery := database.DB.Table("usages").
		Joins("JOIN api_keys ON usages.api_key_id = api_keys.id").
		Where("api_keys.user_id = ?", user.ID).
		Where("usages.timestamp BETWEEN ? AND ?", startDate, endDate)

	switch period {
	case "day":
		timeQuery = timeQuery.Select("DATE_FORMAT(timestamp, '%Y-%m-%d %H:00') as period, COUNT(*) as total_usage, SUM(credits) as total_credits")
	case "week", "month":
		timeQuery = timeQuery.Select("DATE(timestamp) as period, COUNT(*) as total_usage, SUM(credits) as total_credits")
	case "year":
		timeQuery = timeQuery.Select("DATE_FORMAT(timestamp, '%Y-%m') as period, COUNT(*) as total_usage, SUM(credits) as total_credits")
	}

	timeQuery.Group("period").Order("period").Find(&timeResults)

	for _, tr := range timeResults {
		response.TimeSeries = append(response.TimeSeries, TimePeriodSummary{
			Period:       tr.Period,
			TotalUsage:   tr.TotalUsage,
			TotalCredits: tr.TotalCredits,
		})
	}

	c.JSON(http.StatusOK, response)
}
