package models

import (
	"database/sql/driver"
	"encoding/json"
	"errors"
)

// StringSlice is a custom type that implements the Scanner and Valuer interfaces
// to handle conversion between a string in the database and a slice of strings in Go
type StringSlice []string

// <PERSON><PERSON> implements the sql.Scanner interface
func (ss *StringSlice) Scan(value interface{}) error {
	if value == nil {
		*ss = StringSlice{}
		return nil
	}

	switch v := value.(type) {
	case []byte:
		// Try to unmarshal as JSON array first
		var slice []string
		if err := json.Unmarshal(v, &slice); err == nil {
			*ss = slice
			return nil
		}
		// If not a JSON array, treat as a single string
		*ss = StringSlice{string(v)}
		return nil
	case string:
		// Try to unmarshal as JSON array first
		var slice []string
		if err := json.Unmarshal([]byte(v), &slice); err == nil {
			*ss = slice
			return nil
		}
		// If not a JSON array, treat as a single string
		*ss = StringSlice{v}
		return nil
	default:
		return errors.New("unsupported Scan, storing driver.Value type into StringSlice")
	}
}

// Value implements the driver.Valuer interface
func (ss StringSlice) Value() (driver.Value, error) {
	if len(ss) == 0 {
		return "[]", nil
	}
	
	// Convert to JSON array
	bytes, err := json.Marshal(ss)
	if err != nil {
		return nil, err
	}
	return string(bytes), nil
}
