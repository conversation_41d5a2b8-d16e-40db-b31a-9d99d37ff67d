"use client";

import { useState } from "react";
import { Card, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Subscription, SubscriptionTier, Shop, MerchantShop } from "@/types";
import { toast } from "sonner";
import { useRouter } from "next/navigation";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import {
  useGetShopsQuery,
  useGetMerchantShopsQuery,
  useGetSubscriptionTiersQuery
} from "@/lib/api/apiSlice";
import { ShoppingBag, CreditCard, Check, AlertCircle, Users, BarChart, Zap, Globe, Shield } from "lucide-react";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";

interface ShopSubscriptionPanelProps {
  subscriptions: Subscription[];
  createSubscription: any;
}

export default function ShopSubscriptionPanel({
  subscriptions,
  createSubscription: createSubscriptionFn
}: ShopSubscriptionPanelProps) {
  const router = useRouter();
  const [selectedShop, setSelectedShop] = useState<string>("");
  const [selectedTier, setSelectedTier] = useState<any | null>(null);
  const [confirmDialogOpen, setConfirmDialogOpen] = useState(false);
  const [isUpgrading, setIsUpgrading] = useState(false);

  // Fetch shops (both new unified shops and legacy merchant shops)
  const {
    data: shops = [],
    isLoading: isLoadingShops,
  } = useGetShopsQuery();

  const {
    data: merchantShops = [],
    isLoading: isLoadingMerchantShops,
  } = useGetMerchantShopsQuery();

  const {
    data: subscriptionTiers = [],
    isLoading: isLoadingTiers,
  } = useGetSubscriptionTiersQuery();

  // Combine shops and merchant shops for display
  const allShops = [
    ...shops.map(shop => ({ ...shop, type: 'shop' as const })),
    ...merchantShops.map(shop => ({ ...shop, type: 'merchant' as const }))
  ];

  // Get the selected shop object
  const shop = allShops.find(s => s.id === selectedShop);

  // Define shop subscription tiers with updated features
  const shopTiers = [
    {
      id: 1,
      name: "Shop Starter",
      description: "Essential features for small shops",
      price: 9.99,
      credit_limit: 5000,
      features: [
        "Up to 100 customers",
        "Basic credit management",
        "5,000 monthly credits",
        "Basic analytics",
        "Single shop/location",
        "Email support",
        "Standard QR code generation",
        "1 API key"
      ],
      icon: <Users className="h-5 w-5 text-blue-500" />
    },
    {
      id: 2,
      name: "Shop Professional",
      description: "Advanced features for growing shops",
      price: 29.99,
      credit_limit: 25000,
      features: [
        "Up to 1,000 customers",
        "Advanced credit management",
        "25,000 monthly credits",
        "Enhanced analytics",
        "Up to 5 shops/locations",
        "Priority email support",
        "Custom QR code branding",
        "API access with higher limits",
        "Bulk credit operations",
        "5 API keys",
        "Branch management"
      ],
      icon: <BarChart className="h-5 w-5 text-purple-500" />
    },
    {
      id: 3,
      name: "Shop Enterprise",
      description: "Complete solution for large businesses",
      price: 99.99,
      credit_limit: 100000,
      features: [
        "Unlimited customers",
        "Complete credit management suite",
        "100,000+ monthly credits",
        "Advanced analytics and reporting",
        "Unlimited shops/locations",
        "24/7 dedicated support",
        "White-labeled QR codes",
        "Full API access with enterprise limits",
        "Multi-user access with permissions",
        "Custom integration support",
        "Unlimited API keys",
        "Advanced branch management",
        "Custom subscription limits"
      ],
      icon: <Globe className="h-5 w-5 text-green-500" />
    }
  ];

  // Handle upgrade confirmation
  const handleUpgradeConfirm = async () => {
    if (!selectedShop || !selectedTier) return;

    setIsUpgrading(true);
    try {
      const shopObj = allShops.find(s => s.id === selectedShop);
      
      // For free tiers, create subscription directly
      if (selectedTier.price === 0) {
        const subscriptionData = {
          subscription_tier_id: selectedTier.id,
          auto_renew: true,
          subscription_type: "shop",
          ...(shopObj?.type === 'shop' ? { shop_id: selectedShop } : { merchant_shop_id: selectedShop })
        };

        await createSubscriptionFn(subscriptionData);

        toast.success(`Successfully upgraded ${shopObj?.name} to ${selectedTier.name} plan`);
        setConfirmDialogOpen(false);
      } else {
        // For paid tiers, create a Stripe checkout session using the proxy API
        const response = await fetch('/api/stripe/create-checkout-session', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            tier_id: selectedTier.id,
            subscription_type: 'shop',
            ...(shopObj?.type === 'shop' ? { shop_id: selectedShop } : { merchant_shop_id: selectedShop })
          }),
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || 'Failed to create checkout session');
        }

        const data = await response.json();

        // Check if it's a free tier response from the backend
        if (data.free_tier) {
          const subscriptionData = {
            subscription_tier_id: selectedTier.id,
            auto_renew: true,
            subscription_type: "shop",
            ...(shopObj?.type === 'shop' ? { shop_id: selectedShop } : { merchant_shop_id: selectedShop })
          };

          await createSubscriptionFn(subscriptionData);

          toast.success(`Successfully upgraded ${shopObj?.name} to ${selectedTier.name} plan`);
          setConfirmDialogOpen(false);
          return;
        }

        if (!data.checkout_url) {
          throw new Error('Failed to create checkout session');
        }

        // Close the dialog
        setConfirmDialogOpen(false);

        // Redirect to Stripe Checkout
        window.location.href = data.checkout_url;
      }
    } catch (error) {
      toast.error("Failed to upgrade subscription plan");
      console.error("Error upgrading subscription:", error);
    } finally {
      setIsUpgrading(false);
    }
  };

  // Loading state
  if (isLoadingShops || isLoadingMerchantShops || isLoadingTiers) {
    return (
      <Card className="mb-8 border shadow-sm">
        <CardHeader className="pb-3">
          <CardTitle>Shop Subscription Plans</CardTitle>
          <CardDescription>
            Manage subscription plans for your shops
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex justify-center py-8">
            <div className="h-8 w-8 animate-spin rounded-full border-b-2 border-primary"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  // No shops state
  if (allShops.length === 0) {
    return (
      <Card className="mb-8 border shadow-sm">
        <CardHeader className="pb-3">
          <CardTitle>Shop Subscription Plans</CardTitle>
          <CardDescription>
            Manage subscription plans for your shops
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Alert className="mb-4">
            <AlertCircle className="h-4 w-4 mr-2" />
            <AlertDescription>
              You don't have any shops yet. Create a shop to manage subscription plans.
            </AlertDescription>
          </Alert>
          <Button onClick={() => router.push('/dashboard/shops/new')}>
            <ShoppingBag className="h-4 w-4 mr-2" />
            Create Shop
          </Button>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="mb-8 border shadow-sm">
      <CardHeader className="pb-3">
        <CardTitle>Shop Subscription Plans</CardTitle>
        <CardDescription>
          Manage subscription plans for your shops
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="mb-6">
          <h3 className="text-lg font-semibold mb-4">Select a Shop</h3>
          <Select value={selectedShop} onValueChange={setSelectedShop}>
            <SelectTrigger className="w-full">
              <SelectValue placeholder="Choose a shop to manage subscription" />
            </SelectTrigger>
            <SelectContent>
              {allShops.map((shop) => (
                <SelectItem key={shop.id} value={shop.id}>
                  <div className="flex items-center justify-between w-full">
                    <span>{shop.name}</span>
                    <Badge variant={shop.type === 'shop' ? 'default' : 'secondary'} className="ml-2">
                      {shop.type === 'shop' ? 'New' : 'Legacy'}
                    </Badge>
                  </div>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {selectedShop && (
          <>
            <Separator className="my-6" />
            <div className="mb-6">
              <h3 className="text-lg font-semibold mb-4">Available Plans for {shop?.name}</h3>
              <div className="grid gap-6 md:grid-cols-3">
                {shopTiers.map((tier) => (
                  <Card key={tier.id} className="flex flex-col border shadow-sm hover:shadow-md transition-shadow">
                    <CardHeader className="pb-3">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-2">
                          {tier.icon}
                          <CardTitle className="text-lg">{tier.name}</CardTitle>
                        </div>
                      </div>
                      <CardDescription>{tier.description}</CardDescription>
                    </CardHeader>
                    <CardContent className="flex-1">
                      <div className="text-3xl font-bold mb-4">
                        ${tier.price.toFixed(2)}
                        <span className="text-sm font-normal text-muted-foreground">
                          /month
                        </span>
                      </div>
                      <div className="space-y-2 mb-6">
                        <div className="flex justify-between text-sm">
                          <span>Credits:</span>
                          <span className="font-medium">
                            {tier.credit_limit.toLocaleString()}
                          </span>
                        </div>
                      </div>
                      <ul className="space-y-2 mb-6">
                        {tier.features.map((feature, i) => (
                          <li key={i} className="flex items-start">
                            <Check className="h-4 w-4 mr-2 text-green-500 flex-shrink-0 mt-0.5" />
                            <span className="text-sm">{feature}</span>
                          </li>
                        ))}
                      </ul>
                    </CardContent>
                    <CardFooter className="pt-0">
                      <Button
                        className="w-full"
                        onClick={() => {
                          setSelectedTier(tier);
                          setConfirmDialogOpen(true);
                        }}
                      >
                        Select Plan
                      </Button>
                    </CardFooter>
                  </Card>
                ))}
              </div>
            </div>
          </>
        )}
      </CardContent>

      {/* Confirmation Dialog */}
      <Dialog open={confirmDialogOpen} onOpenChange={setConfirmDialogOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Confirm Shop Subscription</DialogTitle>
            <DialogDescription>
              Are you sure you want to upgrade {shop?.name} to the {selectedTier?.name} plan?
            </DialogDescription>
          </DialogHeader>
          <div className="py-4">
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span>Shop:</span>
                <span className="font-medium">{shop?.name}</span>
              </div>
              <div className="flex justify-between">
                <span>Plan:</span>
                <span className="font-medium">{selectedTier?.name}</span>
              </div>
              <div className="flex justify-between">
                <span>Price:</span>
                <span className="font-medium">
                  ${selectedTier?.price.toFixed(2)}/month
                </span>
              </div>
              <div className="flex justify-between">
                <span>Credits:</span>
                <span className="font-medium">
                  {selectedTier?.credit_limit.toLocaleString()}
                </span>
              </div>
            </div>
          </div>
          <DialogFooter className="sm:justify-end">
            <Button variant="outline" size="sm" onClick={() => setConfirmDialogOpen(false)}>
              Cancel
            </Button>
            <Button
              size="sm"
              onClick={handleUpgradeConfirm}
              disabled={isUpgrading}
            >
              {isUpgrading ? (
                <>
                  <span className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-b-transparent"></span>
                  Processing...
                </>
              ) : (
                "Confirm Upgrade"
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </Card>
  );
}
