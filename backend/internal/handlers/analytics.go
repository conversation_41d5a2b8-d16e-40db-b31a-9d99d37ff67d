package handlers

import (
	"fmt"
	"net/http"
	"time"

	"github.com/adc-credit/backend/internal/database"
	"github.com/adc-credit/backend/internal/models"
	"github.com/gin-gonic/gin"
)

// GetAnalyticsSummary returns a summary of analytics data
func GetAnalyticsSummary(c *gin.Context) {
	user := c.MustGet("user").(models.User)

	// Check if user has access to advanced analytics
	var subscription models.Subscription
	if err := database.DB.Where("user_id = ? AND status = ?", user.ID, "active").
		Preload("SubscriptionTier").First(&subscription).Error; err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "No active subscription found"})
		return
	}

	if !subscription.SubscriptionTier.AdvancedAnalytics {
		c.JSON(http.StatusForbidden, gin.H{"error": "Advanced analytics not available on your subscription tier"})
		return
	}

	// Get time range from query parameters
	startDateStr := c.<PERSON><PERSON>ult<PERSON>uer<PERSON>("start_date", time.Now().AddDate(0, -1, 0).Format("2006-01-02"))
	endDateStr := c.<PERSON><PERSON>("end_date", time.Now().Format("2006-01-02"))

	startDate, err := time.Parse("2006-01-02", startDateStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid start date format"})
		return
	}

	endDate, err := time.Parse("2006-01-02", endDateStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid end date format"})
		return
	}
	endDate = endDate.Add(24 * time.Hour) // Include the entire end date

	// Get analytics data
	var analyticsData []models.AnalyticsData
	if err := database.DB.Where("user_id = ? AND date BETWEEN ? AND ?", user.ID, startDate, endDate).
		Find(&analyticsData).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch analytics data"})
		return
	}

	// Calculate summary metrics
	totalRequests := 0
	totalCredits := 0
	avgResponseTime := 0
	totalResponseTime := 0
	errorCount := 0
	p95ResponseTime := 0
	p99ResponseTime := 0

	if len(analyticsData) > 0 {
		for _, data := range analyticsData {
			totalRequests += data.TotalRequests
			totalCredits += data.TotalCredits
			totalResponseTime += data.AvgResponseTime * data.TotalRequests
			errorCount += int(float64(data.TotalRequests) * data.ErrorRate / 100)
		}

		if totalRequests > 0 {
			avgResponseTime = totalResponseTime / totalRequests
		}

		// Calculate percentiles (simplified approach)
		responseTimes := make([]int, 0, len(analyticsData))
		for _, data := range analyticsData {
			responseTimes = append(responseTimes, data.P95ResponseTime)
			responseTimes = append(responseTimes, data.P99ResponseTime)
		}

		if len(responseTimes) > 0 {
			p95ResponseTime = responseTimes[len(responseTimes)*95/100]
			p99ResponseTime = responseTimes[len(responseTimes)*99/100]
		}
	}

	// Prepare response
	response := gin.H{
		"total_requests":    totalRequests,
		"total_credits":     totalCredits,
		"avg_response_time": avgResponseTime,
		"error_rate":        float64(errorCount) / float64(totalRequests) * 100,
		"p95_response_time": p95ResponseTime,
		"p99_response_time": p99ResponseTime,
		"start_date":        startDate.Format("2006-01-02"),
		"end_date":          endDate.Add(-24 * time.Hour).Format("2006-01-02"),
		"subscription_tier": subscription.SubscriptionTier.Name,
	}

	c.JSON(http.StatusOK, response)
}

// GetAnalyticsTrends returns trend data for analytics
func GetAnalyticsTrends(c *gin.Context) {
	user := c.MustGet("user").(models.User)

	// Check if user has access to advanced analytics
	var subscription models.Subscription
	if err := database.DB.Where("user_id = ? AND status = ?", user.ID, "active").
		Preload("SubscriptionTier").First(&subscription).Error; err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "No active subscription found"})
		return
	}

	if !subscription.SubscriptionTier.AdvancedAnalytics {
		c.JSON(http.StatusForbidden, gin.H{"error": "Advanced analytics not available on your subscription tier"})
		return
	}

	// Get time range and interval from query parameters
	startDateStr := c.DefaultQuery("start_date", time.Now().AddDate(0, -1, 0).Format("2006-01-02"))
	endDateStr := c.DefaultQuery("end_date", time.Now().Format("2006-01-02"))
	interval := c.DefaultQuery("interval", "day") // day, week, month

	startDate, err := time.Parse("2006-01-02", startDateStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid start date format"})
		return
	}

	endDate, err := time.Parse("2006-01-02", endDateStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid end date format"})
		return
	}
	endDate = endDate.Add(24 * time.Hour) // Include the entire end date

	// Get analytics data
	var analyticsData []models.AnalyticsData
	if err := database.DB.Where("user_id = ? AND date BETWEEN ? AND ?", user.ID, startDate, endDate).
		Order("date ASC").
		Find(&analyticsData).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch analytics data"})
		return
	}

	// Group data by interval
	trends := make(map[string]gin.H)

	for _, data := range analyticsData {
		var key string
		switch interval {
		case "day":
			key = data.Date.Format("2006-01-02")
		case "week":
			year, week := data.Date.ISOWeek()
			key = fmt.Sprintf("%d-W%02d", year, week)
		case "month":
			key = data.Date.Format("2006-01")
		}

		if _, exists := trends[key]; !exists {
			trends[key] = gin.H{
				"period":              key,
				"total_requests":      0,
				"total_credits":       0,
				"avg_response_time":   0,
				"error_rate":          0.0, // Initialize as float64
				"total_response_time": 0,
			}
		}

		current := trends[key]
		current["total_requests"] = current["total_requests"].(int) + data.TotalRequests
		current["total_credits"] = current["total_credits"].(int) + data.TotalCredits
		current["total_response_time"] = current["total_response_time"].(int) + data.AvgResponseTime*data.TotalRequests

		// Update average response time
		if current["total_requests"].(int) > 0 {
			current["avg_response_time"] = current["total_response_time"].(int) / current["total_requests"].(int)
		}

		// Update error rate (weighted average)
		oldErrorRate := current["error_rate"].(float64)
		oldRequests := current["total_requests"].(int) - data.TotalRequests
		newErrorRate := (oldErrorRate*float64(oldRequests) + data.ErrorRate*float64(data.TotalRequests)) /
			float64(current["total_requests"].(int))
		current["error_rate"] = newErrorRate

		trends[key] = current
	}

	// Convert map to slice for response
	result := make([]gin.H, 0, len(trends))
	for _, value := range trends {
		delete(value, "total_response_time") // Remove intermediate calculation
		result = append(result, value)
	}

	c.JSON(http.StatusOK, result)
}

// GetEndpointAnalytics returns analytics data grouped by endpoint
func GetEndpointAnalytics(c *gin.Context) {
	user := c.MustGet("user").(models.User)

	// Check if user has access to advanced analytics
	var subscription models.Subscription
	if err := database.DB.Where("user_id = ? AND status = ?", user.ID, "active").
		Preload("SubscriptionTier").First(&subscription).Error; err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "No active subscription found"})
		return
	}

	if !subscription.SubscriptionTier.AdvancedAnalytics {
		c.JSON(http.StatusForbidden, gin.H{"error": "Advanced analytics not available on your subscription tier"})
		return
	}

	// Get time range from query parameters
	startDateStr := c.DefaultQuery("start_date", time.Now().AddDate(0, -1, 0).Format("2006-01-02"))
	endDateStr := c.DefaultQuery("end_date", time.Now().Format("2006-01-02"))

	startDate, err := time.Parse("2006-01-02", startDateStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid start date format"})
		return
	}

	endDate, err := time.Parse("2006-01-02", endDateStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid end date format"})
		return
	}
	endDate = endDate.Add(24 * time.Hour) // Include the entire end date

	// Get analytics data grouped by endpoint
	var analyticsData []models.AnalyticsData
	if err := database.DB.Where("user_id = ? AND date BETWEEN ? AND ? AND endpoint != ''",
		user.ID, startDate, endDate).
		Find(&analyticsData).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch analytics data"})
		return
	}

	// Group data by endpoint
	endpointStats := make(map[string]gin.H)

	for _, data := range analyticsData {
		if _, exists := endpointStats[data.Endpoint]; !exists {
			endpointStats[data.Endpoint] = gin.H{
				"endpoint":            data.Endpoint,
				"total_requests":      0,
				"total_credits":       0,
				"avg_response_time":   0,
				"error_rate":          0.0, // Initialize as float64
				"p95_response_time":   0,
				"p99_response_time":   0,
				"total_response_time": 0,
			}
		}

		current := endpointStats[data.Endpoint]
		current["total_requests"] = current["total_requests"].(int) + data.TotalRequests
		current["total_credits"] = current["total_credits"].(int) + data.TotalCredits
		current["total_response_time"] = current["total_response_time"].(int) + data.AvgResponseTime*data.TotalRequests

		// Update average response time
		if current["total_requests"].(int) > 0 {
			current["avg_response_time"] = current["total_response_time"].(int) / current["total_requests"].(int)
		}

		// Update error rate (weighted average)
		oldErrorRate := current["error_rate"].(float64)
		oldRequests := current["total_requests"].(int) - data.TotalRequests
		newErrorRate := (oldErrorRate*float64(oldRequests) + data.ErrorRate*float64(data.TotalRequests)) /
			float64(current["total_requests"].(int))
		current["error_rate"] = newErrorRate

		// Update percentiles (take max)
		if data.P95ResponseTime > current["p95_response_time"].(int) {
			current["p95_response_time"] = data.P95ResponseTime
		}
		if data.P99ResponseTime > current["p99_response_time"].(int) {
			current["p99_response_time"] = data.P99ResponseTime
		}

		endpointStats[data.Endpoint] = current
	}

	// Convert map to slice for response
	result := make([]gin.H, 0, len(endpointStats))
	for _, value := range endpointStats {
		delete(value, "total_response_time") // Remove intermediate calculation
		result = append(result, value)
	}

	c.JSON(http.StatusOK, result)
}

// GetPerformanceMetrics returns performance metrics over time
func GetPerformanceMetrics(c *gin.Context) {
	user := c.MustGet("user").(models.User)

	// Check if user has access to advanced analytics
	var subscription models.Subscription
	if err := database.DB.Where("user_id = ? AND status = ?", user.ID, "active").
		Preload("SubscriptionTier").First(&subscription).Error; err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "No active subscription found"})
		return
	}

	if !subscription.SubscriptionTier.AdvancedAnalytics {
		c.JSON(http.StatusForbidden, gin.H{"error": "Advanced analytics not available on your subscription tier"})
		return
	}

	// Get time range from query parameters
	startDateStr := c.DefaultQuery("start_date", time.Now().AddDate(0, -1, 0).Format("2006-01-02"))
	endDateStr := c.DefaultQuery("end_date", time.Now().Format("2006-01-02"))
	metric := c.DefaultQuery("metric", "response_time") // response_time, error_rate, p95, p99

	startDate, err := time.Parse("2006-01-02", startDateStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid start date format"})
		return
	}

	endDate, err := time.Parse("2006-01-02", endDateStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid end date format"})
		return
	}
	endDate = endDate.Add(24 * time.Hour) // Include the entire end date

	// Get analytics data
	var analyticsData []models.AnalyticsData
	if err := database.DB.Where("user_id = ? AND date BETWEEN ? AND ?", user.ID, startDate, endDate).
		Order("date ASC").
		Find(&analyticsData).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch analytics data"})
		return
	}

	// Prepare response
	result := make([]gin.H, 0, len(analyticsData))

	for _, data := range analyticsData {
		var metricValue float64

		switch metric {
		case "response_time":
			metricValue = float64(data.AvgResponseTime)
		case "error_rate":
			metricValue = data.ErrorRate
		case "p95":
			metricValue = float64(data.P95ResponseTime)
		case "p99":
			metricValue = float64(data.P99ResponseTime)
		}

		result = append(result, gin.H{
			"date":  data.Date.Format("2006-01-02"),
			"value": metricValue,
		})
	}

	c.JSON(http.StatusOK, gin.H{
		"metric": metric,
		"data":   result,
	})
}
