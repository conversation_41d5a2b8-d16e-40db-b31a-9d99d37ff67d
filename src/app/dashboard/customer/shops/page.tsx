"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import Link from "next/link";
import Image from "next/image";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardFooter } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { MagnifyingGlass, Storefront, House } from "phosphor-react";
import { CustomerBreadcrumbs } from "@/components/navigation/breadcrumbs";
import { CustomerPageHeader } from "@/components/layout/page-header-mobile";
import { useGetCustomerShopsQuery } from "@/lib/api/apiSlice";

export default function ShopsPage() {
  const router = useRouter();
  const [searchQuery, setSearchQuery] = useState("");

  // Fetch shops data
  const { data: shops, isLoading } = useGetCustomerShopsQuery();

  // Filter shops based on search query
  const filteredShops = shops?.filter(shop =>
    shop.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    shop.description?.toLowerCase().includes(searchQuery.toLowerCase())
  );

  return (
    <div className="relative flex size-full min-h-screen flex-col bg-[#fbfaf9] justify-between overflow-x-hidden">
      {/* Header */}
      <CustomerPageHeader
        title="Shops"
        backHref="/dashboard/customer"
      />

      {/* Breadcrumbs */}
      <CustomerBreadcrumbs
        segments={[
          { label: "Shops", href: "/dashboard/customer/shops", isCurrentPage: true }
        ]}
      />

      {/* Search */}
      <div className="p-4">
        <div className="relative mb-4">
          <MagnifyingGlass className="absolute left-3 top-1/2 h-5 w-5 -translate-y-1/2 text-[#8a745c]" />
          <Input
            className="pl-10 bg-[#f1edea] border-none h-12"
            placeholder="Search shops..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>

        {/* Shops List */}
        <div className="space-y-4">
          {isLoading ? (
            // Loading state
            Array.from({ length: 3 }).map((_, index) => (
              <Card key={index} className="overflow-hidden">
                <div className="animate-pulse">
                  <div className="h-24 bg-gray-200"></div>
                  <CardContent className="p-4">
                    <div className="h-5 bg-gray-200 rounded w-3/4 mb-2"></div>
                    <div className="h-4 bg-gray-200 rounded w-1/2"></div>
                  </CardContent>
                </div>
              </Card>
            ))
          ) : filteredShops && filteredShops.length > 0 ? (
            // Shops list
            filteredShops.map((shop) => (
              <Card key={shop.id} className="overflow-hidden">
                <Link href={`/dashboard/customer/shops/${shop.id}`}>
                  <div className="relative h-24 w-full bg-[#f1edea]">
                    {shop.image_url ? (
                      <Image
                        src={shop.image_url}
                        alt={shop.name}
                        fill
                        className="object-cover"
                      />
                    ) : (
                      <div className="flex h-full items-center justify-center">
                        <Storefront size={48} className="text-[#8a745c]" />
                      </div>
                    )}
                  </div>
                  <CardContent className="p-4">
                    <h3 className="text-[#181510] text-lg font-bold mb-1">{shop.name}</h3>
                    {shop.description && (
                      <p className="text-[#8a745c] text-sm line-clamp-2">{shop.description}</p>
                    )}
                  </CardContent>
                  <CardFooter className="p-4 pt-0 flex justify-between items-center">
                    <div className="text-sm text-[#8a745c]">
                      {shop.credit_balance ? (
                        <span>Your credits: <strong>{shop.credit_balance}</strong></span>
                      ) : (
                        <span>No credits yet</span>
                      )}
                    </div>
                    <Button variant="ghost" size="sm" className="text-[#181510]">
                      View Shop
                    </Button>
                  </CardFooter>
                </Link>
              </Card>
            ))
          ) : (
            // No shops or no search results
            <div className="text-center py-8">
              <Storefront size={48} className="mx-auto text-[#8a745c] mb-4" />
              <h3 className="text-[#181510] text-lg font-bold mb-2">
                {searchQuery ? "No shops found" : "No shops available"}
              </h3>
              <p className="text-[#8a745c] mb-4">
                {searchQuery
                  ? `No shops match "${searchQuery}"`
                  : "There are no shops available at the moment."}
              </p>
              {searchQuery && (
                <Button variant="outline" onClick={() => setSearchQuery("")}>
                  Clear Search
                </Button>
              )}
            </div>
          )}
        </div>
      </div>

      {/* Footer */}
      <div className="mt-auto">
        <div className="flex gap-1 border-t border-[#f1edea] bg-[#fbfaf9] px-4 pb-3 pt-2">
          <Link className="just flex flex-1 flex-col items-center justify-end gap-1 text-[#8a745c]" href="/dashboard/customer">
            <div className="text-[#8a745c] flex h-8 items-center justify-center">
              <House size={24} weight="regular" />
            </div>
            <p className="text-[#8a745c] text-xs font-medium leading-normal tracking-[0.015em]">Home</p>
          </Link>
          <div className="just flex flex-1 flex-col items-center justify-end gap-1 rounded-full text-[#181510]">
            <div className="text-[#181510] flex h-8 items-center justify-center">
              <Storefront size={24} weight="fill" />
            </div>
            <p className="text-[#181510] text-xs font-medium leading-normal tracking-[0.015em]">Shops</p>
          </div>
        </div>
        <div className="h-5 bg-[#fbfaf9]"></div>
      </div>
    </div>
  );
}
