"use client";

import { useEffect, useState } from "react";
import { Card, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import { Activity, RefreshCw, Clock, ArrowUpRight, ArrowDownRight, Minus } from "lucide-react";
import { useGetCreditBalanceQuery, useGetUsageSummaryQuery } from "@/lib/api/apiSlice";
import Link from "next/link";
import { UsageSummary, TimePeriodSummary } from "@/types";

interface RealUsageCardProps {
  period?: string;
  showDetailedView?: boolean;
}

export function RealUsageCard({ period = "month", showDetailedView = false }: RealUsageCardProps) {
  const [isRefreshing, setIsRefreshing] = useState(false);

  // Fetch credit balance and usage data
  const {
    data: creditInfo,
    isLoading: isLoadingCredit,
    refetch: refetchCredit
  } = useGetCreditBalanceQuery();

  const {
    data: usageSummary,
    isLoading: isLoadingUsage,
    refetch: refetchUsage
  } = useGetUsageSummaryQuery({ period });

  // Function to refresh data
  const handleRefresh = async () => {
    setIsRefreshing(true);
    await Promise.all([refetchCredit(), refetchUsage()]);
    setTimeout(() => setIsRefreshing(false), 500);
  };

  // Calculate usage trend
  const getUsageTrend = () => {
    if (!usageSummary || !usageSummary.time_series || usageSummary.time_series.length < 2) {
      return "stable";
    }

    const lastTwoPoints = usageSummary.time_series.slice(-2);
    const diff = lastTwoPoints[1].total_credits - lastTwoPoints[0].total_credits;

    if (diff > 10) return "up";
    if (diff < -10) return "down";
    return "stable";
  };

  // Get trend icon based on usage pattern
  const getTrendIcon = (trend: "up" | "down" | "stable") => {
    switch (trend) {
      case "up":
        return <ArrowUpRight className="h-4 w-4 text-destructive" />;
      case "down":
        return <ArrowDownRight className="h-4 w-4 text-green-500" />;
      case "stable":
        return <Minus className="h-4 w-4 text-muted-foreground" />;
      default:
        return null;
    }
  };

  const usageTrend = getUsageTrend();
  const loading = isLoadingCredit || isLoadingUsage || isRefreshing;

  // Calculate percentage of credits used
  const creditUsagePercentage = creditInfo 
    ? ((creditInfo.credit_limit - creditInfo.credit_balance) / creditInfo.credit_limit) * 100
    : 0;

  // Format date for display
  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat('en-US', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    }).format(date);
  };

  // Get the current time period description
  const getPeriodDescription = () => {
    switch (period) {
      case "day":
        return "Today's";
      case "week":
        return "This week's";
      case "month":
        return "This month's";
      case "year":
        return "This year's";
      default:
        return "Current";
    }
  };

  return (
    <Card className={showDetailedView ? "h-full" : ""}>
      <CardHeader className="pb-2">
        <div className="flex items-center justify-between">
          <CardTitle className="text-base font-medium flex items-center gap-2">
            <Activity className="h-4 w-4 text-primary" />
            Real-Time Usage
          </CardTitle>
          <div className="flex items-center gap-1 text-xs">
            {loading ? (
              <Skeleton className="h-4 w-16" />
            ) : (
              <>
                {getTrendIcon(usageTrend)}
                <span className={usageTrend === "up" ? "text-destructive" : usageTrend === "down" ? "text-green-500" : "text-muted-foreground"}>
                  {usageTrend === "up" ? "Increasing" : usageTrend === "down" ? "Decreasing" : "Stable"}
                </span>
              </>
            )}
          </div>
        </div>
        <CardDescription>{getPeriodDescription()} usage statistics</CardDescription>
      </CardHeader>
      <CardContent>
        {loading ? (
          <div className="space-y-2">
            <Skeleton className="h-8 w-24" />
            <Skeleton className="h-4 w-full" />
            <Skeleton className="h-4 w-3/4" />
          </div>
        ) : (
          <>
            <div className="text-3xl font-bold">
              {usageSummary?.total_credits.toLocaleString() || 0}
              <span className="text-sm text-muted-foreground ml-2">credits used</span>
            </div>
            <div className="mt-4">
              <div className="flex items-center justify-between mb-1 text-xs">
                <span>0</span>
                <span>{creditInfo?.credit_limit.toLocaleString() || 0}</span>
              </div>
              <Progress value={creditUsagePercentage} className="h-2" />
              <div className="flex justify-between mt-1">
                <span className="text-xs text-muted-foreground">
                  {creditInfo?.credit_balance.toLocaleString() || 0} credits remaining
                </span>
                <span className="text-xs text-muted-foreground">
                  {creditUsagePercentage.toFixed(1)}% used
                </span>
              </div>
            </div>

            {showDetailedView && usageSummary && usageSummary.time_series && usageSummary.time_series.length > 0 && (
              <div className="mt-6">
                <h4 className="text-sm font-medium mb-2">Usage Timeline</h4>
                <div className="h-32">
                  <div className="h-full flex items-end space-x-1">
                    {usageSummary.time_series.map((item: TimePeriodSummary, i: number) => {
                      const maxCredits = Math.max(
                        ...usageSummary.time_series.map((t: TimePeriodSummary) => t.total_credits)
                      );
                      const height = maxCredits
                        ? (item.total_credits / maxCredits) * 100
                        : 0;
                      return (
                        <div
                          key={i}
                          className="flex-1 flex flex-col items-center"
                          title={`${item.period}: ${item.total_credits.toLocaleString()} credits, ${item.total_usage.toLocaleString()} calls`}
                        >
                          <div
                            className="w-full bg-primary rounded-t"
                            style={{ height: `${height}%` }}
                          ></div>
                          <div className="text-xs mt-1 truncate w-full text-center">
                            {item.period.split(' ')[0]}
                          </div>
                        </div>
                      );
                    })}
                  </div>
                </div>
              </div>
            )}

            <div className="mt-4 space-y-2">
              <div className="flex items-center justify-between text-sm">
                <span>Total API Calls</span>
                <span className="font-medium">{usageSummary?.total_usage.toLocaleString() || 0}</span>
              </div>
              <div className="flex items-center justify-between text-sm">
                <span>Avg. Credits/Call</span>
                <span className="font-medium">
                  {usageSummary && usageSummary.total_usage > 0
                    ? (usageSummary.total_credits / usageSummary.total_usage).toFixed(2)
                    : "0.00"}
                </span>
              </div>
              <div className="flex items-center justify-between text-sm">
                <span>Last Updated</span>
                <span className="font-medium flex items-center gap-1">
                  <Clock className="h-3 w-3" />
                  {formatDate(new Date())}
                </span>
              </div>
            </div>
          </>
        )}
      </CardContent>
      <CardFooter className="flex justify-between pt-0">
        <Button
          variant="outline"
          size="sm"
          className="gap-1"
          onClick={handleRefresh}
          disabled={loading}
        >
          <RefreshCw className={`h-3 w-3 ${loading ? 'animate-spin' : ''}`} />
          {loading ? 'Refreshing...' : 'Refresh'}
        </Button>
        <Link href="/dashboard/usage">
          <Button variant="ghost" size="sm">
            View Details
          </Button>
        </Link>
      </CardFooter>
    </Card>
  );
}
