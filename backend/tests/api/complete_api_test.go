package api

import (
	"net/http"
	"testing"

	testutils "github.com/adc-credit/backend/internal/testing"
	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/suite"
)

type CompleteAPITestSuite struct {
	suite.Suite
	router *gin.Engine
}

func (suite *CompleteAPITestSuite) SetupSuite() {
	// Setup test environment
	err := testutils.SetupTestEnvironment()
	suite.Require().NoError(err)

	// Setup complete router with all routes
	suite.router = gin.New()
	suite.router.Use(gin.Recovery())

	// Setup all API routes
	suite.setupAllRoutes()
}

func (suite *CompleteAPITestSuite) TearDownSuite() {
	testutils.CleanupTestEnvironment()
}

func (suite *CompleteAPITestSuite) SetupTest() {
	testutils.CleanTestData()
}

func (suite *CompleteAPITestSuite) setupAllRoutes() {
	// Public routes
	public := suite.router.Group("/api/v1")
	{
		// Health check
		public.GET("/health", func(c *gin.Context) {
			c.JSON(http.StatusOK, gin.H{"status": "ok", "message": "API is healthy"})
		})

		// Authentication routes
		auth := public.Group("/auth")
		{
			auth.POST("/login", func(c *gin.Context) {
				c.JSON(http.StatusOK, gin.H{"message": "Login successful", "token": "mock-jwt-token"})
			})
			auth.POST("/register", func(c *gin.Context) {
				c.JSON(http.StatusCreated, gin.H{"message": "User registered successfully"})
			})
			auth.POST("/google", func(c *gin.Context) {
				c.JSON(http.StatusOK, gin.H{"message": "Google auth successful", "token": "mock-jwt-token"})
			})
			auth.POST("/refresh", func(c *gin.Context) {
				c.JSON(http.StatusOK, gin.H{"message": "Token refreshed", "token": "new-mock-jwt-token"})
			})
			auth.POST("/forgot-password", func(c *gin.Context) {
				c.JSON(http.StatusOK, gin.H{"message": "Password reset email sent"})
			})
			auth.POST("/reset-password", func(c *gin.Context) {
				c.JSON(http.StatusOK, gin.H{"message": "Password reset successful"})
			})
		}

		// External API routes
		external := public.Group("/external")
		{
			external.POST("/verify", func(c *gin.Context) {
				apiKey := c.GetHeader("X-API-Key")
				if apiKey == "" {
					c.JSON(http.StatusUnauthorized, gin.H{"error": "API key required"})
					return
				}
				c.JSON(http.StatusOK, gin.H{"valid": true, "api_key": apiKey})
			})
			external.POST("/consume", func(c *gin.Context) {
				apiKey := c.GetHeader("X-API-Key")
				if apiKey == "" {
					c.JSON(http.StatusUnauthorized, gin.H{"error": "API key required"})
					return
				}
				c.JSON(http.StatusOK, gin.H{"message": "Credits consumed", "remaining_credits": 90})
			})
		}

		// Scheduled tasks
		public.POST("/tasks/process-scheduled-credits", func(c *gin.Context) {
			c.JSON(http.StatusOK, gin.H{"message": "Scheduled credits processed"})
		})
		public.POST("/tasks/process-credit-resets", func(c *gin.Context) {
			c.JSON(http.StatusOK, gin.H{"message": "Credit resets processed"})
		})
	}

	// Protected routes (require authentication)
	protected := suite.router.Group("/api/v1")
	protected.Use(suite.mockAuthMiddleware())
	{
		// User management
		users := protected.Group("/users")
		{
			users.GET("/me", func(c *gin.Context) {
				user := c.MustGet("user")
				c.JSON(http.StatusOK, user)
			})
			users.PUT("/me", func(c *gin.Context) {
				c.JSON(http.StatusOK, gin.H{"message": "User updated successfully"})
			})
		}

		// API key management
		apiKeys := protected.Group("/apikeys")
		{
			apiKeys.GET("", func(c *gin.Context) {
				c.JSON(http.StatusOK, gin.H{
					"data": []gin.H{
						{"id": "key1", "name": "Test Key 1", "enabled": true},
						{"id": "key2", "name": "Test Key 2", "enabled": false},
					},
					"total": 2,
				})
			})
			apiKeys.POST("", func(c *gin.Context) {
				c.JSON(http.StatusCreated, gin.H{
					"id":      "new-key-id",
					"name":    "New API Key",
					"key":     "ak_test_1234567890abcdef",
					"enabled": true,
				})
			})
			apiKeys.GET("/:id", func(c *gin.Context) {
				id := c.Param("id")
				c.JSON(http.StatusOK, gin.H{
					"id":         id,
					"name":       "Test API Key",
					"enabled":    true,
					"created_at": "2023-01-01T00:00:00Z",
				})
			})
			apiKeys.PUT("/:id", func(c *gin.Context) {
				id := c.Param("id")
				c.JSON(http.StatusOK, gin.H{
					"id":      id,
					"message": "API key updated successfully",
				})
			})
			apiKeys.DELETE("/:id", func(c *gin.Context) {
				c.JSON(http.StatusNoContent, nil)
			})
		}

		// Credit management
		credits := protected.Group("/credits")
		{
			credits.GET("", func(c *gin.Context) {
				c.JSON(http.StatusOK, gin.H{
					"credit_balance":    1000,
					"subscription_type": "premium",
				})
			})
			credits.POST("/add", func(c *gin.Context) {
				c.JSON(http.StatusOK, gin.H{
					"message":     "Credits added successfully",
					"new_balance": 1100,
				})
			})
			credits.GET("/transactions", func(c *gin.Context) {
				c.JSON(http.StatusOK, gin.H{
					"data": []gin.H{
						{"id": "tx1", "type": "credit_add", "amount": 100},
						{"id": "tx2", "type": "credit_use", "amount": -10},
					},
					"total": 2,
				})
			})
		}

		// Usage statistics
		usage := protected.Group("/usage")
		{
			usage.GET("", func(c *gin.Context) {
				c.JSON(http.StatusOK, gin.H{
					"data": []gin.H{
						{"endpoint": "/api/test", "requests": 100, "credits": 50},
					},
					"total": 1,
				})
			})
			usage.GET("/summary", func(c *gin.Context) {
				c.JSON(http.StatusOK, gin.H{
					"total_requests":    1000,
					"total_credits":     500,
					"avg_response_time": 150,
				})
			})
		}

		// Subscription management
		subscriptions := protected.Group("/subscriptions")
		{
			subscriptions.GET("", func(c *gin.Context) {
				c.JSON(http.StatusOK, gin.H{
					"data": []gin.H{
						{"id": "sub1", "tier": "premium", "status": "active"},
					},
					"total": 1,
				})
			})
			subscriptions.GET("/active", func(c *gin.Context) {
				c.JSON(http.StatusOK, gin.H{
					"id":             "sub1",
					"tier":           "premium",
					"status":         "active",
					"credit_balance": 1000,
				})
			})
			subscriptions.GET("/tiers", func(c *gin.Context) {
				c.JSON(http.StatusOK, gin.H{
					"data": []gin.H{
						{"id": 1, "name": "Basic", "price": 9.99, "credit_limit": 1000},
						{"id": 2, "name": "Premium", "price": 19.99, "credit_limit": 5000},
					},
				})
			})
			subscriptions.POST("", func(c *gin.Context) {
				c.JSON(http.StatusCreated, gin.H{
					"id":      "new-sub-id",
					"message": "Subscription created successfully",
				})
			})
			subscriptions.PUT("/:id", func(c *gin.Context) {
				id := c.Param("id")
				c.JSON(http.StatusOK, gin.H{
					"id":      id,
					"message": "Subscription updated successfully",
				})
			})
			subscriptions.DELETE("/:id", func(c *gin.Context) {
				c.JSON(http.StatusOK, gin.H{
					"message": "Subscription cancelled successfully",
				})
			})
			subscriptions.POST("/:id/upgrade", func(c *gin.Context) {
				id := c.Param("id")
				c.JSON(http.StatusOK, gin.H{
					"id":      id,
					"message": "Subscription upgraded successfully",
				})
			})
		}

		// Webhook management
		webhooks := protected.Group("/webhooks")
		{
			webhooks.GET("", func(c *gin.Context) {
				c.JSON(http.StatusOK, gin.H{
					"data": []gin.H{
						{"id": "wh1", "name": "Test Webhook", "url": "https://example.com/webhook", "active": true},
					},
					"total": 1,
				})
			})
			webhooks.POST("", func(c *gin.Context) {
				c.JSON(http.StatusCreated, gin.H{
					"id":      "new-webhook-id",
					"message": "Webhook created successfully",
				})
			})
			webhooks.GET("/:id", func(c *gin.Context) {
				id := c.Param("id")
				c.JSON(http.StatusOK, gin.H{
					"id":     id,
					"name":   "Test Webhook",
					"url":    "https://example.com/webhook",
					"active": true,
				})
			})
			webhooks.PUT("/:id", func(c *gin.Context) {
				id := c.Param("id")
				c.JSON(http.StatusOK, gin.H{
					"id":      id,
					"message": "Webhook updated successfully",
				})
			})
			webhooks.DELETE("/:id", func(c *gin.Context) {
				c.JSON(http.StatusNoContent, nil)
			})
			webhooks.GET("/:id/deliveries", func(c *gin.Context) {
				id := c.Param("id")
				c.JSON(http.StatusOK, gin.H{
					"webhook_id": id,
					"data": []gin.H{
						{"id": "del1", "event": "credit.consumed", "status_code": 200, "success": true},
					},
					"total": 1,
				})
			})
		}
	}
}

// Mock authentication middleware for testing
func (suite *CompleteAPITestSuite) mockAuthMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Check for Authorization header
		auth := c.GetHeader("Authorization")
		if auth == "" {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "Authorization required"})
			c.Abort()
			return
		}

		// Mock user data
		mockUser := gin.H{
			"id":    "user-123",
			"email": "<EMAIL>",
			"name":  "Test User",
			"role":  "user",
		}
		c.Set("user", mockUser)
		c.Next()
	}
}

// ============================================================================
// PUBLIC API ENDPOINT TESTS
// ============================================================================

func (suite *CompleteAPITestSuite) TestHealthEndpoint() {
	t := suite.T()

	req := testutils.TestRequest{
		Method: "GET",
		URL:    "/api/v1/health",
	}

	resp := testutils.MakeRequest(t, suite.router, req)

	testutils.AssertSuccessResponse(t, resp)
	assert.Equal(t, 200, resp.StatusCode)
	testutils.AssertJSONResponse(t, resp, map[string]interface{}{
		"status":  "ok",
		"message": "API is healthy",
	})
}

// ============================================================================
// AUTHENTICATION ENDPOINT TESTS
// ============================================================================

func (suite *CompleteAPITestSuite) TestAuthLogin() {
	t := suite.T()

	loginData := map[string]interface{}{
		"email":    "<EMAIL>",
		"password": "password123",
	}

	req := testutils.TestRequest{
		Method: "POST",
		URL:    "/api/v1/auth/login",
		Body:   loginData,
	}

	resp := testutils.MakeRequest(t, suite.router, req)

	testutils.AssertSuccessResponse(t, resp)
	assert.Equal(t, 200, resp.StatusCode)
	assert.Contains(t, resp.Body, "token")
}

func (suite *CompleteAPITestSuite) TestAuthRegister() {
	t := suite.T()

	registerData := map[string]interface{}{
		"email":    "<EMAIL>",
		"password": "password123",
		"name":     "New User",
	}

	req := testutils.TestRequest{
		Method: "POST",
		URL:    "/api/v1/auth/register",
		Body:   registerData,
	}

	resp := testutils.MakeRequest(t, suite.router, req)

	testutils.AssertSuccessResponse(t, resp)
	assert.Equal(t, 201, resp.StatusCode)
}

func (suite *CompleteAPITestSuite) TestAuthGoogleAuth() {
	t := suite.T()

	googleData := map[string]interface{}{
		"token": "google-oauth-token",
	}

	req := testutils.TestRequest{
		Method: "POST",
		URL:    "/api/v1/auth/google",
		Body:   googleData,
	}

	resp := testutils.MakeRequest(t, suite.router, req)

	testutils.AssertSuccessResponse(t, resp)
	assert.Equal(t, 200, resp.StatusCode)
	assert.Contains(t, resp.Body, "token")
}

func (suite *CompleteAPITestSuite) TestAuthRefreshToken() {
	t := suite.T()

	refreshData := map[string]interface{}{
		"refresh_token": "old-refresh-token",
	}

	req := testutils.TestRequest{
		Method: "POST",
		URL:    "/api/v1/auth/refresh",
		Body:   refreshData,
	}

	resp := testutils.MakeRequest(t, suite.router, req)

	testutils.AssertSuccessResponse(t, resp)
	assert.Equal(t, 200, resp.StatusCode)
	assert.Contains(t, resp.Body, "token")
}

func (suite *CompleteAPITestSuite) TestAuthForgotPassword() {
	t := suite.T()

	forgotData := map[string]interface{}{
		"email": "<EMAIL>",
	}

	req := testutils.TestRequest{
		Method: "POST",
		URL:    "/api/v1/auth/forgot-password",
		Body:   forgotData,
	}

	resp := testutils.MakeRequest(t, suite.router, req)

	testutils.AssertSuccessResponse(t, resp)
	assert.Equal(t, 200, resp.StatusCode)
}

func (suite *CompleteAPITestSuite) TestAuthResetPassword() {
	t := suite.T()

	resetData := map[string]interface{}{
		"token":    "reset-token",
		"password": "newpassword123",
	}

	req := testutils.TestRequest{
		Method: "POST",
		URL:    "/api/v1/auth/reset-password",
		Body:   resetData,
	}

	resp := testutils.MakeRequest(t, suite.router, req)

	testutils.AssertSuccessResponse(t, resp)
	assert.Equal(t, 200, resp.StatusCode)
}

// Run the test suite
func TestCompleteAPITestSuite(t *testing.T) {
	suite.Run(t, new(CompleteAPITestSuite))
}
