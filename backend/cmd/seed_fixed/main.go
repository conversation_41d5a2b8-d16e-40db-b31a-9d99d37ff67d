package main

import (
	"fmt"
	"log"
	"time"

	"github.com/adc-credit/backend/internal/database"
	"github.com/google/uuid"
	"github.com/joho/godotenv"
	"gorm.io/gorm"
)

func main() {
	// Load environment variables
	if err := godotenv.Load(); err != nil {
		log.Println("No .env file found, using environment variables")
	}

	// Initialize database connection
	if err := database.InitDB(); err != nil {
		log.Fatalf("Failed to connect to database: %v", err)
	}

	// Seed test data
	if err := seedTestData(); err != nil {
		log.Fatalf("Failed to seed test data: %v", err)
	}

	log.Println("Test data seeded successfully!")
}

func seedTestData() error {
	// Create a test user if it doesn't exist
	var testUser struct {
		ID    uuid.UUID
		Email string
	}

	if err := database.DB.Table("users").Where("email = ?", "<EMAIL>").First(&testUser).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			// Create a new user with a unique ID
			userID := uuid.New()

			// Execute raw SQL to insert the user with explicit NULL for google_id
			result := database.DB.Exec(`
				INSERT INTO users (id, email, name, password, role, google_id, created_at, updated_at)
				VALUES (?, ?, ?, ?, ?, NULL, ?, ?)
			`, userID, "<EMAIL>", "Test User",
				"$2a$10$yjH6RgJCHjvjI5Cv.1JTwOKKD.YmPRVuKL9Dbu5RDOTtCZ7IYJvSe", // "password"
				"user", time.Now(), time.Now())

			if result.Error != nil {
				return fmt.Errorf("failed to create test user: %w", result.Error)
			}

			// Fetch the newly created user
			if err := database.DB.Table("users").Where("id = ?", userID).First(&testUser).Error; err != nil {
				return fmt.Errorf("failed to fetch created user: %w", err)
			}

			log.Println("Created test user:", testUser.ID)
		} else {
			return fmt.Errorf("failed to check for test user: %w", err)
		}
	} else {
		log.Println("Using existing test user:", testUser.ID)
	}

	// Ensure subscription tiers exist
	if err := ensureSubscriptionTiers(); err != nil {
		return fmt.Errorf("failed to ensure subscription tiers: %w", err)
	}

	// Create a subscription for the test user if it doesn't exist
	var subscription struct {
		ID uuid.UUID
	}

	if err := database.DB.Table("subscriptions").Where("user_id = ?", testUser.ID).First(&subscription).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			// Get the Pro tier
			var proTier struct {
				ID          uint
				CreditLimit int
			}

			if err := database.DB.Table("subscription_tiers").Where("name = ?", "Pro").First(&proTier).Error; err != nil {
				return fmt.Errorf("failed to find Pro tier: %w", err)
			}

			subscriptionID := uuid.New()

			// Insert subscription using raw SQL
			result := database.DB.Exec(`
				INSERT INTO subscriptions (id, user_id, subscription_tier_id, start_date, auto_renew, status, credit_balance, created_at, updated_at)
				VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
			`, subscriptionID, testUser.ID, proTier.ID, time.Now(), true, "active", proTier.CreditLimit, time.Now(), time.Now())

			if result.Error != nil {
				return fmt.Errorf("failed to create subscription: %w", result.Error)
			}

			log.Println("Created subscription for test user:", subscriptionID)
		} else {
			return fmt.Errorf("failed to check for subscription: %w", err)
		}
	} else {
		log.Println("Using existing subscription:", subscription.ID)
	}

	// Create API keys for the test user
	if err := seedAPIKeys(testUser.ID); err != nil {
		return fmt.Errorf("failed to seed API keys: %w", err)
	}

	// Create test webhooks
	if err := seedWebhooks(testUser.ID); err != nil {
		return fmt.Errorf("failed to seed webhooks: %w", err)
	}

	return nil
}

// ensureSubscriptionTiers makes sure the subscription tiers exist in the database
func ensureSubscriptionTiers() error {
	var count int64
	database.DB.Table("subscription_tiers").Count(&count)
	if count > 0 {
		log.Println("Subscription tiers already exist")
		return nil
	}

	// Create subscription tiers using raw SQL to avoid type issues
	result := database.DB.Exec(`
		INSERT INTO subscription_tiers
		(name, description, price, credit_limit, features, default_rate_limit_max, default_rate_limit_rate, max_webhooks, advanced_analytics, created_at, updated_at)
		VALUES
		('Free', 'Basic tier with limited credits', 0, 1000, ARRAY['1,000 credits per month', 'Basic API access', 'Standard support', '1 webhook', 'Basic rate limiting'], 10, 0.16666666666666666, 1, false, NOW(), NOW()),
		('Pro', 'Professional tier with more credits', 29.99, 10000, ARRAY['10,000 credits per month', 'Full API access', 'Priority support', 'Detailed analytics', '5 webhooks', 'Enhanced rate limiting'], 60, 1, 5, true, NOW(), NOW()),
		('Enterprise', 'Enterprise tier with unlimited credits', 99.99, 50000, ARRAY['50,000 credits per month', 'Full API access', '24/7 support', 'Advanced analytics', 'Custom integrations', '20 webhooks', 'Custom rate limiting'], 600, 10, 20, true, NOW(), NOW())
	`)

	if result.Error != nil {
		return fmt.Errorf("failed to create subscription tiers: %w", result.Error)
	}

	log.Println("Created subscription tiers")
	return nil
}

func seedAPIKeys(userID uuid.UUID) error {
	// Check if user already has API keys
	var count int64
	if err := database.DB.Table("api_keys").Where("user_id = ?", userID).Count(&count).Error; err != nil {
		return fmt.Errorf("failed to check for existing API keys: %w", err)
	}

	if count > 0 {
		log.Printf("User already has %d API keys", count)
		return nil
	}

	// Create test API keys
	apiKeys := []struct {
		Name        string
		Permissions []string
		Enabled     bool
	}{
		{
			Name:        "Production API Key",
			Permissions: []string{"read", "write"},
			Enabled:     true,
		},
		{
			Name:        "Development API Key",
			Permissions: []string{"read", "write"},
			Enabled:     true,
		},
		{
			Name:        "Test API Key",
			Permissions: []string{"read"},
			Enabled:     false,
		},
	}

	for _, apiKey := range apiKeys {
		id := uuid.New()

		// Determine key prefix based on name
		keyPrefix := "test"
		if apiKey.Name == "Production API Key" {
			keyPrefix = "prod"
		} else if apiKey.Name == "Development API Key" {
			keyPrefix = "dev"
		}

		key := fmt.Sprintf("pk_%s_%s", keyPrefix, id.String()[0:8])

		// Convert permissions to a string array representation
		permissionsStr := "ARRAY["
		for i, perm := range apiKey.Permissions {
			if i > 0 {
				permissionsStr += ", "
			}
			permissionsStr += fmt.Sprintf("'%s'", perm)
		}
		permissionsStr += "]"

		// Insert API key using raw SQL
		query := fmt.Sprintf(`
			INSERT INTO api_keys (id, user_id, name, key, enabled, permissions, rate_limit_max, rate_limit_rate, created_at, updated_at)
			VALUES ('%s', '%s', '%s', '%s', %t, %s, %d, %f, '%s', '%s')
		`, id, userID, apiKey.Name, key, apiKey.Enabled,
			permissionsStr, 60, 1.0, time.Now().Format("2006-01-02 15:04:05"),
			time.Now().Format("2006-01-02 15:04:05"))

		if err := database.DB.Exec(query).Error; err != nil {
			return fmt.Errorf("failed to create API key %s: %w", apiKey.Name, err)
		}

		log.Printf("Created API key: %s (ID: %s)", apiKey.Name, id)
	}

	return nil
}

func seedWebhooks(userID uuid.UUID) error {
	// Delete existing webhooks for this user
	if err := database.DB.Exec("DELETE FROM webhook_deliveries WHERE webhook_id IN (SELECT id FROM webhooks WHERE user_id = ?)", userID).Error; err != nil {
		return fmt.Errorf("failed to delete existing webhook deliveries: %w", err)
	}

	if err := database.DB.Exec("DELETE FROM webhooks WHERE user_id = ?", userID).Error; err != nil {
		return fmt.Errorf("failed to delete existing webhooks: %w", err)
	}

	log.Println("Deleted existing webhooks for user:", userID)

	// Create test webhooks
	webhooks := []struct {
		Name   string
		URL    string
		Secret string
		Events []string
		Active bool
	}{
		{
			Name:   "Credit Notifications",
			URL:    "https://webhook.site/12345678-1234-1234-1234-123456789012",
			Secret: "secret123",
			Events: []string{"credit.consumed", "credit.added"},
			Active: true,
		},
		{
			Name:   "API Key Events",
			URL:    "https://webhook.site/*************-4321-4321-************",
			Secret: "apisecret456",
			Events: []string{"api_key.created", "api_key.updated", "api_key.deleted"},
			Active: true,
		},
		{
			Name:   "Subscription Events",
			URL:    "https://webhook.site/abcdef12-3456-7890-abcd-ef1234567890",
			Secret: "subsecret789",
			Events: []string{"subscription.created", "subscription.updated", "subscription.cancelled"},
			Active: false,
		},
	}

	for _, webhook := range webhooks {
		id := uuid.New()

		// Convert events to a string array representation
		eventsStr := "ARRAY["
		for i, event := range webhook.Events {
			if i > 0 {
				eventsStr += ", "
			}
			eventsStr += fmt.Sprintf("'%s'", event)
		}
		eventsStr += "]"

		// Insert webhook using raw SQL
		query := fmt.Sprintf(`
			INSERT INTO webhooks (id, user_id, name, url, secret, events, active, fail_count, created_at, updated_at)
			VALUES ('%s', '%s', '%s', '%s', '%s', %s, %t, %d, '%s', '%s')
		`, id, userID, webhook.Name, webhook.URL, webhook.Secret,
			eventsStr, webhook.Active, 0, time.Now().Format("2006-01-02 15:04:05"),
			time.Now().Format("2006-01-02 15:04:05"))

		if err := database.DB.Exec(query).Error; err != nil {
			return fmt.Errorf("failed to create webhook %s: %w", webhook.Name, err)
		}

		log.Printf("Created webhook: %s (ID: %s)", webhook.Name, id)
	}

	return nil
}
