# Use Node.js as the base image
FROM node:18-alpine

# Set working directory
WORKDIR /app

# Copy package.json and package-lock.json
COPY package.json ./

# Install dependencies
RUN npm install

# Copy the rest of the application code
COPY . .

# Set environment variables
ENV NODE_ENV=development

# Expose the port that Cloud Run expects
EXPOSE 8080

# Start the application in development mode with the port that Cloud Run expects
CMD ["sh", "-c", "PORT=8080 npm run dev -- --port 8080"]
