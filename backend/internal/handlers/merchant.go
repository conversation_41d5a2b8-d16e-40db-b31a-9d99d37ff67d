package handlers

import (
	"math"
	"net/http"
	"regexp"
	"strconv"
	"strings"

	"github.com/adc-credit/backend/internal/database"
	"github.com/adc-credit/backend/internal/models"
	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

// CreateMerchantShop creates a new merchant shop
func CreateMerchantShop(c *gin.Context) {
	user := c.MustGet("user").(models.User)

	type CreateShopRequest struct {
		Name         string `json:"name" binding:"required"`
		Description  string `json:"description"`
		ContactEmail string `json:"contact_email"`
		ContactPhone string `json:"contact_phone"`
	}

	var req CreateShopRequest
	if err := c.Should<PERSON>ind<PERSON>(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	shopID := uuid.New()
	slug := generateSlug(req.Name)
	slug = ensureUniqueSlug(slug, shopID)

	shop := models.MerchantShop{
		ID:           shopID,
		Slug:         slug,
		Name:         req.Name,
		Description:  req.Description,
		ContactEmail: req.ContactEmail,
		ContactPhone: req.ContactPhone,
		OwnerUserID:  user.ID,
	}

	if err := database.DB.Create(&shop).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create merchant shop"})
		return
	}

	c.JSON(http.StatusCreated, shop)
}

// GetMerchantShops returns all merchant shops owned by the current user
func GetMerchantShops(c *gin.Context) {
	user := c.MustGet("user").(models.User)

	var shops []models.MerchantShop
	if err := database.DB.Where("owner_user_id = ?", user.ID).Find(&shops).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch merchant shops"})
		return
	}

	c.JSON(http.StatusOK, shops)
}

// GetMerchantShop returns a specific merchant shop
func GetMerchantShop(c *gin.Context) {
	user := c.MustGet("user").(models.User)
	shopID := c.Param("id")

	var shop models.MerchantShop
	if err := database.DB.First(&shop, "id = ? AND owner_user_id = ?", shopID, user.ID).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Merchant shop not found"})
		return
	}

	c.JSON(http.StatusOK, shop)
}

// GetMerchantShopBySlug returns a specific merchant shop by slug
func GetMerchantShopBySlug(c *gin.Context) {
	user := c.MustGet("user").(models.User)
	shopSlug := c.Param("slug")

	var shop models.MerchantShop
	if err := database.DB.First(&shop, "slug = ? AND owner_user_id = ?", shopSlug, user.ID).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Merchant shop not found"})
		return
	}

	c.JSON(http.StatusOK, shop)
}

// UpdateMerchantShop updates a merchant shop
func UpdateMerchantShop(c *gin.Context) {
	user := c.MustGet("user").(models.User)
	shopID := c.Param("id")

	var shop models.MerchantShop
	if err := database.DB.First(&shop, "id = ? AND owner_user_id = ?", shopID, user.ID).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Merchant shop not found"})
		return
	}

	type UpdateShopRequest struct {
		Name         string `json:"name"`
		Description  string `json:"description"`
		ContactEmail string `json:"contact_email"`
		ContactPhone string `json:"contact_phone"`
	}

	var req UpdateShopRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Update fields if provided
	if req.Name != "" && req.Name != shop.Name {
		shop.Name = req.Name
		// Update slug when name changes
		slug := generateSlug(req.Name)
		shop.Slug = ensureUniqueSlug(slug, shop.ID)
	}
	if req.Description != "" {
		shop.Description = req.Description
	}
	if req.ContactEmail != "" {
		shop.ContactEmail = req.ContactEmail
	}
	if req.ContactPhone != "" {
		shop.ContactPhone = req.ContactPhone
	}

	if err := database.DB.Save(&shop).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update merchant shop"})
		return
	}

	c.JSON(http.StatusOK, shop)
}

// DeleteMerchantShop deletes a merchant shop
func DeleteMerchantShop(c *gin.Context) {
	user := c.MustGet("user").(models.User)
	shopID := c.Param("id")

	var shop models.MerchantShop
	if err := database.DB.First(&shop, "id = ? AND owner_user_id = ?", shopID, user.ID).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Merchant shop not found"})
		return
	}

	if err := database.DB.Delete(&shop).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete merchant shop"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Merchant shop deleted successfully"})
}

// AddShopCustomer adds a customer to a merchant shop
func AddShopCustomer(c *gin.Context) {
	user := c.MustGet("user").(models.User)
	shopID := c.Param("id")

	var shop models.MerchantShop
	if err := database.DB.First(&shop, "id = ? AND owner_user_id = ?", shopID, user.ID).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Merchant shop not found"})
		return
	}

	type AddCustomerRequest struct {
		Email string `json:"email" binding:"required,email"`
		Name  string `json:"name" binding:"required"`
		Phone string `json:"phone"`
	}

	var req AddCustomerRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Check if user exists
	var customerUser models.User
	if err := database.DB.Where("email = ?", req.Email).First(&customerUser).Error; err != nil {
		// User doesn't exist, create a new one
		customerUser = models.User{
			ID:             uuid.New(),
			Email:          req.Email,
			Name:           req.Name,
			Role:           "user",
			IsExternalUser: true,
			GoogleID:       "shop_customer_" + uuid.New().String(), // Set a unique google_id
		}

		if err := database.DB.Create(&customerUser).Error; err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create customer user"})
			return
		}
	}

	// Check if customer already exists for this shop
	var existingCustomer models.ShopCustomer
	if err := database.DB.Where("shop_id = ? AND user_id = ?", shop.ID, customerUser.ID).First(&existingCustomer).Error; err == nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Customer already exists for this shop"})
		return
	}

	// Create shop customer
	shopCustomer := models.ShopCustomer{
		ID:            uuid.New(),
		ShopID:        shop.ID,
		UserID:        customerUser.ID,
		Phone:         req.Phone,
		CreditBalance: 0,
	}

	if err := database.DB.Create(&shopCustomer).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to add customer to shop"})
		return
	}

	c.JSON(http.StatusCreated, shopCustomer)
}

// GetShopCustomers returns customers for a merchant shop with search, filtering, sorting, and pagination
func GetShopCustomers(c *gin.Context) {
	user := c.MustGet("user").(models.User)
	shopID := c.Param("id")

	var shop models.MerchantShop
	if err := database.DB.First(&shop, "id = ? AND owner_user_id = ?", shopID, user.ID).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Merchant shop not found"})
		return
	}

	// Parse query parameters
	search := c.Query("search")
	sortBy := c.DefaultQuery("sortBy", "name")
	sortOrder := c.DefaultQuery("sortOrder", "asc")
	page := c.DefaultQuery("page", "1")
	limit := c.DefaultQuery("limit", "10")
	minCreditBalance := c.Query("minCreditBalance")
	maxCreditBalance := c.Query("maxCreditBalance")

	// Convert string parameters to integers
	pageInt, err := strconv.Atoi(page)
	if err != nil || pageInt < 1 {
		pageInt = 1
	}

	limitInt, err := strconv.Atoi(limit)
	if err != nil || limitInt < 1 || limitInt > 100 {
		limitInt = 10
	}

	// Build the query
	query := database.DB.Table("shop_customers").
		Select("shop_customers.*, users.name, users.email").
		Joins("LEFT JOIN users ON shop_customers.user_id = users.id").
		Where("shop_customers.shop_id = ? AND shop_customers.deleted_at IS NULL", shop.ID)

	// Apply search filter
	if search != "" {
		searchPattern := "%" + strings.ToLower(search) + "%"
		query = query.Where("(LOWER(users.name) LIKE ? OR LOWER(users.email) LIKE ?)", searchPattern, searchPattern)
	}

	// Apply credit balance filters
	if minCreditBalance != "" {
		if minCredit, err := strconv.Atoi(minCreditBalance); err == nil {
			query = query.Where("shop_customers.credit_balance >= ?", minCredit)
		}
	}

	if maxCreditBalance != "" {
		if maxCredit, err := strconv.Atoi(maxCreditBalance); err == nil {
			query = query.Where("shop_customers.credit_balance <= ?", maxCredit)
		}
	}

	// Count total records for pagination
	var total int64
	countQuery := query
	if err := countQuery.Count(&total).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to count customers"})
		return
	}

	// Apply sorting
	var orderClause string
	switch sortBy {
	case "name":
		orderClause = "users.name " + strings.ToUpper(sortOrder)
	case "email":
		orderClause = "users.email " + strings.ToUpper(sortOrder)
	case "credit_balance":
		orderClause = "shop_customers.credit_balance " + strings.ToUpper(sortOrder)
	case "created_at":
		orderClause = "shop_customers.created_at " + strings.ToUpper(sortOrder)
	default:
		orderClause = "users.name ASC"
	}

	// Apply pagination and sorting
	offset := (pageInt - 1) * limitInt
	query = query.Order(orderClause).Offset(offset).Limit(limitInt)

	// Execute query
	var customers []models.ShopCustomer
	if err := query.Find(&customers).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch shop customers"})
		return
	}

	// Manually populate User data since we used a custom query
	for i := range customers {
		var user models.User
		if err := database.DB.Select("id, email, name").First(&user, "id = ?", customers[i].UserID).Error; err == nil {
			customers[i].User = user
		}
	}

	// Calculate pagination metadata
	totalPages := int(math.Ceil(float64(total) / float64(limitInt)))

	// Return paginated response
	response := gin.H{
		"customers":  customers,
		"total":      total,
		"page":       pageInt,
		"limit":      limitInt,
		"totalPages": totalPages,
	}

	c.JSON(http.StatusOK, response)
}

// generateSlug creates a URL-friendly slug from a shop name
func generateSlug(name string) string {
	// Convert to lowercase
	slug := strings.ToLower(name)

	// Replace spaces with hyphens
	slug = strings.ReplaceAll(slug, " ", "-")

	// Remove any character that is not alphanumeric or hyphen
	reg := regexp.MustCompile("[^a-z0-9-]")
	slug = reg.ReplaceAllString(slug, "")

	// Remove multiple consecutive hyphens
	reg = regexp.MustCompile("-+")
	slug = reg.ReplaceAllString(slug, "-")

	// Trim hyphens from beginning and end
	slug = strings.Trim(slug, "-")

	// If slug is empty, use a default
	if slug == "" {
		slug = "shop"
	}

	return slug
}

// ensureUniqueSlug makes sure the slug is unique by appending a number if necessary
func ensureUniqueSlug(slug string, shopID uuid.UUID) string {
	baseSlug := slug
	counter := 1

	for {
		// Check if slug exists for another shop
		var existingShop models.MerchantShop
		err := database.DB.Where("slug = ? AND id != ?", slug, shopID).First(&existingShop).Error

		// If no shop found with this slug, it's unique
		if err != nil {
			return slug
		}

		// Append counter to make it unique
		slug = baseSlug + "-" + string(rune(counter+'0'))
		counter++
	}
}
