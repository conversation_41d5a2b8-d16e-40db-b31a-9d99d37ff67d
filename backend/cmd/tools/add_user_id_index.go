package main

import (
	"log"

	"github.com/adc-credit/backend/internal/database"
	"github.com/joho/godotenv"
)

func main() {
	// Load environment variables
	if err := godotenv.Load(); err != nil {
		log.Println("No .env file found, using environment variables")
	}

	// Initialize database connection
	if err := database.InitDB(); err != nil {
		log.Fatalf("Failed to connect to database: %v", err)
	}

	// Add index on user ID if it doesn't exist
	if err := addUserIDIndex(); err != nil {
		log.Fatalf("Failed to add user ID index: %v", err)
	}

	log.Println("User ID index added successfully!")
}

func addUserIDIndex() error {
	// Check if index exists
	var count int64
	if err := database.DB.Raw("SELECT COUNT(*) FROM pg_indexes WHERE indexname = 'idx_users_id'").Count(&count).Error; err != nil {
		return err
	}

	if count == 0 {
		// Create index if it doesn't exist
		if err := database.DB.Exec("CREATE INDEX idx_users_id ON users (id)").Error; err != nil {
			return err
		}
		log.Println("Created index idx_users_id on users table")
	} else {
		log.Println("Index idx_users_id already exists")
	}

	return nil
}
