package models

import (
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// MerchantShop represents a merchant's shop in the system
type MerchantShop struct {
	ID           uuid.UUID      `gorm:"type:uuid;primary_key;default:gen_random_uuid()" json:"id"`
	Slug         string         `gorm:"uniqueIndex" json:"slug"` // Removed NOT NULL constraint for migration
	Name         string         `json:"name"`
	Description  string         `json:"description"`
	ContactEmail string         `json:"contact_email"`
	ContactPhone string         `json:"contact_phone"`
	OwnerUserID  uuid.UUID      `gorm:"type:uuid;not null" json:"owner_user_id"`
	Owner        User           `json:"-" gorm:"foreignKey:OwnerUserID"`
	Customers    []ShopCustomer `json:"customers,omitempty" gorm:"foreignKey:ShopID"`
	CreditCodes  []CreditCode   `json:"credit_codes,omitempty" gorm:"foreignKey:ShopID"`
	CreatedAt    time.Time      `json:"created_at"`
	UpdatedAt    time.Time      `json:"updated_at"`
	DeletedAt    gorm.DeletedAt `gorm:"index" json:"-"`
}

// ShopCustomer represents a customer of a merchant's shop
type ShopCustomer struct {
	ID            uuid.UUID      `gorm:"type:uuid;primary_key;default:gen_random_uuid()" json:"id"`
	ShopID        uuid.UUID      `gorm:"type:uuid;not null" json:"shop_id"`
	Shop          MerchantShop   `json:"-" gorm:"foreignKey:ShopID"`
	UserID        uuid.UUID      `gorm:"type:uuid;not null" json:"user_id"`
	User          User           `json:"-" gorm:"foreignKey:UserID"`
	Phone         string         `json:"phone"`
	CreditBalance int            `gorm:"default:0" json:"credit_balance"`
	CreatedAt     time.Time      `json:"created_at"`
	UpdatedAt     time.Time      `json:"updated_at"`
	DeletedAt     gorm.DeletedAt `gorm:"index" json:"-"`
}

// CreditCode represents a unique code that can be used to top up credit
type CreditCode struct {
	ID            uuid.UUID      `gorm:"type:uuid;primary_key;default:gen_random_uuid()" json:"id"`
	ShopID        uuid.UUID      `gorm:"type:uuid;not null" json:"shop_id"`
	Shop          MerchantShop   `json:"-" gorm:"foreignKey:ShopID"`
	Code          string         `gorm:"uniqueIndex;not null" json:"code"`
	Amount        int            `json:"amount"`
	Description   string         `json:"description"`
	IsRedeemed    bool           `gorm:"default:false" json:"is_redeemed"`
	RedeemedByID  *uuid.UUID     `gorm:"type:uuid" json:"redeemed_by_id"`
	RedeemedBy    *User          `json:"-" gorm:"foreignKey:RedeemedByID"`
	RedeemedAt    *time.Time     `json:"redeemed_at"`
	ExpiresAt     *time.Time     `json:"expires_at"`
	CreatedAt     time.Time      `json:"created_at"`
	UpdatedAt     time.Time      `json:"updated_at"`
	DeletedAt     gorm.DeletedAt `gorm:"index" json:"-"`
}

// ShopCreditTransaction represents a credit transaction within a merchant's shop
type ShopCreditTransaction struct {
	ID          uuid.UUID      `gorm:"type:uuid;primary_key;default:gen_random_uuid()" json:"id"`
	ShopID      uuid.UUID      `gorm:"type:uuid;not null" json:"shop_id"`
	Shop        MerchantShop   `json:"-" gorm:"foreignKey:ShopID"`
	CustomerID  uuid.UUID      `gorm:"type:uuid;not null" json:"customer_id"`
	Customer    ShopCustomer   `json:"-" gorm:"foreignKey:CustomerID"`
	Type        string         `json:"type"` // "credit_add", "credit_use", "credit_redeem"
	Amount      int            `json:"amount"`
	Description string         `json:"description"`
	Reference   string         `json:"reference"` // Can reference a CreditCode ID
	CreatedAt   time.Time      `json:"created_at"`
	UpdatedAt   time.Time      `json:"updated_at"`
	DeletedAt   gorm.DeletedAt `gorm:"index" json:"-"`
}
