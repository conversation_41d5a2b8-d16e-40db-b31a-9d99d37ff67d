'use client';

import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Button } from '@/components/ui/button';
import { 
  useSubscriptionLimits, 
  formatLimitDisplay, 
  getLimitStatus 
} from '@/hooks/useSubscriptionLimits';
import { 
  Store, 
  Users, 
  Key, 
  GitBranch, 
  QrCode, 
  Webhook, 
  CreditCard,
  BarChart3,
  HeadphonesIcon,
  Crown,
  AlertTriangle,
  CheckCircle
} from 'lucide-react';
import { Skeleton } from '@/components/ui/skeleton';
import Link from 'next/link';

interface LimitItemProps {
  icon: React.ReactNode;
  label: string;
  current: number;
  max: number;
  unlimited: boolean;
  allowed?: boolean;
  unit?: string;
}

const LimitItem: React.FC<LimitItemProps> = ({ 
  icon, 
  label, 
  current, 
  max, 
  unlimited, 
  allowed = true,
  unit = ''
}) => {
  const status = getLimitStatus(current, max, unlimited);
  const percentage = unlimited ? 0 : (current / max) * 100;

  const getStatusColor = () => {
    if (!allowed) return 'text-red-500';
    switch (status) {
      case 'danger': return 'text-red-500';
      case 'warning': return 'text-yellow-500';
      default: return 'text-green-500';
    }
  };

  const getProgressColor = () => {
    if (!allowed) return 'bg-red-500';
    switch (status) {
      case 'danger': return 'bg-red-500';
      case 'warning': return 'bg-yellow-500';
      default: return 'bg-green-500';
    }
  };

  return (
    <div className="flex items-center justify-between p-3 border rounded-lg">
      <div className="flex items-center gap-3">
        <div className={`${getStatusColor()}`}>
          {icon}
        </div>
        <div>
          <p className="font-medium text-sm">{label}</p>
          <p className="text-xs text-muted-foreground">
            {formatLimitDisplay(current, max, unlimited)} {unit}
          </p>
        </div>
      </div>
      <div className="flex items-center gap-2">
        {!unlimited && (
          <div className="w-16">
            <Progress 
              value={Math.min(percentage, 100)} 
              className="h-2"
            />
          </div>
        )}
        {!allowed && (
          <AlertTriangle className="h-4 w-4 text-red-500" />
        )}
        {allowed && unlimited && (
          <CheckCircle className="h-4 w-4 text-green-500" />
        )}
      </div>
    </div>
  );
};

const SubscriptionLimitsCard: React.FC = () => {
  const { data: limits, isLoading, error } = useSubscriptionLimits();

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Crown className="h-5 w-5" />
            Subscription Limits
          </CardTitle>
          <CardDescription>
            Your current subscription usage and limits
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {[...Array(6)].map((_, i) => (
            <Skeleton key={i} className="h-16 w-full" />
          ))}
        </CardContent>
      </Card>
    );
  }

  if (error || !limits) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Crown className="h-5 w-5" />
            Subscription Limits
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <AlertTriangle className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <p className="text-muted-foreground">
              Unable to load subscription limits
            </p>
            <Button variant="outline" className="mt-4" onClick={() => window.location.reload()}>
              Retry
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  const { subscription_tier, limits: userLimits } = limits;

  const getSupportLevelBadge = (level: string) => {
    const variants = {
      community: 'secondary',
      email: 'default',
      priority: 'default',
      dedicated: 'default',
      enterprise: 'default'
    } as const;

    const colors = {
      community: 'bg-gray-100 text-gray-800',
      email: 'bg-blue-100 text-blue-800',
      priority: 'bg-purple-100 text-purple-800',
      dedicated: 'bg-green-100 text-green-800',
      enterprise: 'bg-gold-100 text-gold-800'
    } as const;

    return (
      <Badge variant={variants[level as keyof typeof variants] || 'secondary'} 
             className={colors[level as keyof typeof colors] || ''}>
        {level.charAt(0).toUpperCase() + level.slice(1)} Support
      </Badge>
    );
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <Crown className="h-5 w-5" />
              {subscription_tier.name} Plan
            </CardTitle>
            <CardDescription>
              {subscription_tier.description}
            </CardDescription>
          </div>
          <div className="text-right">
            <p className="text-2xl font-bold">
              ${subscription_tier.price}
              <span className="text-sm font-normal text-muted-foreground">/month</span>
            </p>
            {getSupportLevelBadge(userLimits.support_level)}
          </div>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Shops */}
        <LimitItem
          icon={<Store className="h-4 w-4" />}
          label="Shops"
          current={userLimits.shops.current}
          max={userLimits.shops.max}
          unlimited={userLimits.shops.unlimited}
          allowed={userLimits.shops.allowed}
        />

        {/* QR Codes */}
        <LimitItem
          icon={<QrCode className="h-4 w-4" />}
          label="QR Codes"
          current={userLimits.qr_codes_per_month.current}
          max={userLimits.qr_codes_per_month.max}
          unlimited={userLimits.qr_codes_per_month.unlimited}
          allowed={userLimits.qr_codes_per_month.allowed}
          unit="this month"
        />

        {/* Webhooks */}
        <LimitItem
          icon={<Webhook className="h-4 w-4" />}
          label="Webhooks"
          current={userLimits.webhooks.current}
          max={userLimits.webhooks.max}
          unlimited={false}
          allowed={userLimits.webhooks.allowed}
        />

        {/* Credits */}
        <div className="flex items-center justify-between p-3 border rounded-lg">
          <div className="flex items-center gap-3">
            <CreditCard className="h-4 w-4 text-blue-500" />
            <div>
              <p className="font-medium text-sm">Credits</p>
              <p className="text-xs text-muted-foreground">
                {userLimits.credits.current.toLocaleString()} / {userLimits.credits.limit.toLocaleString()}
              </p>
            </div>
          </div>
          <div className="w-16">
            <Progress 
              value={(userLimits.credits.current / userLimits.credits.limit) * 100} 
              className="h-2"
            />
          </div>
        </div>

        {/* Additional Limits Info */}
        <div className="grid grid-cols-2 gap-4 pt-4 border-t">
          <div className="space-y-2">
            <div className="flex items-center gap-2">
              <Users className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm">Customers per shop</span>
            </div>
            <p className="text-sm font-medium">
              {userLimits.customers_per_shop.unlimited 
                ? 'Unlimited' 
                : userLimits.customers_per_shop.max.toLocaleString()
              }
            </p>
          </div>

          <div className="space-y-2">
            <div className="flex items-center gap-2">
              <Key className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm">API keys per shop</span>
            </div>
            <p className="text-sm font-medium">
              {userLimits.api_keys_per_shop.max}
            </p>
          </div>

          <div className="space-y-2">
            <div className="flex items-center gap-2">
              <GitBranch className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm">Branches per shop</span>
            </div>
            <p className="text-sm font-medium">
              {userLimits.branches_per_shop.unlimited 
                ? 'Unlimited' 
                : userLimits.branches_per_shop.max.toLocaleString()
              }
            </p>
          </div>

          <div className="space-y-2">
            <div className="flex items-center gap-2">
              <BarChart3 className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm">Analytics history</span>
            </div>
            <p className="text-sm font-medium">
              {userLimits.analytics_history_days === 0 
                ? 'Unlimited' 
                : `${userLimits.analytics_history_days} days`
              }
            </p>
          </div>
        </div>

        {/* Allowed Shop Types */}
        <div className="pt-4 border-t">
          <div className="flex items-center gap-2 mb-2">
            <Store className="h-4 w-4 text-muted-foreground" />
            <span className="text-sm font-medium">Allowed Shop Types</span>
          </div>
          <div className="flex flex-wrap gap-2">
            {userLimits.allowed_shop_types.map((type) => (
              <Badge key={type} variant="outline" className="text-xs">
                {type.replace('_', ' ').toUpperCase()}
              </Badge>
            ))}
          </div>
        </div>

        {/* Upgrade CTA */}
        {subscription_tier.name === 'Starter' && (
          <div className="pt-4 border-t">
            <div className="bg-gradient-to-r from-blue-50 to-purple-50 p-4 rounded-lg">
              <h4 className="font-medium text-sm mb-2">Need more resources?</h4>
              <p className="text-xs text-muted-foreground mb-3">
                Upgrade to unlock higher limits and advanced features.
              </p>
              <Link href="/dashboard/subscriptions">
                <Button size="sm" className="w-full">
                  Upgrade Plan
                </Button>
              </Link>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default SubscriptionLimitsCard;
