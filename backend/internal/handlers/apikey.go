package handlers

import (
	"crypto/rand"
	"encoding/base64"
	"net/http"

	"github.com/adc-credit/backend/internal/database"
	"github.com/adc-credit/backend/internal/models"
	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

// GetAPIKeys returns all API keys for the current user
func GetAPIKeys(c *gin.Context) {
	user := c.MustGet("user").(models.User)

	var apiKeys []models.APIKey
	if err := database.DB.Where("user_id = ?", user.ID).Find(&apiKeys).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch API keys"})
		return
	}

	c.JSON(http.StatusOK, apiKeys)
}

// GetAPIKey returns a specific API key
func GetAPIKey(c *gin.Context) {
	user := c.MustGet("user").(models.User)
	id := c.<PERSON>m("id")

	apiKeyID, err := uuid.Parse(id)
	if err != nil {
		c.<PERSON>(http.StatusBadRequest, gin.H{"error": "Invalid API key ID"})
		return
	}

	var apiKey models.APIKey
	if err := database.DB.Where("id = ? AND user_id = ?", apiKeyID, user.ID).First(&apiKey).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "API key not found"})
		return
	}

	c.JSON(http.StatusOK, apiKey)
}

// CreateAPIKey creates a new API key
func CreateAPIKey(c *gin.Context) {
	user := c.MustGet("user").(models.User)

	type CreateAPIKeyRequest struct {
		Name        string   `json:"name" binding:"required"`
		Permissions []string `json:"permissions"`
	}

	var req CreateAPIKeyRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Generate a random API key
	key, err := generateAPIKey()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to generate API key"})
		return
	}

	// Create the API key
	apiKey := models.APIKey{
		ID:          uuid.New(),
		UserID:      user.ID,
		Name:        req.Name,
		Key:         key,
		Enabled:     true,
		Permissions: req.Permissions,
	}

	if err := database.DB.Create(&apiKey).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create API key"})
		return
	}

	c.JSON(http.StatusCreated, apiKey)
}

// UpdateAPIKey updates an existing API key
func UpdateAPIKey(c *gin.Context) {
	user := c.MustGet("user").(models.User)
	id := c.Param("id")

	apiKeyID, err := uuid.Parse(id)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid API key ID"})
		return
	}

	type UpdateAPIKeyRequest struct {
		Name        string   `json:"name"`
		Enabled     *bool    `json:"enabled"`
		Permissions []string `json:"permissions"`
	}

	var req UpdateAPIKeyRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	var apiKey models.APIKey
	if err := database.DB.Where("id = ? AND user_id = ?", apiKeyID, user.ID).First(&apiKey).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "API key not found"})
		return
	}

	// Update fields if provided
	if req.Name != "" {
		apiKey.Name = req.Name
	}
	if req.Enabled != nil {
		apiKey.Enabled = *req.Enabled
	}
	if req.Permissions != nil {
		apiKey.Permissions = req.Permissions
	}

	if err := database.DB.Save(&apiKey).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update API key"})
		return
	}

	c.JSON(http.StatusOK, apiKey)
}

// DeleteAPIKey deletes an API key
func DeleteAPIKey(c *gin.Context) {
	user := c.MustGet("user").(models.User)
	id := c.Param("id")

	apiKeyID, err := uuid.Parse(id)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid API key ID"})
		return
	}

	var apiKey models.APIKey
	if err := database.DB.Where("id = ? AND user_id = ?", apiKeyID, user.ID).First(&apiKey).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "API key not found"})
		return
	}

	if err := database.DB.Delete(&apiKey).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete API key"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "API key deleted successfully"})
}

// generateAPIKey generates a random API key
func generateAPIKey() (string, error) {
	b := make([]byte, 32)
	_, err := rand.Read(b)
	if err != nil {
		return "", err
	}
	return base64.URLEncoding.EncodeToString(b), nil
}
