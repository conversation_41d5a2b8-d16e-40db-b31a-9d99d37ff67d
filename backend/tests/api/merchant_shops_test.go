package api

import (
	"testing"

	"github.com/adc-credit/backend/internal/handlers"
	testutils "github.com/adc-credit/backend/internal/testing"
	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/suite"
)

type MerchantShopsTestSuite struct {
	suite.Suite
	router *gin.Engine
}

func (suite *MerchantShopsTestSuite) SetupSuite() {
	// Setup test environment
	err := testutils.SetupTestEnvironment()
	suite.Require().NoError(err)

	// Setup router with merchant shop routes
	suite.router = gin.New()
	suite.router.Use(gin.Recovery())
	
	// Setup merchant shop routes
	merchantHandler := handlers.NewMerchantHandler(testutils.TestDB)
	api := suite.router.Group("/api/v1")
	{
		shops := api.Group("/merchant-shops")
		{
			shops.GET("", merchantHandler.ListMerchantShops)
			shops.POST("", merchantHandler.CreateMerchantShop)
			shops.GET("/:slug", merchantHandler.GetMerchantShop)
			shops.PUT("/:slug", merchantHandler.UpdateMerchantShop)
			shops.DELETE("/:slug", merchantHandler.DeleteMerchantShop)
			
			// Customer management
			shops.GET("/:slug/customers", merchantHandler.ListShopCustomers)
			shops.POST("/:slug/customers", merchantHandler.AddShopCustomer)
			shops.DELETE("/:slug/customers/:customer_id", merchantHandler.RemoveShopCustomer)
			
			// Credit code management
			shops.GET("/:slug/credit-codes", merchantHandler.ListCreditCodes)
			shops.POST("/:slug/credit-codes", merchantHandler.CreateCreditCode)
			shops.DELETE("/:slug/credit-codes/:code_id", merchantHandler.DeleteCreditCode)
		}
	}
}

func (suite *MerchantShopsTestSuite) TearDownSuite() {
	testutils.CleanupTestEnvironment()
}

func (suite *MerchantShopsTestSuite) SetupTest() {
	testutils.CleanTestData()
}

func (suite *MerchantShopsTestSuite) TestCreateMerchantShop() {
	t := suite.T()

	// Create test user
	user, token := testutils.CreateTestUserWithAuth(t, "<EMAIL>", "Test User")
	defer testutils.CleanupTestUser(t, user.ID)

	// Test creating merchant shop
	createData := map[string]interface{}{
		"name":          "Test Shop",
		"slug":          "test-shop",
		"description":   "A test merchant shop",
		"contact_email": "<EMAIL>",
		"contact_phone": "+1234567890",
	}

	req := testutils.WithAuth(testutils.TestRequest{
		Method: "POST",
		URL:    "/api/v1/merchant-shops",
		Body:   createData,
	}, user.ID, user.Email)

	resp := testutils.MakeRequest(t, suite.router, req)

	// Assertions
	testutils.AssertSuccessResponse(t, resp)
	assert.Equal(t, 201, resp.StatusCode)
	
	// Check response structure
	testutils.AssertJSONResponse(t, resp, map[string]interface{}{
		"name":          "Test Shop",
		"slug":          "test-shop",
		"description":   "A test merchant shop",
		"contact_email": "<EMAIL>",
		"contact_phone": "+1234567890",
	})
	
	testutils.AssertValidUUID(t, resp.Body["id"])
	testutils.AssertValidTimestamp(t, resp.Body["created_at"])
}

func (suite *MerchantShopsTestSuite) TestCreateMerchantShopDuplicateSlug() {
	t := suite.T()

	// Create test user
	user, token := testutils.CreateTestUserWithAuth(t, "<EMAIL>", "Test User")
	defer testutils.CleanupTestUser(t, user.ID)

	// Create first shop
	shop1, err := testutils.CreateTestMerchantShop("Shop 1", "test-shop", user.ID)
	suite.Require().NoError(err)

	// Test creating shop with duplicate slug
	createData := map[string]interface{}{
		"name":          "Shop 2",
		"slug":          "test-shop", // Same slug
		"description":   "Another test shop",
		"contact_email": "<EMAIL>",
		"contact_phone": "+1234567890",
	}

	req := testutils.WithAuth(testutils.TestRequest{
		Method: "POST",
		URL:    "/api/v1/merchant-shops",
		Body:   createData,
	}, user.ID, user.Email)

	resp := testutils.MakeRequest(t, suite.router, req)

	// Should return 409 Conflict or 400 Bad Request
	assert.True(t, resp.StatusCode == 409 || resp.StatusCode == 400)
	testutils.AssertErrorResponse(t, resp, resp.StatusCode)
}

func (suite *MerchantShopsTestSuite) TestListMerchantShops() {
	t := suite.T()

	// Create test user with shops
	user, token := testutils.CreateTestUserWithAuth(t, "<EMAIL>", "Test User")
	defer testutils.CleanupTestUser(t, user.ID)

	// Create test shops
	shop1, err := testutils.CreateTestMerchantShop("Shop 1", "shop-1", user.ID)
	suite.Require().NoError(err)
	shop2, err := testutils.CreateTestMerchantShop("Shop 2", "shop-2", user.ID)
	suite.Require().NoError(err)

	// Test listing merchant shops
	req := testutils.WithAuth(testutils.TestRequest{
		Method: "GET",
		URL:    "/api/v1/merchant-shops",
		QueryParams: map[string]string{
			"limit": "10",
			"page":  "1",
		},
	}, user.ID, user.Email)

	resp := testutils.MakeRequest(t, suite.router, req)

	// Assertions
	testutils.AssertSuccessResponse(t, resp)
	assert.Equal(t, 200, resp.StatusCode)
	
	// Check pagination structure
	testutils.AssertPaginatedResponse(t, resp)
	testutils.AssertArrayResponse(t, resp, 2) // At least 2 shops
}

func (suite *MerchantShopsTestSuite) TestGetMerchantShop() {
	t := suite.T()

	// Create test user with shop
	user, token := testutils.CreateTestUserWithAuth(t, "<EMAIL>", "Test User")
	defer testutils.CleanupTestUser(t, user.ID)

	shop, err := testutils.CreateTestMerchantShop("Test Shop", "test-shop", user.ID)
	suite.Require().NoError(err)

	// Test getting merchant shop
	req := testutils.WithAuth(testutils.TestRequest{
		Method: "GET",
		URL:    "/api/v1/merchant-shops/test-shop",
	}, user.ID, user.Email)

	resp := testutils.MakeRequest(t, suite.router, req)

	// Assertions
	testutils.AssertSuccessResponse(t, resp)
	assert.Equal(t, 200, resp.StatusCode)
	
	// Check response structure
	testutils.AssertJSONResponse(t, resp, map[string]interface{}{
		"name":        shop.Name,
		"slug":        shop.Slug,
		"description": shop.Description,
	})
	
	testutils.AssertValidUUID(t, resp.Body["id"])
}

func (suite *MerchantShopsTestSuite) TestGetMerchantShopNotFound() {
	t := suite.T()

	// Create test user
	user, token := testutils.CreateTestUserWithAuth(t, "<EMAIL>", "Test User")
	defer testutils.CleanupTestUser(t, user.ID)

	// Test getting non-existent shop
	req := testutils.WithAuth(testutils.TestRequest{
		Method: "GET",
		URL:    "/api/v1/merchant-shops/non-existent-shop",
	}, user.ID, user.Email)

	resp := testutils.MakeRequest(t, suite.router, req)

	// Should return 404 Not Found
	testutils.AssertErrorResponse(t, resp, 404)
}

func (suite *MerchantShopsTestSuite) TestUpdateMerchantShop() {
	t := suite.T()

	// Create test user with shop
	user, token := testutils.CreateTestUserWithAuth(t, "<EMAIL>", "Test User")
	defer testutils.CleanupTestUser(t, user.ID)

	shop, err := testutils.CreateTestMerchantShop("Test Shop", "test-shop", user.ID)
	suite.Require().NoError(err)

	// Test updating merchant shop
	updateData := map[string]interface{}{
		"name":        "Updated Shop Name",
		"description": "Updated description",
	}

	req := testutils.WithAuth(testutils.TestRequest{
		Method: "PUT",
		URL:    "/api/v1/merchant-shops/test-shop",
		Body:   updateData,
	}, user.ID, user.Email)

	resp := testutils.MakeRequest(t, suite.router, req)

	// Assertions
	testutils.AssertSuccessResponse(t, resp)
	assert.Equal(t, 200, resp.StatusCode)
	
	// Check updated data
	testutils.AssertJSONResponse(t, resp, map[string]interface{}{
		"name":        "Updated Shop Name",
		"description": "Updated description",
		"slug":        shop.Slug, // Should remain unchanged
	})
}

func (suite *MerchantShopsTestSuite) TestDeleteMerchantShop() {
	t := suite.T()

	// Create test user with shop
	user, token := testutils.CreateTestUserWithAuth(t, "<EMAIL>", "Test User")
	defer testutils.CleanupTestUser(t, user.ID)

	shop, err := testutils.CreateTestMerchantShop("Test Shop", "test-shop", user.ID)
	suite.Require().NoError(err)

	// Test deleting merchant shop
	req := testutils.WithAuth(testutils.TestRequest{
		Method: "DELETE",
		URL:    "/api/v1/merchant-shops/test-shop",
	}, user.ID, user.Email)

	resp := testutils.MakeRequest(t, suite.router, req)

	// Assertions
	testutils.AssertSuccessResponse(t, resp)
	assert.Equal(t, 204, resp.StatusCode)

	// Verify shop is deleted
	var count int64
	testutils.TestDB.Model(&shop).Where("id = ?", shop.ID).Count(&count)
	assert.Equal(t, int64(0), count)
}

func (suite *MerchantShopsTestSuite) TestMerchantShopUnauthorizedAccess() {
	t := suite.T()

	// Create two users
	user1, token1 := testutils.CreateTestUserWithAuth(t, "<EMAIL>", "User 1")
	user2, token2 := testutils.CreateTestUserWithAuth(t, "<EMAIL>", "User 2")
	defer func() {
		testutils.CleanupTestUser(t, user1.ID)
		testutils.CleanupTestUser(t, user2.ID)
	}()

	// Create shop owned by user1
	shop, err := testutils.CreateTestMerchantShop("User 1 Shop", "user1-shop", user1.ID)
	suite.Require().NoError(err)

	// Test user2 trying to access user1's shop
	req := testutils.WithAuth(testutils.TestRequest{
		Method: "GET",
		URL:    "/api/v1/merchant-shops/user1-shop",
	}, user2.ID, user2.Email)

	resp := testutils.MakeRequest(t, suite.router, req)

	// Should return 403 Forbidden or 404 Not Found
	assert.True(t, resp.StatusCode == 403 || resp.StatusCode == 404)
}

func (suite *MerchantShopsTestSuite) TestCreateMerchantShopInvalidData() {
	t := suite.T()

	// Create test user
	user, token := testutils.CreateTestUserWithAuth(t, "<EMAIL>", "Test User")
	defer testutils.CleanupTestUser(t, user.ID)

	// Test with missing required fields
	createData := map[string]interface{}{
		"description": "Shop without name",
	}

	req := testutils.WithAuth(testutils.TestRequest{
		Method: "POST",
		URL:    "/api/v1/merchant-shops",
		Body:   createData,
	}, user.ID, user.Email)

	resp := testutils.MakeRequest(t, suite.router, req)

	// Should return 400 Bad Request
	testutils.AssertErrorResponse(t, resp, 400)
}

func (suite *MerchantShopsTestSuite) TestCreateMerchantShopInvalidEmail() {
	t := suite.T()

	// Create test user
	user, token := testutils.CreateTestUserWithAuth(t, "<EMAIL>", "Test User")
	defer testutils.CleanupTestUser(t, user.ID)

	// Test with invalid email
	createData := map[string]interface{}{
		"name":          "Test Shop",
		"slug":          "test-shop",
		"description":   "A test shop",
		"contact_email": "invalid-email",
		"contact_phone": "+1234567890",
	}

	req := testutils.WithAuth(testutils.TestRequest{
		Method: "POST",
		URL:    "/api/v1/merchant-shops",
		Body:   createData,
	}, user.ID, user.Email)

	resp := testutils.MakeRequest(t, suite.router, req)

	// Should return 400 Bad Request
	testutils.AssertErrorResponse(t, resp, 400)
}

// Run the test suite
func TestMerchantShopsTestSuite(t *testing.T) {
	suite.Run(t, new(MerchantShopsTestSuite))
}
