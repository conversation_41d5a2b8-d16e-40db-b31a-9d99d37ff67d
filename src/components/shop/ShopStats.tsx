'use client';

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import { useGetShopStatsQuery } from "@/lib/api/apiSlice";
import { 
  Users, 
  CreditCard, 
  TrendingUp, 
  TrendingDown, 
  Activity,
  DollarSign,
  Target,
  Clock
} from "lucide-react";

interface ShopStatsProps {
  shopId: string;
}

export function ShopStats({ shopId }: ShopStatsProps) {
  const { data: stats, isLoading, error } = useGetShopStatsQuery(shopId);

  if (isLoading) {
    return <ShopStatsSkeleton />;
  }

  if (error) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="text-center text-muted-foreground">
            Failed to load shop statistics
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!stats) {
    return null;
  }

  const formatNumber = (num: number) => {
    return new Intl.NumberFormat().format(num);
  };

  const formatPercentage = (num: number) => {
    return `${num.toFixed(1)}%`;
  };

  const getRedemptionRateColor = (rate: number) => {
    if (rate >= 80) return "text-green-600";
    if (rate >= 60) return "text-yellow-600";
    return "text-red-600";
  };

  const getRedemptionRateBadgeVariant = (rate: number) => {
    if (rate >= 80) return "default";
    if (rate >= 60) return "secondary";
    return "destructive";
  };

  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
      {/* Total Customers */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Total Customers</CardTitle>
          <Users className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{formatNumber(stats.total_customers)}</div>
          <p className="text-xs text-muted-foreground">
            Active customers in your shop
          </p>
        </CardContent>
      </Card>

      {/* Total Credits Issued */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Credits Issued</CardTitle>
          <CreditCard className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{formatNumber(stats.total_credits_issued)}</div>
          <p className="text-xs text-muted-foreground">
            Total credits generated
          </p>
        </CardContent>
      </Card>

      {/* Credits Redeemed */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Credits Redeemed</CardTitle>
          <TrendingUp className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{formatNumber(stats.total_credits_redeemed)}</div>
          <p className="text-xs text-muted-foreground">
            Successfully used credits
          </p>
        </CardContent>
      </Card>

      {/* Redemption Rate */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Redemption Rate</CardTitle>
          <Target className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className={`text-2xl font-bold ${getRedemptionRateColor(stats.redemption_rate)}`}>
            {formatPercentage(stats.redemption_rate)}
          </div>
          <div className="flex items-center space-x-2">
            <Badge variant={getRedemptionRateBadgeVariant(stats.redemption_rate)}>
              {stats.redemption_rate >= 80 ? 'Excellent' : 
               stats.redemption_rate >= 60 ? 'Good' : 'Needs Improvement'}
            </Badge>
          </div>
        </CardContent>
      </Card>

      {/* Active Credit Codes */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Active Codes</CardTitle>
          <Activity className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{formatNumber(stats.active_credit_codes)}</div>
          <p className="text-xs text-muted-foreground">
            Unused credit codes
          </p>
        </CardContent>
      </Card>

      {/* Customer Balance */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Customer Balance</CardTitle>
          <DollarSign className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{formatNumber(stats.total_customer_balance)}</div>
          <p className="text-xs text-muted-foreground">
            Total credits held by customers
          </p>
        </CardContent>
      </Card>

      {/* Recent Transactions */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Recent Activity</CardTitle>
          <Clock className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{formatNumber(stats.recent_transactions)}</div>
          <p className="text-xs text-muted-foreground">
            Transactions in last 30 days
          </p>
        </CardContent>
      </Card>

      {/* Total Credit Codes */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Total Codes</CardTitle>
          <CreditCard className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{formatNumber(stats.total_credit_codes)}</div>
          <p className="text-xs text-muted-foreground">
            All credit codes generated
          </p>
        </CardContent>
      </Card>
    </div>
  );
}

function ShopStatsSkeleton() {
  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
      {Array.from({ length: 8 }).map((_, i) => (
        <Card key={i}>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <Skeleton className="h-4 w-24" />
            <Skeleton className="h-4 w-4" />
          </CardHeader>
          <CardContent>
            <Skeleton className="h-8 w-16 mb-2" />
            <Skeleton className="h-3 w-32" />
          </CardContent>
        </Card>
      ))}
    </div>
  );
}

export default ShopStats;
