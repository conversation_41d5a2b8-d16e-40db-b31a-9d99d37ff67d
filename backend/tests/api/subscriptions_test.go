package api

import (
	"testing"

	"github.com/adc-credit/backend/internal/handlers"
	testutils "github.com/adc-credit/backend/internal/testing"
	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/suite"
)

type SubscriptionsTestSuite struct {
	suite.Suite
	router *gin.Engine
}

func (suite *SubscriptionsTestSuite) SetupSuite() {
	// Setup test environment
	err := testutils.SetupTestEnvironment()
	suite.Require().NoError(err)

	// Setup router with subscription routes
	suite.router = gin.New()
	suite.router.Use(gin.Recovery())
	
	// Setup subscription routes
	subscriptionHandler := handlers.NewSubscriptionHandler(testutils.TestDB)
	api := suite.router.Group("/api/v1")
	{
		subscriptions := api.Group("/subscriptions")
		{
			subscriptions.GET("", subscriptionHandler.ListSubscriptions)
			subscriptions.POST("", subscriptionHandler.CreateSubscription)
			subscriptions.GET("/:id", subscriptionHandler.GetSubscription)
			subscriptions.PUT("/:id", subscriptionHandler.UpdateSubscription)
			subscriptions.DELETE("/:id", subscriptionHandler.CancelSubscription)
			subscriptions.POST("/:id/upgrade", subscriptionHandler.UpgradeSubscription)
		}
		
		// Subscription tiers
		tiers := api.Group("/subscription-tiers")
		{
			tiers.GET("", subscriptionHandler.ListSubscriptionTiers)
			tiers.GET("/:id", subscriptionHandler.GetSubscriptionTier)
		}
	}
}

func (suite *SubscriptionsTestSuite) TearDownSuite() {
	testutils.CleanupTestEnvironment()
}

func (suite *SubscriptionsTestSuite) SetupTest() {
	testutils.CleanTestData()
}

func (suite *SubscriptionsTestSuite) TestListSubscriptionTiers() {
	t := suite.T()

	// Create test subscription tiers
	tier1, err := testutils.CreateTestSubscriptionTier("Basic", 1000)
	suite.Require().NoError(err)
	tier2, err := testutils.CreateTestSubscriptionTier("Premium", 5000)
	suite.Require().NoError(err)

	// Create test user
	user, token := testutils.CreateTestUserWithAuth(t, "<EMAIL>", "Test User")
	defer testutils.CleanupTestUser(t, user.ID)

	// Test listing subscription tiers
	req := testutils.WithAuth(testutils.TestRequest{
		Method: "GET",
		URL:    "/api/v1/subscription-tiers",
	}, user.ID, user.Email)

	resp := testutils.MakeRequest(t, suite.router, req)

	// Assertions
	testutils.AssertSuccessResponse(t, resp)
	assert.Equal(t, 200, resp.StatusCode)
	
	// Check response structure
	testutils.AssertArrayResponse(t, resp, 2) // At least 2 tiers
	
	// Check tier data structure
	data := resp.Body["data"].([]interface{})
	firstTier := data[0].(map[string]interface{})
	assert.Contains(t, firstTier, "name")
	assert.Contains(t, firstTier, "description")
	assert.Contains(t, firstTier, "price")
	assert.Contains(t, firstTier, "credit_limit")
	assert.Contains(t, firstTier, "features")
}

func (suite *SubscriptionsTestSuite) TestGetSubscriptionTier() {
	t := suite.T()

	// Create test subscription tier
	tier, err := testutils.CreateTestSubscriptionTier("Test Tier", 2000)
	suite.Require().NoError(err)

	// Create test user
	user, token := testutils.CreateTestUserWithAuth(t, "<EMAIL>", "Test User")
	defer testutils.CleanupTestUser(t, user.ID)

	// Test getting subscription tier
	req := testutils.WithAuth(testutils.TestRequest{
		Method: "GET",
		URL:    "/api/v1/subscription-tiers/" + string(rune(tier.ID)),
	}, user.ID, user.Email)

	resp := testutils.MakeRequest(t, suite.router, req)

	// Assertions
	testutils.AssertSuccessResponse(t, resp)
	assert.Equal(t, 200, resp.StatusCode)
	
	// Check response structure
	testutils.AssertJSONResponse(t, resp, map[string]interface{}{
		"name":         tier.Name,
		"description":  tier.Description,
		"credit_limit": float64(tier.CreditLimit),
	})
}

func (suite *SubscriptionsTestSuite) TestCreateSubscription() {
	t := suite.T()

	// Create test subscription tier
	tier, err := testutils.CreateTestSubscriptionTier("Test Tier", 1000)
	suite.Require().NoError(err)

	// Create test user
	user, token := testutils.CreateTestUserWithAuth(t, "<EMAIL>", "Test User")
	defer testutils.CleanupTestUser(t, user.ID)

	// Test creating subscription
	createData := map[string]interface{}{
		"subscription_tier_id": tier.ID,
		"subscription_type":    "personal",
	}

	req := testutils.WithAuth(testutils.TestRequest{
		Method: "POST",
		URL:    "/api/v1/subscriptions",
		Body:   createData,
	}, user.ID, user.Email)

	resp := testutils.MakeRequest(t, suite.router, req)

	// Assertions
	testutils.AssertSuccessResponse(t, resp)
	assert.Equal(t, 201, resp.StatusCode)
	
	// Check response structure
	testutils.AssertJSONResponse(t, resp, map[string]interface{}{
		"subscription_type": "personal",
		"status":           "active",
	})
	
	testutils.AssertValidUUID(t, resp.Body["id"])
	testutils.AssertValidTimestamp(t, resp.Body["created_at"])
	
	// Should include subscription tier info
	assert.Contains(t, resp.Body, "subscription_tier")
}

func (suite *SubscriptionsTestSuite) TestCreateSubscriptionInvalidTier() {
	t := suite.T()

	// Create test user
	user, token := testutils.CreateTestUserWithAuth(t, "<EMAIL>", "Test User")
	defer testutils.CleanupTestUser(t, user.ID)

	// Test creating subscription with invalid tier
	createData := map[string]interface{}{
		"subscription_tier_id": 99999,
		"subscription_type":    "personal",
	}

	req := testutils.WithAuth(testutils.TestRequest{
		Method: "POST",
		URL:    "/api/v1/subscriptions",
		Body:   createData,
	}, user.ID, user.Email)

	resp := testutils.MakeRequest(t, suite.router, req)

	// Should return 400 Bad Request or 404 Not Found
	assert.True(t, resp.StatusCode == 400 || resp.StatusCode == 404)
}

func (suite *SubscriptionsTestSuite) TestListSubscriptions() {
	t := suite.T()

	// Create test user with subscription
	user, subscription, token := testutils.CreateTestUserWithSubscription(t, "<EMAIL>", "Test User", 1000)
	defer testutils.CleanupTestUser(t, user.ID)

	// Test listing subscriptions
	req := testutils.WithAuth(testutils.TestRequest{
		Method: "GET",
		URL:    "/api/v1/subscriptions",
		QueryParams: map[string]string{
			"limit": "10",
			"page":  "1",
		},
	}, user.ID, user.Email)

	resp := testutils.MakeRequest(t, suite.router, req)

	// Assertions
	testutils.AssertSuccessResponse(t, resp)
	assert.Equal(t, 200, resp.StatusCode)
	
	// Check pagination structure
	testutils.AssertPaginatedResponse(t, resp)
	testutils.AssertArrayResponse(t, resp, 1) // At least 1 subscription
}

func (suite *SubscriptionsTestSuite) TestGetSubscription() {
	t := suite.T()

	// Create test user with subscription
	user, subscription, token := testutils.CreateTestUserWithSubscription(t, "<EMAIL>", "Test User", 1000)
	defer testutils.CleanupTestUser(t, user.ID)

	// Test getting subscription
	req := testutils.WithAuth(testutils.TestRequest{
		Method: "GET",
		URL:    "/api/v1/subscriptions/" + subscription.ID.String(),
	}, user.ID, user.Email)

	resp := testutils.MakeRequest(t, suite.router, req)

	// Assertions
	testutils.AssertSuccessResponse(t, resp)
	assert.Equal(t, 200, resp.StatusCode)
	
	// Check response structure
	testutils.AssertJSONResponse(t, resp, map[string]interface{}{
		"subscription_type": subscription.SubscriptionType,
		"status":           subscription.Status,
		"credit_balance":   float64(subscription.CreditBalance),
	})
	
	testutils.AssertValidUUID(t, resp.Body["id"])
	
	// Should include subscription tier info
	assert.Contains(t, resp.Body, "subscription_tier")
}

func (suite *SubscriptionsTestSuite) TestUpdateSubscription() {
	t := suite.T()

	// Create test user with subscription
	user, subscription, token := testutils.CreateTestUserWithSubscription(t, "<EMAIL>", "Test User", 1000)
	defer testutils.CleanupTestUser(t, user.ID)

	// Test updating subscription
	updateData := map[string]interface{}{
		"auto_renew": true,
	}

	req := testutils.WithAuth(testutils.TestRequest{
		Method: "PUT",
		URL:    "/api/v1/subscriptions/" + subscription.ID.String(),
		Body:   updateData,
	}, user.ID, user.Email)

	resp := testutils.MakeRequest(t, suite.router, req)

	// Assertions
	testutils.AssertSuccessResponse(t, resp)
	assert.Equal(t, 200, resp.StatusCode)
	
	// Check updated data
	testutils.AssertJSONResponse(t, resp, map[string]interface{}{
		"auto_renew": true,
	})
}

func (suite *SubscriptionsTestSuite) TestUpgradeSubscription() {
	t := suite.T()

	// Create test subscription tiers
	basicTier, err := testutils.CreateTestSubscriptionTier("Basic", 1000)
	suite.Require().NoError(err)
	premiumTier, err := testutils.CreateTestSubscriptionTier("Premium", 5000)
	suite.Require().NoError(err)

	// Create test user with basic subscription
	user, token := testutils.CreateTestUserWithAuth(t, "<EMAIL>", "Test User")
	subscription, err := testutils.CreateTestSubscription(user.ID, basicTier.ID)
	suite.Require().NoError(err)
	defer testutils.CleanupTestUser(t, user.ID)

	// Test upgrading subscription
	upgradeData := map[string]interface{}{
		"new_tier_id": premiumTier.ID,
	}

	req := testutils.WithAuth(testutils.TestRequest{
		Method: "POST",
		URL:    "/api/v1/subscriptions/" + subscription.ID.String() + "/upgrade",
		Body:   upgradeData,
	}, user.ID, user.Email)

	resp := testutils.MakeRequest(t, suite.router, req)

	// Assertions
	testutils.AssertSuccessResponse(t, resp)
	assert.Equal(t, 200, resp.StatusCode)
	
	// Check that subscription tier was updated
	assert.Contains(t, resp.Body, "subscription_tier")
	tier := resp.Body["subscription_tier"].(map[string]interface{})
	assert.Equal(t, premiumTier.Name, tier["name"])
}

func (suite *SubscriptionsTestSuite) TestCancelSubscription() {
	t := suite.T()

	// Create test user with subscription
	user, subscription, token := testutils.CreateTestUserWithSubscription(t, "<EMAIL>", "Test User", 1000)
	defer testutils.CleanupTestUser(t, user.ID)

	// Test canceling subscription
	req := testutils.WithAuth(testutils.TestRequest{
		Method: "DELETE",
		URL:    "/api/v1/subscriptions/" + subscription.ID.String(),
	}, user.ID, user.Email)

	resp := testutils.MakeRequest(t, suite.router, req)

	// Assertions
	testutils.AssertSuccessResponse(t, resp)
	assert.Equal(t, 200, resp.StatusCode)
	
	// Check that subscription status was updated
	testutils.AssertJSONResponse(t, resp, map[string]interface{}{
		"status": "cancelled",
	})
}

func (suite *SubscriptionsTestSuite) TestSubscriptionUnauthorizedAccess() {
	t := suite.T()

	// Create two users with subscriptions
	user1, subscription1, token1 := testutils.CreateTestUserWithSubscription(t, "<EMAIL>", "User 1", 1000)
	user2, _, token2 := testutils.CreateTestUserWithSubscription(t, "<EMAIL>", "User 2", 1000)
	defer func() {
		testutils.CleanupTestUser(t, user1.ID)
		testutils.CleanupTestUser(t, user2.ID)
	}()

	// Test user2 trying to access user1's subscription
	req := testutils.WithAuth(testutils.TestRequest{
		Method: "GET",
		URL:    "/api/v1/subscriptions/" + subscription1.ID.String(),
	}, user2.ID, user2.Email)

	resp := testutils.MakeRequest(t, suite.router, req)

	// Should return 403 Forbidden or 404 Not Found
	assert.True(t, resp.StatusCode == 403 || resp.StatusCode == 404)
}

// Run the test suite
func TestSubscriptionsTestSuite(t *testing.T) {
	suite.Run(t, new(SubscriptionsTestSuite))
}
