'use client';

import React from 'react';
import { use } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Ta<PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { ShopStats } from "@/components/shop/ShopStats";
import { ShopAPIKeyManager } from "@/components/shop/ShopAPIKeyManager";
import { useGetMerchantShopQuery } from "@/lib/api/apiSlice";
import { Skeleton } from "@/components/ui/skeleton";
import { Button } from "@/components/ui/button";
import { useRouter } from "next/navigation";
import { ArrowLeft, Settings, BarChart3, Key } from "lucide-react";

interface ShopSettingsPageProps {
  params: Promise<{ id: string }>;
}

export default function ShopSettingsPage({ params }: ShopSettingsPageProps) {
  const resolvedParams = use(params);
  const shopId = resolvedParams.id;
  const router = useRouter();

  const { data: shop, isLoading, error } = useGetMerchantShopQuery(shopId);

  if (isLoading) {
    return <ShopSettingsPageSkeleton />;
  }

  if (error || !shop) {
    return (
      <div className="container mx-auto p-6">
        <Card>
          <CardContent className="p-6">
            <div className="text-center">
              <h2 className="text-lg font-semibold mb-2">Shop not found</h2>
              <p className="text-muted-foreground mb-4">
                The shop you're looking for doesn't exist or you don't have access to it.
              </p>
              <Button onClick={() => router.push('/dashboard/shops')}>
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Shops
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center gap-4">
        <Button
          variant="ghost"
          size="sm"
          onClick={() => router.back()}
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back
        </Button>
        <div>
          <h1 className="text-2xl font-bold flex items-center gap-2">
            <Settings className="h-6 w-6" />
            Shop Settings
          </h1>
          <p className="text-muted-foreground">
            Manage settings for {shop.name}
          </p>
        </div>
      </div>

      {/* Shop Info Card */}
      <Card>
        <CardHeader>
          <CardTitle>Shop Information</CardTitle>
          <CardDescription>
            Basic information about your shop
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="text-sm font-medium text-muted-foreground">Shop Name</label>
              <p className="text-lg font-semibold">{shop.name}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-muted-foreground">Shop Type</label>
              <p className="text-lg font-semibold capitalize">{shop.shop_type || 'retail'}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-muted-foreground">Contact Email</label>
              <p className="text-lg">{shop.contact_email || 'Not set'}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-muted-foreground">Contact Phone</label>
              <p className="text-lg">{shop.contact_phone || 'Not set'}</p>
            </div>
            <div className="md:col-span-2">
              <label className="text-sm font-medium text-muted-foreground">Description</label>
              <p className="text-lg">{shop.description || 'No description'}</p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Settings Tabs */}
      <Tabs defaultValue="statistics" className="w-full">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="statistics" className="flex items-center gap-2">
            <BarChart3 className="h-4 w-4" />
            Statistics
          </TabsTrigger>
          <TabsTrigger value="api-keys" className="flex items-center gap-2">
            <Key className="h-4 w-4" />
            API Keys
          </TabsTrigger>
        </TabsList>

        <TabsContent value="statistics" className="mt-6">
          <div className="space-y-4">
            <div>
              <h3 className="text-lg font-semibold mb-2">Shop Statistics</h3>
              <p className="text-muted-foreground mb-4">
                Overview of your shop's performance and activity
              </p>
            </div>
            <ShopStats shopId={shopId} />
          </div>
        </TabsContent>

        <TabsContent value="api-keys" className="mt-6">
          <div className="space-y-4">
            <div>
              <h3 className="text-lg font-semibold mb-2">API Key Management</h3>
              <p className="text-muted-foreground mb-4">
                Create and manage API keys for integrating with your shop
              </p>
            </div>
            <ShopAPIKeyManager shopId={shopId} />
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}

function ShopSettingsPageSkeleton() {
  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Header Skeleton */}
      <div className="flex items-center gap-4">
        <Skeleton className="h-9 w-20" />
        <div>
          <Skeleton className="h-8 w-48 mb-2" />
          <Skeleton className="h-4 w-64" />
        </div>
      </div>

      {/* Shop Info Card Skeleton */}
      <Card>
        <CardHeader>
          <Skeleton className="h-6 w-32 mb-2" />
          <Skeleton className="h-4 w-48" />
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {Array.from({ length: 6 }).map((_, i) => (
              <div key={i} className={i === 5 ? "md:col-span-2" : ""}>
                <Skeleton className="h-4 w-24 mb-2" />
                <Skeleton className="h-6 w-32" />
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Tabs Skeleton */}
      <div className="space-y-4">
        <Skeleton className="h-10 w-full" />
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          {Array.from({ length: 8 }).map((_, i) => (
            <Card key={i}>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <Skeleton className="h-4 w-24" />
                <Skeleton className="h-4 w-4" />
              </CardHeader>
              <CardContent>
                <Skeleton className="h-8 w-16 mb-2" />
                <Skeleton className="h-3 w-32" />
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </div>
  );
}
