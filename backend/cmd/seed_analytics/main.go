package main

import (
	"fmt"
	"log"
	"math/rand"
	"time"

	"github.com/adc-credit/backend/internal/database"
	"github.com/google/uuid"
	"github.com/joho/godotenv"
)

func main() {
	// Load environment variables
	if err := godotenv.Load(); err != nil {
		log.Println("No .env file found, using environment variables")
	}

	// Initialize database connection
	if err := database.InitDB(); err != nil {
		log.Fatalf("Failed to connect to database: %v", err)
	}

	// Get the user <NAME_EMAIL>
	var user struct {
		ID    uuid.UUID
		Email string
	}

	if err := database.DB.Model(&struct {
		ID    uuid.UUID
		Email string
	}{}).Table("users").Select("id, email").Where("email = ?", "<EMAIL>").Scan(&user).Error; err != nil {
		log.Fatalf("Failed to get user: %v", err)
	}

	if user.ID == uuid.Nil {
		log.Fatalf("User <NAME_EMAIL> not found")
	}

	// Use the specified user
	userID := user.ID
	log.Printf("Using user: %s (%s)", userID, user.Email)

	// Create test analytics data
	if err := seedAnalyticsData(userID); err != nil {
		log.Fatalf("Failed to seed analytics data: %v", err)
	}

	log.Println("Analytics data seeded successfully!")
}

func seedAnalyticsData(userID uuid.UUID) error {
	// Delete existing analytics data for this user
	if err := database.DB.Exec("DELETE FROM analytics_data WHERE user_id = ?", userID).Error; err != nil {
		return fmt.Errorf("failed to delete existing analytics data: %w", err)
	}

	log.Println("Deleted existing analytics data for user:", userID)

	// Generate analytics data for the last 90 days
	endDate := time.Now()
	startDate := endDate.AddDate(0, 0, -90)

	// Get API keys for the user
	var apiKeys []struct {
		ID uuid.UUID
	}
	if err := database.DB.Table("api_keys").Where("user_id = ?", userID).Select("id").Scan(&apiKeys).Error; err != nil {
		return fmt.Errorf("failed to get API keys: %w", err)
	}

	if len(apiKeys) == 0 {
		// Create a test API key if none exists
		apiKeyID := uuid.New()
		// Create permissions array string
		permissionsStr := "ARRAY['read', 'write']"

		// Insert API key using raw SQL with direct array syntax
		query := fmt.Sprintf(`
			INSERT INTO api_keys (id, user_id, name, key, enabled, permissions, rate_limit_max, rate_limit_rate, created_at, updated_at)
			VALUES ('%s', '%s', 'Test API Key', 'test_key_%s', %t, %s, %d, %f, '%s', '%s')
		`, apiKeyID, userID, apiKeyID.String()[0:8], true,
			permissionsStr, 60, 1.0, time.Now().Format("2006-01-02 15:04:05"),
			time.Now().Format("2006-01-02 15:04:05"))

		if err := database.DB.Exec(query).Error; err != nil {
			return fmt.Errorf("failed to create test API key: %w", err)
		}

		apiKeys = append(apiKeys, struct{ ID uuid.UUID }{ID: apiKeyID})
		log.Printf("Created test API key: %s", apiKeyID)
	}

	// Sample endpoints
	endpoints := []string{
		"/api/v1/data",
		"/api/v1/analyze",
		"/api/v1/search",
		"/api/v1/process",
		"/api/v1/generate",
	}

	// Generate daily analytics data
	for d := startDate; d.Before(endDate) || d.Equal(endDate); d = d.AddDate(0, 0, 1) {
		// Create overall analytics for the day
		totalRequests := rand.Intn(100) + 50
		totalCredits := totalRequests * (rand.Intn(5) + 1)
		avgResponseTime := rand.Intn(200) + 50
		errorRate := rand.Float64() * 5.0
		p95ResponseTime := avgResponseTime + rand.Intn(100)
		p99ResponseTime := p95ResponseTime + rand.Intn(100)

		// Insert overall analytics
		if err := database.DB.Exec(`
			INSERT INTO analytics_data (
				id, user_id, date, api_key_id, endpoint,
				total_requests, total_credits, avg_response_time,
				error_rate, p95_response_time, p99_response_time,
				created_at, updated_at
			)
			VALUES (?, ?, ?, NULL, '', ?, ?, ?, ?, ?, ?, ?, ?)
		`, uuid.New(), userID, d,
			totalRequests, totalCredits, avgResponseTime,
			errorRate, p95ResponseTime, p99ResponseTime,
			time.Now(), time.Now()).Error; err != nil {
			return fmt.Errorf("failed to create overall analytics for %s: %w", d.Format("2006-01-02"), err)
		}

		// Create endpoint-specific analytics
		for _, endpoint := range endpoints {
			endpointRequests := rand.Intn(30) + 5
			endpointCredits := endpointRequests * (rand.Intn(3) + 1)
			endpointAvgResponseTime := rand.Intn(300) + 30
			endpointErrorRate := rand.Float64() * 8.0
			endpointP95ResponseTime := endpointAvgResponseTime + rand.Intn(150)
			endpointP99ResponseTime := endpointP95ResponseTime + rand.Intn(150)

			// Choose a random API key
			apiKeyID := apiKeys[rand.Intn(len(apiKeys))].ID

			// Insert endpoint analytics
			if err := database.DB.Exec(`
				INSERT INTO analytics_data (
					id, user_id, date, api_key_id, endpoint,
					total_requests, total_credits, avg_response_time,
					error_rate, p95_response_time, p99_response_time,
					created_at, updated_at
				)
				VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
			`, uuid.New(), userID, d, apiKeyID, endpoint,
				endpointRequests, endpointCredits, endpointAvgResponseTime,
				endpointErrorRate, endpointP95ResponseTime, endpointP99ResponseTime,
				time.Now(), time.Now()).Error; err != nil {
				return fmt.Errorf("failed to create endpoint analytics for %s - %s: %w",
					d.Format("2006-01-02"), endpoint, err)
			}
		}

		// Log progress every 10 days
		if d.Day()%10 == 0 {
			log.Printf("Created analytics data for %s", d.Format("2006-01-02"))
		}
	}

	log.Printf("Created analytics data for %d days", int(endDate.Sub(startDate).Hours()/24)+1)
	return nil
}
