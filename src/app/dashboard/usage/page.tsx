"use client";

import { useSession } from "next-auth/react";
import { useEffect } from "react";
import { PageHeader } from "@/components/layout/page-header";
import { RealUsageDetails } from "@/components/dashboard/RealUsageDetails";

export default function UsagePage() {
  const { data: session } = useSession();

  // Store the access token when it changes
  useEffect(() => {
    if (session?.accessToken) {
      sessionStorage.setItem('accessToken', session.accessToken);
    }
  }, [session?.accessToken]);

  return (
    <div className="container py-10">
      <PageHeader
        heading="Usage Statistics"
        description="Detailed view of your API usage and credit consumption"
      />
      <div className="mt-8">
        <RealUsageDetails />
      </div>
    </div>
  );
}
