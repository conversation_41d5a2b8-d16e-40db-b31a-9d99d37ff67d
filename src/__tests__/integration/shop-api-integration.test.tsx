/**
 * @jest-environment jsdom
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { Provider } from 'react-redux';
import { configureStore } from '@reduxjs/toolkit';
import { apiSlice } from '@/lib/api/apiSlice';
import { ShopStats } from '@/components/shop/ShopStats';
import { ShopAPIKeyManager } from '@/components/shop/ShopAPIKeyManager';
import '@testing-library/jest-dom';

// Mock the API responses
const mockShopStats = {
  shop_id: 'test-shop-id',
  shop_name: 'Test Shop',
  shop_type: 'retail',
  total_customers: 25,
  total_credit_codes: 100,
  total_credits_issued: 5000,
  total_credits_redeemed: 3500,
  active_credit_codes: 15,
  recent_transactions: 45,
  total_customer_balance: 1500,
  redemption_rate: 70.0,
};

const mockAPIKeys = [
  {
    id: 'key-1',
    name: 'Production API Key',
    key: 'sk_test_key_1234567890',
    enabled: true,
    permissions: ['read', 'write'],
    created_at: '2024-01-01T00:00:00Z',
  },
  {
    id: 'key-2',
    name: 'Development API Key',
    key: 'sk_test_key_0987654321',
    enabled: false,
    permissions: ['read'],
    created_at: '2024-01-02T00:00:00Z',
  },
];

// Create a mock store
const createMockStore = (initialState = {}) => {
  return configureStore({
    reducer: {
      api: apiSlice.reducer,
    },
    middleware: (getDefaultMiddleware) =>
      getDefaultMiddleware().concat(apiSlice.middleware),
    preloadedState: initialState,
  });
};

// Mock the API slice hooks
jest.mock('@/lib/api/apiSlice', () => ({
  ...jest.requireActual('@/lib/api/apiSlice'),
  useGetShopStatsQuery: jest.fn(),
  useGetShopAPIKeysQuery: jest.fn(),
  useCreateShopAPIKeyMutation: jest.fn(),
  useUpdateShopAPIKeyMutation: jest.fn(),
  useDeleteShopAPIKeyMutation: jest.fn(),
}));

// Mock sonner toast
jest.mock('sonner', () => ({
  toast: {
    success: jest.fn(),
    error: jest.fn(),
  },
}));

// Mock clipboard API
Object.assign(navigator, {
  clipboard: {
    writeText: jest.fn(),
  },
});

describe('Shop API Integration Tests', () => {
  let store: ReturnType<typeof createMockStore>;

  beforeEach(() => {
    store = createMockStore();
    jest.clearAllMocks();
  });

  describe('ShopStats Component', () => {
    it('renders shop statistics correctly', async () => {
      const mockUseGetShopStatsQuery = require('@/lib/api/apiSlice').useGetShopStatsQuery;
      mockUseGetShopStatsQuery.mockReturnValue({
        data: mockShopStats,
        isLoading: false,
        error: null,
      });

      render(
        <Provider store={store}>
          <ShopStats shopId="test-shop-id" />
        </Provider>
      );

      // Check if statistics are displayed
      expect(screen.getByText('25')).toBeInTheDocument(); // Total customers
      expect(screen.getByText('5,000')).toBeInTheDocument(); // Credits issued
      expect(screen.getByText('3,500')).toBeInTheDocument(); // Credits redeemed
      expect(screen.getByText('70.0%')).toBeInTheDocument(); // Redemption rate
      expect(screen.getByText('15')).toBeInTheDocument(); // Active codes
      expect(screen.getByText('1,500')).toBeInTheDocument(); // Customer balance
      expect(screen.getByText('45')).toBeInTheDocument(); // Recent transactions
      expect(screen.getByText('100')).toBeInTheDocument(); // Total codes
    });

    it('shows loading state', () => {
      const mockUseGetShopStatsQuery = require('@/lib/api/apiSlice').useGetShopStatsQuery;
      mockUseGetShopStatsQuery.mockReturnValue({
        data: null,
        isLoading: true,
        error: null,
      });

      render(
        <Provider store={store}>
          <ShopStats shopId="test-shop-id" />
        </Provider>
      );

      // Should show skeleton loading
      expect(document.querySelectorAll('[data-testid="skeleton"]')).toBeTruthy();
    });

    it('shows error state', () => {
      const mockUseGetShopStatsQuery = require('@/lib/api/apiSlice').useGetShopStatsQuery;
      mockUseGetShopStatsQuery.mockReturnValue({
        data: null,
        isLoading: false,
        error: { message: 'Failed to load statistics' },
      });

      render(
        <Provider store={store}>
          <ShopStats shopId="test-shop-id" />
        </Provider>
      );

      expect(screen.getByText('Failed to load shop statistics')).toBeInTheDocument();
    });
  });

  describe('ShopAPIKeyManager Component', () => {
    it('renders API keys correctly', async () => {
      const mockUseGetShopAPIKeysQuery = require('@/lib/api/apiSlice').useGetShopAPIKeysQuery;
      const mockUseCreateShopAPIKeyMutation = require('@/lib/api/apiSlice').useCreateShopAPIKeyMutation;
      const mockUseUpdateShopAPIKeyMutation = require('@/lib/api/apiSlice').useUpdateShopAPIKeyMutation;
      const mockUseDeleteShopAPIKeyMutation = require('@/lib/api/apiSlice').useDeleteShopAPIKeyMutation;

      mockUseGetShopAPIKeysQuery.mockReturnValue({
        data: mockAPIKeys,
        isLoading: false,
        error: null,
      });

      mockUseCreateShopAPIKeyMutation.mockReturnValue([jest.fn(), { isLoading: false }]);
      mockUseUpdateShopAPIKeyMutation.mockReturnValue([jest.fn()]);
      mockUseDeleteShopAPIKeyMutation.mockReturnValue([jest.fn()]);

      render(
        <Provider store={store}>
          <ShopAPIKeyManager shopId="test-shop-id" />
        </Provider>
      );

      // Check if API keys are displayed
      expect(screen.getByText('Production API Key')).toBeInTheDocument();
      expect(screen.getByText('Development API Key')).toBeInTheDocument();
      expect(screen.getByText('Active')).toBeInTheDocument();
      expect(screen.getByText('Disabled')).toBeInTheDocument();
    });

    it('opens create API key dialog', async () => {
      const mockUseGetShopAPIKeysQuery = require('@/lib/api/apiSlice').useGetShopAPIKeysQuery;
      const mockUseCreateShopAPIKeyMutation = require('@/lib/api/apiSlice').useCreateShopAPIKeyMutation;
      const mockUseUpdateShopAPIKeyMutation = require('@/lib/api/apiSlice').useUpdateShopAPIKeyMutation;
      const mockUseDeleteShopAPIKeyMutation = require('@/lib/api/apiSlice').useDeleteShopAPIKeyMutation;

      mockUseGetShopAPIKeysQuery.mockReturnValue({
        data: [],
        isLoading: false,
        error: null,
      });

      mockUseCreateShopAPIKeyMutation.mockReturnValue([jest.fn(), { isLoading: false }]);
      mockUseUpdateShopAPIKeyMutation.mockReturnValue([jest.fn()]);
      mockUseDeleteShopAPIKeyMutation.mockReturnValue([jest.fn()]);

      render(
        <Provider store={store}>
          <ShopAPIKeyManager shopId="test-shop-id" />
        </Provider>
      );

      // Click create API key button
      const createButton = screen.getByText('Create API Key');
      fireEvent.click(createButton);

      // Check if dialog opens
      await waitFor(() => {
        expect(screen.getByText('Create New API Key')).toBeInTheDocument();
        expect(screen.getByLabelText('API Key Name')).toBeInTheDocument();
      });
    });

    it('creates new API key', async () => {
      const mockCreateAPIKey = jest.fn().mockResolvedValue({
        unwrap: () => Promise.resolve({
          id: 'new-key',
          name: 'New API Key',
          key: 'sk_new_key_123',
          enabled: true,
        }),
      });

      const mockUseGetShopAPIKeysQuery = require('@/lib/api/apiSlice').useGetShopAPIKeysQuery;
      const mockUseCreateShopAPIKeyMutation = require('@/lib/api/apiSlice').useCreateShopAPIKeyMutation;
      const mockUseUpdateShopAPIKeyMutation = require('@/lib/api/apiSlice').useUpdateShopAPIKeyMutation;
      const mockUseDeleteShopAPIKeyMutation = require('@/lib/api/apiSlice').useDeleteShopAPIKeyMutation;

      mockUseGetShopAPIKeysQuery.mockReturnValue({
        data: [],
        isLoading: false,
        error: null,
      });

      mockUseCreateShopAPIKeyMutation.mockReturnValue([mockCreateAPIKey, { isLoading: false }]);
      mockUseUpdateShopAPIKeyMutation.mockReturnValue([jest.fn()]);
      mockUseDeleteShopAPIKeyMutation.mockReturnValue([jest.fn()]);

      render(
        <Provider store={store}>
          <ShopAPIKeyManager shopId="test-shop-id" />
        </Provider>
      );

      // Open create dialog
      fireEvent.click(screen.getByText('Create API Key'));

      await waitFor(() => {
        expect(screen.getByLabelText('API Key Name')).toBeInTheDocument();
      });

      // Fill in the form
      const nameInput = screen.getByLabelText('API Key Name');
      fireEvent.change(nameInput, { target: { value: 'New API Key' } });

      // Submit the form
      const submitButton = screen.getByRole('button', { name: /create api key/i });
      fireEvent.click(submitButton);

      // Verify the API was called
      await waitFor(() => {
        expect(mockCreateAPIKey).toHaveBeenCalledWith({
          shopId: 'test-shop-id',
          name: 'New API Key',
          permissions: ['read', 'write'],
        });
      });
    });

    it('shows empty state when no API keys exist', () => {
      const mockUseGetShopAPIKeysQuery = require('@/lib/api/apiSlice').useGetShopAPIKeysQuery;
      const mockUseCreateShopAPIKeyMutation = require('@/lib/api/apiSlice').useCreateShopAPIKeyMutation;
      const mockUseUpdateShopAPIKeyMutation = require('@/lib/api/apiSlice').useUpdateShopAPIKeyMutation;
      const mockUseDeleteShopAPIKeyMutation = require('@/lib/api/apiSlice').useDeleteShopAPIKeyMutation;

      mockUseGetShopAPIKeysQuery.mockReturnValue({
        data: [],
        isLoading: false,
        error: null,
      });

      mockUseCreateShopAPIKeyMutation.mockReturnValue([jest.fn(), { isLoading: false }]);
      mockUseUpdateShopAPIKeyMutation.mockReturnValue([jest.fn()]);
      mockUseDeleteShopAPIKeyMutation.mockReturnValue([jest.fn()]);

      render(
        <Provider store={store}>
          <ShopAPIKeyManager shopId="test-shop-id" />
        </Provider>
      );

      expect(screen.getByText('No API keys found')).toBeInTheDocument();
      expect(screen.getByText('Create your first API key to get started')).toBeInTheDocument();
    });
  });
});
