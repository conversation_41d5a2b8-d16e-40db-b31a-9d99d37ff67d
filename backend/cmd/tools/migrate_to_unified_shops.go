package main

import (
	"fmt"
	"log"

	"github.com/adc-credit/backend/internal/database"
	"github.com/joho/godotenv"
	"gorm.io/gorm"
)

func main() {
	// Load environment variables
	if err := godotenv.Load(); err != nil {
		// Try to load from the project root directory
		if err := godotenv.Load("../../.env"); err != nil {
			log.Println("No .env file found, using environment variables")
		}
	}

	// Initialize database connection
	if err := database.InitDB(); err != nil {
		log.Fatalf("Failed to initialize database: %v", err)
	}

	fmt.Println("Starting migration to unified Shop model...")

	if err := migrateToUnifiedShops(); err != nil {
		log.Fatalf("Migration failed: %v", err)
	}

	fmt.Println("Migration completed successfully!")
}

func migrateToUnifiedShops() error {
	// Begin transaction
	tx := database.DB.Begin()
	if tx.Error != nil {
		return tx.Error
	}

	// Step 1: Create the new shops table
	if err := createShopsTable(tx); err != nil {
		tx.Rollback()
		return fmt.Errorf("failed to create shops table: %w", err)
	}

	// Step 2: Migrate data from organizations to shops
	if err := migrateOrganizationsToShops(tx); err != nil {
		tx.Rollback()
		return fmt.Errorf("failed to migrate organizations: %w", err)
	}

	// Step 3: Migrate data from merchant_shops to shops
	if err := migrateMerchantShopsToShops(tx); err != nil {
		tx.Rollback()
		return fmt.Errorf("failed to migrate merchant shops: %w", err)
	}

	// Step 4: Create the new shop_branches table and migrate branches
	if err := createShopBranchesTableAndMigrate(tx); err != nil {
		tx.Rollback()
		return fmt.Errorf("failed to create shop branches table: %w", err)
	}

	// Step 5: Update foreign key references
	if err := updateForeignKeyReferences(tx); err != nil {
		tx.Rollback()
		return fmt.Errorf("failed to update foreign key references: %w", err)
	}

	// Commit transaction
	return tx.Commit().Error
}

func createShopsTable(tx *gorm.DB) error {
	// Create shops table
	createShopsSQL := `
	CREATE TABLE IF NOT EXISTS shops (
		id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
		slug TEXT UNIQUE,
		name TEXT,
		description TEXT,
		shop_type TEXT DEFAULT 'retail',
		contact_email TEXT,
		contact_phone TEXT,
		owner_user_id UUID NOT NULL,
		created_at TIMESTAMP WITH TIME ZONE,
		updated_at TIMESTAMP WITH TIME ZONE,
		deleted_at TIMESTAMP WITH TIME ZONE,
		CONSTRAINT fk_shops_owner FOREIGN KEY (owner_user_id) REFERENCES users(id)
	);`

	if err := tx.Exec(createShopsSQL).Error; err != nil {
		return err
	}

	// Create indexes
	indexSQL := []string{
		"CREATE INDEX IF NOT EXISTS idx_shops_deleted_at ON shops(deleted_at);",
		"CREATE UNIQUE INDEX IF NOT EXISTS idx_shops_slug ON shops(slug);",
		"CREATE INDEX IF NOT EXISTS idx_shops_owner_user_id ON shops(owner_user_id);",
		"CREATE INDEX IF NOT EXISTS idx_shops_shop_type ON shops(shop_type);",
	}

	for _, sql := range indexSQL {
		if err := tx.Exec(sql).Error; err != nil {
			return err
		}
	}

	return nil
}

func migrateOrganizationsToShops(tx *gorm.DB) error {
	// Migrate organizations to shops with shop_type = 'api_service'
	migrateOrgSQL := `
	INSERT INTO shops (id, slug, name, description, shop_type, owner_user_id, created_at, updated_at, deleted_at)
	SELECT id, slug, name, description, 'api_service', owner_user_id, created_at, updated_at, deleted_at
	FROM organizations
	ON CONFLICT (id) DO NOTHING;`

	return tx.Exec(migrateOrgSQL).Error
}

func migrateMerchantShopsToShops(tx *gorm.DB) error {
	// Migrate merchant_shops to shops with shop_type = 'retail'
	migrateMerchantSQL := `
	INSERT INTO shops (id, slug, name, description, shop_type, contact_email, contact_phone, owner_user_id, created_at, updated_at, deleted_at)
	SELECT id, slug, name, description, 'retail', contact_email, contact_phone, owner_user_id, created_at, updated_at, deleted_at
	FROM merchant_shops
	ON CONFLICT (id) DO NOTHING;`

	return tx.Exec(migrateMerchantSQL).Error
}

func createShopBranchesTableAndMigrate(tx *gorm.DB) error {
	// Create shop_branches table
	createShopBranchesSQL := `
	CREATE TABLE IF NOT EXISTS shop_branches (
		id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
		shop_id UUID NOT NULL,
		name TEXT,
		description TEXT,
		branch_type TEXT DEFAULT 'location',
		contact_email TEXT,
		contact_phone TEXT,
		address TEXT,
		created_at TIMESTAMP WITH TIME ZONE,
		updated_at TIMESTAMP WITH TIME ZONE,
		deleted_at TIMESTAMP WITH TIME ZONE,
		CONSTRAINT fk_shop_branches_shop FOREIGN KEY (shop_id) REFERENCES shops(id)
	);`

	if err := tx.Exec(createShopBranchesSQL).Error; err != nil {
		return err
	}

	// Create indexes
	indexSQL := []string{
		"CREATE INDEX IF NOT EXISTS idx_shop_branches_deleted_at ON shop_branches(deleted_at);",
		"CREATE INDEX IF NOT EXISTS idx_shop_branches_shop_id ON shop_branches(shop_id);",
		"CREATE INDEX IF NOT EXISTS idx_shop_branches_branch_type ON shop_branches(branch_type);",
	}

	for _, sql := range indexSQL {
		if err := tx.Exec(sql).Error; err != nil {
			return err
		}
	}

	// Migrate branches to shop_branches, updating organization_id to shop_id
	migrateBranchesSQL := `
	INSERT INTO shop_branches (id, shop_id, name, description, branch_type, created_at, updated_at, deleted_at)
	SELECT id, organization_id, name, description, 'department', created_at, updated_at, deleted_at
	FROM branches
	ON CONFLICT (id) DO NOTHING;`

	return tx.Exec(migrateBranchesSQL).Error
}

func updateForeignKeyReferences(tx *gorm.DB) error {
	// Add new columns to users table
	alterUsersSQL := []string{
		"ALTER TABLE users ADD COLUMN IF NOT EXISTS shop_id UUID;",
		"ALTER TABLE users ADD COLUMN IF NOT EXISTS shop_branch_id UUID;",
	}

	for _, sql := range alterUsersSQL {
		if err := tx.Exec(sql).Error; err != nil {
			return err
		}
	}

	// Add new columns to api_keys table
	alterAPIKeysSQL := []string{
		"ALTER TABLE api_keys ADD COLUMN IF NOT EXISTS shop_id UUID;",
		"ALTER TABLE api_keys ADD COLUMN IF NOT EXISTS shop_branch_id UUID;",
	}

	for _, sql := range alterAPIKeysSQL {
		if err := tx.Exec(sql).Error; err != nil {
			return err
		}
	}

	// Add new column to subscriptions table
	alterSubscriptionsSQL := "ALTER TABLE subscriptions ADD COLUMN IF NOT EXISTS shop_id UUID;"
	if err := tx.Exec(alterSubscriptionsSQL).Error; err != nil {
		return err
	}

	// Update users table to reference shops instead of organizations
	updateUsersSQL := `
	UPDATE users 
	SET shop_id = organization_id, shop_branch_id = branch_id 
	WHERE organization_id IS NOT NULL;`

	if err := tx.Exec(updateUsersSQL).Error; err != nil {
		return err
	}

	// Update subscriptions table to reference shops instead of merchant_shops
	updateSubscriptionsSQL := `
	UPDATE subscriptions 
	SET shop_id = merchant_shop_id 
	WHERE merchant_shop_id IS NOT NULL;`

	if err := tx.Exec(updateSubscriptionsSQL).Error; err != nil {
		return err
	}

	// Add foreign key constraints
	constraintSQL := []string{
		"ALTER TABLE users ADD CONSTRAINT fk_users_shop FOREIGN KEY (shop_id) REFERENCES shops(id) ON DELETE SET NULL;",
		"ALTER TABLE users ADD CONSTRAINT fk_users_shop_branch FOREIGN KEY (shop_branch_id) REFERENCES shop_branches(id) ON DELETE SET NULL;",
		"ALTER TABLE api_keys ADD CONSTRAINT fk_api_keys_shop FOREIGN KEY (shop_id) REFERENCES shops(id) ON DELETE SET NULL;",
		"ALTER TABLE api_keys ADD CONSTRAINT fk_api_keys_shop_branch FOREIGN KEY (shop_branch_id) REFERENCES shop_branches(id) ON DELETE SET NULL;",
		"ALTER TABLE subscriptions ADD CONSTRAINT fk_subscriptions_shop FOREIGN KEY (shop_id) REFERENCES shops(id) ON DELETE SET NULL;",
	}

	for _, sql := range constraintSQL {
		if err := tx.Exec(sql).Error; err != nil {
			// Log the error but continue - constraints might already exist
			log.Printf("Warning: Failed to add constraint: %v", err)
		}
	}

	return nil
}
