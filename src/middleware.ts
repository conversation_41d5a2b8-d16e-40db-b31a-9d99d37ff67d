import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';
import { getToken } from 'next-auth/jwt';

// This function can be marked `async` if using `await` inside
export async function middleware(request: NextRequest) {
  // Get the pathname of the request
  const path = request.nextUrl.pathname;

  // Check if the path is for API routes that need authentication
  const isApiRoute = path.startsWith('/api/v1') &&
                    !path.startsWith('/api/v1/auth') &&
                    !path.startsWith('/api/v1/health');

  if (isApiRoute) {
    // Get the token from the request using NextAuth.js
    const token = await getToken({
      req: request,
      secret: process.env.NEXTAUTH_SECRET
    });

    // If no token and this is an API route that requires authentication
    if (!token) {
      // Respond with JSON indicating an error
      return new NextResponse(
        JSON.stringify({ error: 'Authentication required' }),
        {
          status: 401,
          headers: { 'content-type': 'application/json' }
        }
      );
    }

    // Add the token to the request headers for the backend
    const requestHeaders = new Headers(request.headers);

    // Check if token has accessToken property
    if (token.accessToken) {
      requestHeaders.set('Authorization', `Bearer ${token.accessToken}`);
    } else {
      console.error('No access token found in token object:', token);
      // Return 401 if no access token is found
      return new NextResponse(
        JSON.stringify({ error: 'No access token found' }),
        {
          status: 401,
          headers: { 'content-type': 'application/json' }
        }
      );
    }

    // Return the request with the modified headers
    return NextResponse.next({
      request: {
        headers: requestHeaders,
      },
    });
  }

  // Continue for non-API routes or public API routes
  return NextResponse.next();
}

// Configure which paths the middleware should run on
export const config = {
  matcher: [
    // Match all API routes
    '/api/:path*',
  ],
};
