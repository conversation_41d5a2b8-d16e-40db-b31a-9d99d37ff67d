export const HeroSection = () => {
  return (
    <section className="@container">
      <div className="p-0 md:p-4">
        <div 
          className="relative flex min-h-[560px] md:min-h-[640px] flex-col gap-8 bg-cover bg-center bg-no-repeat items-center justify-center p-6 md:p-12 rounded-xl overflow-hidden shadow-2xl"
          style={{
            backgroundImage: "linear-gradient(rgba(17, 20, 24, 0.8) 0%, rgba(17, 20, 24, 0.95) 100%), url('https://lh3.googleusercontent.com/aida-public/AB6AXuCQWlNFCBDvgvDDRQ-PHcyV81L-7fg_QPKCoLBAXM4xWyv0Gzm_QjdJnfhFOiUjZVgDAL0zd4pfXut0mo1UnWgrE8GpYuIiUKnTYoT7HpKYWxoUv6_Efy3E57H9r6Qte3mY1JcDLz5NW-rLDnXQPwlawRDpV7oUnYRP_F7Ty-UmVBubOoiKxzRwT4lD0RlkXg51AHf5jNZALa8hel_ZkRosteO4y3AUGapsztehIGh3E0WAoVIDzG1lwlvUmOHDcQKkYD6t3_JQSO9O')"
          }}
        >
          <div className="absolute inset-0 opacity-10 pointer-events-none">
            <div className="absolute inset-0 bg-radial-gradient(ellipse_at_center,transparent_20%,rgba(17,20,24,1)_100%)"></div>
          </div>
          <div className="flex flex-col gap-4 text-center z-10">
            <h1 className="text-white text-5xl font-black leading-tight tracking-tighter md:text-6xl lg:text-7xl">Illuminate Your API Management</h1>
            <p className="text-gray-300 text-base font-normal leading-relaxed md:text-lg max-w-2xl mx-auto">
              Streamline your API operations with our comprehensive suite of tools. Manage keys, analyze usage, and scale your business effortlessly.
            </p>
          </div>
          <div className="flex flex-wrap gap-4 justify-center z-10">
            <button className="flex min-w-[120px] max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-lg h-12 px-6 md:h-14 md:px-8 bg-[#0c7ff2] hover:bg-[#0a6acc] text-white text-base font-semibold leading-normal tracking-[0.015em] transition-colors shadow-lg hover:shadow-xl transform hover:scale-105">
              <span className="truncate">Get Started</span>
            </button>
            <button className="flex min-w-[120px] max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-lg h-12 px-6 md:h-14 md:px-8 bg-[#283039] hover:bg-[#3b4754] text-white text-base font-semibold leading-normal tracking-[0.015em] transition-colors shadow-lg hover:shadow-xl transform hover:scale-105">
              <span className="truncate">Learn More</span>
            </button>
          </div>
        </div>
      </div>
    </section>
  );
};
