package tests

import (
	"testing"

	"github.com/adc-credit/backend/internal/models"
	testutils "github.com/adc-credit/backend/internal/testing"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// TestDatabaseConnection tests that the database connection and setup works
func TestDatabaseConnection(t *testing.T) {
	// Setup test environment
	err := testutils.SetupTestEnvironment()
	require.NoError(t, err, "Should be able to setup test environment")

	// Test that we can connect to the database
	assert.NotNil(t, testutils.TestDB, "Test database should be initialized")

	// Test that we can create a simple record
	user, err := testutils.CreateTestUser("<EMAIL>", "Test User")
	require.NoError(t, err, "Should be able to create test user")
	assert.NotEmpty(t, user.ID, "User should have an ID")
	assert.Equal(t, "<EMAIL>", user.Email, "User email should match")

	// Test that we can query the record
	var foundUser models.User
	err = testutils.TestDB.Where("email = ?", "<EMAIL>").First(&foundUser).Error
	require.NoError(t, err, "Should be able to find the user")
	assert.Equal(t, user.ID, foundUser.ID, "Found user should match created user")

	// Clean up
	testutils.CleanupTestUser(t, user.ID)

	// Verify cleanup worked
	var count int64
	testutils.TestDB.Model(&models.User{}).Where("id = ?", user.ID).Count(&count)
	assert.Equal(t, int64(0), count, "User should be deleted after cleanup")

	// Cleanup test environment
	testutils.CleanupTestEnvironment()
}

// TestTestHelpers tests the test helper functions
func TestTestHelpers(t *testing.T) {
	// Setup test environment
	err := testutils.SetupTestEnvironment()
	require.NoError(t, err)
	defer testutils.CleanupTestEnvironment()

	t.Run("CreateTestUser", func(t *testing.T) {
		user, err := testutils.CreateTestUser("<EMAIL>", "Helper User")
		require.NoError(t, err)
		assert.NotEmpty(t, user.ID)
		assert.Equal(t, "<EMAIL>", user.Email)
		defer testutils.CleanupTestUser(t, user.ID)
	})

	t.Run("CreateTestUserWithAuth", func(t *testing.T) {
		user, token := testutils.CreateTestUserWithAuth(t, "<EMAIL>", "Auth User")
		assert.NotEmpty(t, user.ID)
		assert.NotEmpty(t, token)
		defer testutils.CleanupTestUser(t, user.ID)
	})

	t.Run("CreateTestSubscriptionTier", func(t *testing.T) {
		tier, err := testutils.CreateTestSubscriptionTier("Test Tier", 1000)
		require.NoError(t, err)
		assert.NotEmpty(t, tier.ID)
		assert.Equal(t, "Test Tier", tier.Name)
		assert.Equal(t, 1000, tier.CreditLimit)
	})

	t.Run("CreateTestUserWithSubscription", func(t *testing.T) {
		user, subscription, token := testutils.CreateTestUserWithSubscription(t, "<EMAIL>", "Sub User", 500)
		assert.NotEmpty(t, user.ID)
		assert.NotEmpty(t, subscription.ID)
		assert.NotEmpty(t, token)
		assert.Equal(t, 100, subscription.CreditBalance) // Default credit balance from helper
		defer testutils.CleanupTestUser(t, user.ID)
	})
}
