package handlers

import (
	"net/http"

	"github.com/adc-credit/backend/internal/models"
	"github.com/adc-credit/backend/internal/services"
	"github.com/gin-gonic/gin"
)

var (
	creditResetService = services.NewCreditResetService()
)

// ProcessCreditResets processes all credit resets that are due
// This endpoint can be called manually or by a Cloud Scheduler job
func ProcessCreditResets(c *gin.Context) {
	// Check for API key authentication
	apiKey, exists := c.Get("apiKey")
	if !exists {
		// If no API key, check for admin user
		user, exists := c.Get("user")
		if !exists {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "Authentication required"})
			return
		}

		// Check if user is admin
		typedUser := user.(models.User)
		if typedUser.Role != "admin" {
			c.JSON(http.StatusForbidden, gin.H{"error": "Admin access required"})
			return
		}
	} else {
		// If API key exists, check if it has admin permissions
		typedAPIKey := apiKey.(models.APIKey)
		hasAdminPerm := false
		for _, perm := range typedAPIKey.Permissions {
			if perm == "admin" {
				hasAdminPerm = true
				break
			}
		}

		if !hasAdminPerm {
			c.JSON(http.StatusForbidden, gin.H{"error": "Admin API key required"})
			return
		}
	}

	// Process credit resets
	processedCount, err := creditResetService.ProcessCreditResets()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "Credit resets processed successfully",
		"count":   processedCount,
	})
}

// GetNextCreditResetDate returns the date when the next credit reset will occur
func GetNextCreditResetDate(c *gin.Context) {
	user := c.MustGet("user").(models.User)

	// Get next reset date
	nextDate, err := creditResetService.GetNextResetDate(user.ID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	if nextDate == nil {
		c.JSON(http.StatusOK, gin.H{
			"next_credit_reset_date": nil,
			"message":                "No credit reset scheduled",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"next_credit_reset_date": nextDate,
	})
}

// SetMonthlyCredits sets the monthly credit allocation for a user
func SetMonthlyCredits(c *gin.Context) {
	user := c.MustGet("user").(models.User)
	userID := c.Param("id")

	// Only admin can set monthly credits for other users
	if user.Role != "admin" && user.ID.String() != userID {
		c.JSON(http.StatusForbidden, gin.H{"error": "Admin access required"})
		return
	}

	type SetMonthlyCreditsRequest struct {
		Credits int `json:"credits" binding:"required,min=0"`
	}

	var req SetMonthlyCreditsRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Set monthly credits
	if err := creditResetService.SetMonthlyCredits(user.ID, req.Credits); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message":         "Monthly credits set successfully",
		"monthly_credits": req.Credits,
	})
}
