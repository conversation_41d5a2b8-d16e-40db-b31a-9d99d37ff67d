# Customer Search and Filtering API

## Overview

The enhanced `GET /api/v1/merchant-shops/{shopId}/customers` endpoint now supports advanced search, filtering, sorting, and pagination capabilities for scalable customer management.

## Endpoint

```
GET /api/v1/merchant-shops/{shopId}/customers
```

## Authentication

Requires JWT authentication via `Authorization: Bearer <token>` header.

## Query Parameters

| Parameter | Type | Default | Description |
|-----------|------|---------|-------------|
| `search` | string | - | Search customers by name or email (case-insensitive) |
| `page` | integer | 1 | Page number (1-based) |
| `limit` | integer | 10 | Items per page (1-100) |
| `sortBy` | string | "name" | Sort field: `name`, `email`, `credit_balance`, `created_at` |
| `sortOrder` | string | "asc" | Sort order: `asc` or `desc` |
| `minCreditBalance` | integer | - | Minimum credit balance filter |
| `maxCreditBalance` | integer | - | Maximum credit balance filter |

## Response Format

```json
{
  "customers": [
    {
      "id": "uuid",
      "shop_id": "uuid",
      "user_id": "uuid",
      "phone": "string",
      "credit_balance": 150,
      "created_at": "2024-01-01T00:00:00Z",
      "updated_at": "2024-01-01T00:00:00Z",
      "user": {
        "id": "uuid",
        "email": "<EMAIL>",
        "name": "Customer Name"
      }
    }
  ],
  "total": 150,
  "page": 1,
  "limit": 10,
  "totalPages": 15
}
```

## Example Requests

### 1. Basic Pagination
```bash
GET /api/v1/merchant-shops/{shopId}/customers?page=1&limit=10
```

### 2. Search by Name or Email
```bash
GET /api/v1/merchant-shops/{shopId}/customers?search=john&page=1&limit=10
```

### 3. Sort by Credit Balance (High to Low)
```bash
GET /api/v1/merchant-shops/{shopId}/customers?sortBy=credit_balance&sortOrder=desc
```

### 4. Filter by Credit Range
```bash
GET /api/v1/merchant-shops/{shopId}/customers?minCreditBalance=100&maxCreditBalance=1000
```

### 5. Combined Search, Filter, and Sort
```bash
GET /api/v1/merchant-shops/{shopId}/customers?search=test&sortBy=name&sortOrder=asc&minCreditBalance=0&page=1&limit=5
```

## Frontend Integration

The frontend automatically uses these parameters through the RTK Query implementation:

```typescript
const { data: customersResponse } = useGetShopCustomersQuery({
  shopId: shop.id,
  search: debouncedSearchQuery,
  page: currentPage,
  limit: itemsPerPage,
  sortBy,
  sortOrder,
  minCreditBalance,
  maxCreditBalance,
});
```

## Performance Features

- **Database-level filtering**: All filtering happens at the database level for optimal performance
- **Indexed searches**: Searches use database indexes on user names and emails
- **Efficient pagination**: Uses OFFSET/LIMIT for memory-efficient pagination
- **Debounced frontend**: 300ms debounce prevents excessive API calls

## Error Responses

### 404 - Shop Not Found
```json
{
  "error": "Merchant shop not found"
}
```

### 500 - Database Error
```json
{
  "error": "Failed to fetch shop customers"
}
```

## Database Query Structure

The implementation uses optimized SQL queries:

```sql
SELECT shop_customers.*, users.name, users.email 
FROM shop_customers 
LEFT JOIN users ON shop_customers.user_id = users.id 
WHERE shop_customers.shop_id = ? 
  AND shop_customers.deleted_at IS NULL
  AND (LOWER(users.name) LIKE ? OR LOWER(users.email) LIKE ?)
  AND shop_customers.credit_balance >= ?
  AND shop_customers.credit_balance <= ?
ORDER BY users.name ASC 
OFFSET ? LIMIT ?
```

## Testing

Use the provided test script:

```bash
cd backend
go run test_search_api.go
```

Make sure to update the `shopID` and `authToken` variables in the test file.
