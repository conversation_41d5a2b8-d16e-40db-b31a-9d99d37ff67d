-- Add indexes for optimized customer search and filtering
-- Run this migration to improve performance of the new search API

-- Index for shop_customers table
CREATE INDEX IF NOT EXISTS idx_shop_customers_shop_id ON shop_customers(shop_id);
CREATE INDEX IF NOT EXISTS idx_shop_customers_credit_balance ON shop_customers(credit_balance);
CREATE INDEX IF NOT EXISTS idx_shop_customers_created_at ON shop_customers(created_at);
CREATE INDEX IF NOT EXISTS idx_shop_customers_shop_credit ON shop_customers(shop_id, credit_balance);

-- Index for users table (for search functionality)
CREATE INDEX IF NOT EXISTS idx_users_name_lower ON users(LOWER(name));
CREATE INDEX IF NOT EXISTS idx_users_email_lower ON users(LOWER(email));
CREATE INDEX IF NOT EXISTS idx_users_name_email ON users(name, email);

-- Composite index for common query patterns
CREATE INDEX IF NOT EXISTS idx_shop_customers_composite ON shop_customers(shop_id, credit_balance, created_at);

-- Index for soft deletes
CREATE INDEX IF NOT EXISTS idx_shop_customers_deleted_at ON shop_customers(deleted_at);

-- Add comments for documentation
COMMENT ON INDEX idx_shop_customers_shop_id IS 'Index for filtering customers by shop';
COMMENT ON INDEX idx_shop_customers_credit_balance IS 'Index for credit balance filtering and sorting';
COMMENT ON INDEX idx_shop_customers_created_at IS 'Index for date-based sorting';
COMMENT ON INDEX idx_shop_customers_shop_credit IS 'Composite index for shop + credit filtering';
COMMENT ON INDEX idx_users_name_lower IS 'Index for case-insensitive name search';
COMMENT ON INDEX idx_users_email_lower IS 'Index for case-insensitive email search';
COMMENT ON INDEX idx_users_name_email IS 'Composite index for name and email operations';
COMMENT ON INDEX idx_shop_customers_composite IS 'Composite index for complex queries';
COMMENT ON INDEX idx_shop_customers_deleted_at IS 'Index for soft delete filtering';
