"use client";

import Link from "next/link";
import { usePathname } from "next/navigation";
import { cn } from "@/lib/utils";
import {
  LayoutDashboard,
  Key,
  BarChart3,
  CreditCard,
  Webhook,
  Settings,
  LineChart,
  Store,
} from "lucide-react";

interface SidebarNavProps extends React.HTMLAttributes<HTMLElement> {
  items: {
    href: string;
    title: string;
    icon: React.ReactNode;
  }[];
}

export function Sidebar({ className }: React.HTMLAttributes<HTMLElement>) {
  const items = [
    {
      href: "/dashboard/merchant",
      title: "Overview",
      icon: <LayoutDashboard className="h-5 w-5" />,
    },
    {
      href: "/dashboard/api-keys",
      title: "API Keys",
      icon: <Key className="h-5 w-5" />,
    },
    {
      href: "/dashboard/usage",
      title: "Usage",
      icon: <BarChart3 className="h-5 w-5" />,
    },
    {
      href: "/dashboard/subscriptions",
      title: "Subscriptions",
      icon: <CreditCard className="h-5 w-5" />,
    },
    {
      href: "/dashboard/merchant",
      title: "Shops",
      icon: <Store className="h-5 w-5" />,
    },
    {
      href: "/dashboard/webhooks",
      title: "Webhooks",
      icon: <Webhook className="h-5 w-5" />,
    },
    {
      href: "/dashboard/analytics",
      title: "Analytics",
      icon: <LineChart className="h-5 w-5" />,
    },
    {
      href: "/dashboard/settings",
      title: "Settings",
      icon: <Settings className="h-5 w-5" />,
    },
  ];

  return (
    <nav
      className={cn(
        "hidden md:block border-r h-screen sticky top-0 overflow-y-auto w-64 py-8 px-4 bg-background",
        className
      )}
    >
      <div className="space-y-2">
        <Link href="/dashboard" className="flex items-center space-x-2 px-4 mb-6">
          <span className="font-bold text-xl">ADC Credit</span>
        </Link>
        <SidebarNav items={items} />
      </div>
    </nav>
  );
}

export function SidebarNav({ items, className, ...props }: SidebarNavProps) {
  const pathname = usePathname();

  return (
    <nav className={cn("flex flex-col space-y-1", className)} {...props}>
      {items.map((item) => (
        <Link
          key={item.href}
          href={item.href}
          className={cn(
            "flex items-center gap-3 rounded-md px-4 py-2 text-sm font-medium transition-colors",
            pathname === item.href
              ? "bg-muted text-foreground"
              : "text-muted-foreground hover:bg-muted hover:text-foreground"
          )}
        >
          {item.icon}
          {item.title}
        </Link>
      ))}
    </nav>
  );
}
