"use client";

import { Provider } from 'react-redux';
import { store } from '@/lib/store';
import { ReactNode } from 'react';

interface ReduxProviderProps {
  children: ReactNode;
}

export function ReduxProvider({ children }: ReduxProviderProps) {
  // No need to manually manage the session token
  // NextAuth.js handles this automatically with HTTP-only cookies

  return <Provider store={store}>{children}</Provider>;
}
