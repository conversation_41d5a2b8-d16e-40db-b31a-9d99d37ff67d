import { useState } from 'react';
import { useRouter } from 'next/navigation';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle
} from "@/components/ui/card";
import {
  Tabs,
  TabsContent,
  Ta<PERSON>List,
  TabsTrigger
} from "@/components/ui/tabs";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import {
  Package,
  User,
  Store,
  Coffee,
  Settings,
  AlertCircle,
  ArrowUpDown
} from "lucide-react";
import { Subscription, SubscriptionTier } from "@/types/subscription";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { UpgradeSubscriptionDialog } from "./upgrade-subscription-dialog";

interface MySubscriptionsPanelProps {
  subscriptions: Subscription[];
  tiers: SubscriptionTier[];
  onManage?: (subscription: Subscription) => void;
  onCancel?: (subscription: Subscription) => void;
  onUpgrade?: (subscriptionId: string, newTierId: number, prorate: boolean) => Promise<void>;
}

export function MySubscriptionsPanel({
  subscriptions,
  tiers,
  onManage,
  onCancel,
  onUpgrade
}: MySubscriptionsPanelProps) {
  const router = useRouter();
  const [cancelDialogOpen, setCancelDialogOpen] = useState(false);
  const [subscriptionToCancel, setSubscriptionToCancel] = useState<Subscription | null>(null);
  const [upgradeDialogOpen, setUpgradeDialogOpen] = useState(false);
  const [subscriptionToUpgrade, setSubscriptionToUpgrade] = useState<Subscription | null>(null);
  const [selectedTier, setSelectedTier] = useState<SubscriptionTier | null>(null);
  const [isProcessingUpgrade, setIsProcessingUpgrade] = useState(false);

  // Helper function to get the most recent subscription for each entity
  const getMostRecentSubscriptions = (subs: Subscription[], type: string) => {
    // Filter active subscriptions of the specified type
    const activeSubscriptions = subs.filter(sub => sub.subscription_type === type && sub.status === 'active');

    if (type === 'personal') {
      // For personal subscriptions, just return the most recent one if any
      return activeSubscriptions.length > 0
        ? [activeSubscriptions.sort((a, b) => new Date(b.start_date).getTime() - new Date(a.start_date).getTime())[0]]
        : [];
    } else {
      // For merchant and customer subscriptions, group by entity ID
      const groupedSubscriptions = new Map<string, Subscription>();

      activeSubscriptions.forEach(sub => {
        const entityId = type === 'merchant'
          ? sub.merchant_shop_id
          : sub.shop_customer_id;

        if (!entityId) return;

        // If we haven't seen this entity yet, or this subscription is newer, update it
        if (!groupedSubscriptions.has(entityId) ||
            new Date(sub.start_date).getTime() > new Date(groupedSubscriptions.get(entityId)!.start_date).getTime()) {
          groupedSubscriptions.set(entityId, sub);
        }
      });

      // Return the values (most recent subscription for each entity)
      return Array.from(groupedSubscriptions.values());
    }
  };

  // Get the most recent active subscription for each entity
  const personalSubscriptions = getMostRecentSubscriptions(subscriptions, 'personal');
  const merchantSubscriptions = getMostRecentSubscriptions(subscriptions, 'merchant');
  const customerSubscriptions = getMostRecentSubscriptions(subscriptions, 'customer');

  const handleManageSubscription = (subscription: Subscription) => {
    if (onManage) {
      onManage(subscription);
    }
  };

  const handleCancelClick = (subscription: Subscription) => {
    setSubscriptionToCancel(subscription);
    setCancelDialogOpen(true);
  };

  const handleCancelConfirm = () => {
    if (subscriptionToCancel && onCancel) {
      onCancel(subscriptionToCancel);
      // Dialog will be closed by the parent component after successful cancellation
    }
  };

  const handleUpgradeClick = (subscription: Subscription, tier: SubscriptionTier) => {
    setSubscriptionToUpgrade(subscription);
    setSelectedTier(tier);
    setUpgradeDialogOpen(true);
  };

  const handleUpgradeConfirm = async (subscriptionId: string, newTierId: number, prorate: boolean) => {
    if (!onUpgrade) return;

    setIsProcessingUpgrade(true);
    try {
      await onUpgrade(subscriptionId, newTierId, prorate);
      setUpgradeDialogOpen(false);
      setSubscriptionToUpgrade(null);
      setSelectedTier(null);
    } finally {
      setIsProcessingUpgrade(false);
    }
  };

  return (
    <Card className="mb-8 border shadow-sm">
      <CardHeader className="pb-3">
        <CardTitle>My Subscriptions</CardTitle>
        <CardDescription>
          View and manage your active subscriptions
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="all" className="w-full">
          <TabsList className="grid w-full grid-cols-4 mb-6">
            <TabsTrigger value="all">
              <Package className="h-4 w-4 mr-2" />
              All ({personalSubscriptions.length + merchantSubscriptions.length + customerSubscriptions.length})
            </TabsTrigger>
            <TabsTrigger value="personal">
              <User className="h-4 w-4 mr-2" />
              Personal ({personalSubscriptions.length})
            </TabsTrigger>
            <TabsTrigger value="merchant">
              <Store className="h-4 w-4 mr-2" />
              Merchant ({merchantSubscriptions.length})
            </TabsTrigger>
            <TabsTrigger value="customer">
              <Coffee className="h-4 w-4 mr-2" />
              Customer ({customerSubscriptions.length})
            </TabsTrigger>
          </TabsList>

          <TabsContent value="all">
            {subscriptions.filter(sub => sub.status === 'active').length === 0 ? (
              <Alert>
                <AlertCircle className="h-4 w-4 mr-2" />
                <AlertDescription>
                  You don't have any active subscriptions. Subscribe to a plan below to get started.
                </AlertDescription>
              </Alert>
            ) : (
              <div className="space-y-6">
                {personalSubscriptions.length > 0 && (
                  <div>
                    <h3 className="text-lg font-semibold mb-3">Personal Subscriptions</h3>
                    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                      {personalSubscriptions.map((sub) => {
                        const tier = tiers.find(t => t.id === sub.subscription_tier_id);
                        return (
                          <Card key={sub.id} className="border shadow-sm">
                            <CardHeader className="pb-2">
                              <div className="flex justify-between items-center">
                                <CardTitle className="text-lg">{tier?.name || 'Unknown Tier'}</CardTitle>
                                <Badge>{sub.status}</Badge>
                              </div>
                              <CardDescription>Personal Subscription</CardDescription>
                            </CardHeader>
                            <CardContent>
                              <div className="space-y-3">
                                <div className="flex justify-between text-sm">
                                  <span className="text-muted-foreground">Credit Balance:</span>
                                  <span className="font-medium">{sub.credit_balance}</span>
                                </div>
                                <div className="flex justify-between text-sm">
                                  <span className="text-muted-foreground">Price:</span>
                                  <span className="font-medium">${tier?.price.toFixed(2)}/month</span>
                                </div>
                                <div className="flex justify-between text-sm">
                                  <span className="text-muted-foreground">Started:</span>
                                  <span className="font-medium">{new Date(sub.start_date).toLocaleDateString()}</span>
                                </div>
                                <div className="flex justify-between text-sm">
                                  <span className="text-muted-foreground">Auto-renew:</span>
                                  <span className="font-medium">{sub.auto_renew ? 'Yes' : 'No'}</span>
                                </div>
                              </div>
                            </CardContent>
                            <CardFooter className="pt-0 flex gap-2">
                              <Button variant="outline" size="sm" className="flex-1" onClick={() => handleManageSubscription(sub)}>
                                <Settings className="h-4 w-4 mr-2" />
                                Manage
                              </Button>
                              <Button
                                variant="outline"
                                size="sm"
                                className="flex-1"
                                onClick={() => {
                                  // Find other tiers to upgrade/downgrade to
                                  const otherTiers = tiers.filter(t => t.id !== sub.subscription_tier_id);
                                  if (otherTiers.length > 0) {
                                    // For simplicity, just use the next tier up or down
                                    const currentTier = tiers.find(t => t.id === sub.subscription_tier_id);
                                    if (currentTier) {
                                      // Find the next tier up
                                      const nextTierUp = tiers
                                        .filter(t => t.credit_limit > currentTier.credit_limit)
                                        .sort((a, b) => a.credit_limit - b.credit_limit)[0];

                                      // If there's a tier up, use it, otherwise use the next tier down
                                      if (nextTierUp) {
                                        handleUpgradeClick(sub, nextTierUp);
                                      } else {
                                        const nextTierDown = tiers
                                          .filter(t => t.credit_limit < currentTier.credit_limit)
                                          .sort((a, b) => b.credit_limit - a.credit_limit)[0];

                                        if (nextTierDown) {
                                          handleUpgradeClick(sub, nextTierDown);
                                        }
                                      }
                                    }
                                  }
                                }}
                              >
                                <ArrowUpDown className="h-4 w-4 mr-2" />
                                Change Plan
                              </Button>
                              <Button variant="destructive" size="sm" className="flex-1" onClick={() => handleCancelClick(sub)}>
                                Cancel
                              </Button>
                            </CardFooter>
                          </Card>
                        );
                      })}
                    </div>
                  </div>
                )}

                {/* Similar sections for merchant and customer subscriptions */}
                {/* Merchant Subscriptions */}
                {merchantSubscriptions.length > 0 && (
                  <div>
                    <h3 className="text-lg font-semibold mb-3">Merchant Subscriptions</h3>
                    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                      {merchantSubscriptions.map((sub) => {
                        const tier = tiers.find(t => t.id === sub.subscription_tier_id);
                        return (
                          <Card key={sub.id} className="border shadow-sm">
                            <CardHeader className="pb-2">
                              <div className="flex justify-between items-center">
                                <CardTitle className="text-lg">{tier?.name || 'Unknown Tier'}</CardTitle>
                                <Badge>{sub.status}</Badge>
                              </div>
                              <CardDescription>
                                {sub.merchant_shop?.name || 'Merchant Subscription'}
                              </CardDescription>
                            </CardHeader>
                            <CardContent>
                              <div className="space-y-3">
                                <div className="flex justify-between text-sm">
                                  <span className="text-muted-foreground">Credit Balance:</span>
                                  <span className="font-medium">{sub.credit_balance}</span>
                                </div>
                                <div className="flex justify-between text-sm">
                                  <span className="text-muted-foreground">Price:</span>
                                  <span className="font-medium">${tier?.price.toFixed(2)}/month</span>
                                </div>
                                <div className="flex justify-between text-sm">
                                  <span className="text-muted-foreground">Started:</span>
                                  <span className="font-medium">{new Date(sub.start_date).toLocaleDateString()}</span>
                                </div>
                                <div className="flex justify-between text-sm">
                                  <span className="text-muted-foreground">Auto-renew:</span>
                                  <span className="font-medium">{sub.auto_renew ? 'Yes' : 'No'}</span>
                                </div>
                              </div>
                            </CardContent>
                            <CardFooter className="pt-0 flex gap-2">
                              <Button variant="outline" size="sm" className="flex-1" onClick={() => handleManageSubscription(sub)}>
                                <Settings className="h-4 w-4 mr-2" />
                                Manage
                              </Button>
                              <Button
                                variant="outline"
                                size="sm"
                                className="flex-1"
                                onClick={() => {
                                  // Find other tiers to upgrade/downgrade to
                                  const otherTiers = tiers.filter(t => t.id !== sub.subscription_tier_id);
                                  if (otherTiers.length > 0) {
                                    // For simplicity, just use the next tier up or down
                                    const currentTier = tiers.find(t => t.id === sub.subscription_tier_id);
                                    if (currentTier) {
                                      // Find the next tier up
                                      const nextTierUp = tiers
                                        .filter(t => t.credit_limit > currentTier.credit_limit)
                                        .sort((a, b) => a.credit_limit - b.credit_limit)[0];

                                      // If there's a tier up, use it, otherwise use the next tier down
                                      if (nextTierUp) {
                                        handleUpgradeClick(sub, nextTierUp);
                                      } else {
                                        const nextTierDown = tiers
                                          .filter(t => t.credit_limit < currentTier.credit_limit)
                                          .sort((a, b) => b.credit_limit - a.credit_limit)[0];

                                        if (nextTierDown) {
                                          handleUpgradeClick(sub, nextTierDown);
                                        }
                                      }
                                    }
                                  }
                                }}
                              >
                                <ArrowUpDown className="h-4 w-4 mr-2" />
                                Change Plan
                              </Button>
                              <Button variant="destructive" size="sm" className="flex-1" onClick={() => handleCancelClick(sub)}>
                                Cancel
                              </Button>
                            </CardFooter>
                          </Card>
                        );
                      })}
                    </div>
                  </div>
                )}

                {/* Customer Subscriptions */}
                {customerSubscriptions.length > 0 && (
                  <div>
                    <h3 className="text-lg font-semibold mb-3">Customer Subscriptions</h3>
                    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                      {customerSubscriptions.map((sub) => {
                        const tier = tiers.find(t => t.id === sub.subscription_tier_id);
                        return (
                          <Card key={sub.id} className="border shadow-sm">
                            <CardHeader className="pb-2">
                              <div className="flex justify-between items-center">
                                <CardTitle className="text-lg">{tier?.name || 'Unknown Tier'}</CardTitle>
                                <Badge>{sub.status}</Badge>
                              </div>
                              <CardDescription>
                                {sub.shop_customer?.shop?.name || 'Customer Subscription'}
                              </CardDescription>
                            </CardHeader>
                            <CardContent>
                              <div className="space-y-3">
                                <div className="flex justify-between text-sm">
                                  <span className="text-muted-foreground">Credit Balance:</span>
                                  <span className="font-medium">{sub.credit_balance}</span>
                                </div>
                                <div className="flex justify-between text-sm">
                                  <span className="text-muted-foreground">Price:</span>
                                  <span className="font-medium">${tier?.price.toFixed(2)}/month</span>
                                </div>
                                <div className="flex justify-between text-sm">
                                  <span className="text-muted-foreground">Started:</span>
                                  <span className="font-medium">{new Date(sub.start_date).toLocaleDateString()}</span>
                                </div>
                                <div className="flex justify-between text-sm">
                                  <span className="text-muted-foreground">Auto-renew:</span>
                                  <span className="font-medium">{sub.auto_renew ? 'Yes' : 'No'}</span>
                                </div>
                              </div>
                            </CardContent>
                            <CardFooter className="pt-0 flex gap-2">
                              <Button variant="outline" size="sm" className="flex-1" onClick={() => handleManageSubscription(sub)}>
                                <Settings className="h-4 w-4 mr-2" />
                                Manage
                              </Button>
                              <Button
                                variant="outline"
                                size="sm"
                                className="flex-1"
                                onClick={() => {
                                  // Find other tiers to upgrade/downgrade to
                                  const otherTiers = tiers.filter(t => t.id !== sub.subscription_tier_id);
                                  if (otherTiers.length > 0) {
                                    // For simplicity, just use the next tier up or down
                                    const currentTier = tiers.find(t => t.id === sub.subscription_tier_id);
                                    if (currentTier) {
                                      // Find the next tier up
                                      const nextTierUp = tiers
                                        .filter(t => t.credit_limit > currentTier.credit_limit)
                                        .sort((a, b) => a.credit_limit - b.credit_limit)[0];

                                      // If there's a tier up, use it, otherwise use the next tier down
                                      if (nextTierUp) {
                                        handleUpgradeClick(sub, nextTierUp);
                                      } else {
                                        const nextTierDown = tiers
                                          .filter(t => t.credit_limit < currentTier.credit_limit)
                                          .sort((a, b) => b.credit_limit - a.credit_limit)[0];

                                        if (nextTierDown) {
                                          handleUpgradeClick(sub, nextTierDown);
                                        }
                                      }
                                    }
                                  }
                                }}
                              >
                                <ArrowUpDown className="h-4 w-4 mr-2" />
                                Change Plan
                              </Button>
                              <Button variant="destructive" size="sm" className="flex-1" onClick={() => handleCancelClick(sub)}>
                                Cancel
                              </Button>
                            </CardFooter>
                          </Card>
                        );
                      })}
                    </div>
                  </div>
                )}
              </div>
            )}
          </TabsContent>

          {/* Tab content for personal, merchant, and customer subscriptions */}
          {/* These would be similar to the "all" tab but filtered by type */}
        </Tabs>
      </CardContent>

      {/* Cancel Subscription Dialog */}
      <Dialog open={cancelDialogOpen} onOpenChange={setCancelDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Cancel Subscription</DialogTitle>
            <DialogDescription>
              Are you sure you want to cancel this subscription? This action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          <div className="py-4">
            <Alert>
              <AlertDescription>
                Your subscription will be cancelled immediately. You will still have access to your remaining credits until they are used up.
              </AlertDescription>
            </Alert>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setCancelDialogOpen(false)}>Cancel</Button>
            <Button variant="destructive" onClick={handleCancelConfirm}>Confirm Cancellation</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Upgrade/Downgrade Subscription Dialog */}
      <UpgradeSubscriptionDialog
        open={upgradeDialogOpen}
        onOpenChange={setUpgradeDialogOpen}
        subscription={subscriptionToUpgrade}
        newTier={selectedTier}
        tiers={tiers}
        onUpgrade={handleUpgradeConfirm}
        isProcessing={isProcessingUpgrade}
      />
    </Card>
  );
}
