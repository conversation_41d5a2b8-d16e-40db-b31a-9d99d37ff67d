package services

import (
	"fmt"
	"log"
	"time"

	"github.com/adc-credit/backend/internal/database"
	"github.com/adc-credit/backend/internal/models"
	"github.com/google/uuid"
	"gorm.io/gorm"
)

// ScheduledCreditService handles scheduled credit operations
type ScheduledCreditService struct {
	db             *gorm.DB
	webhookService *WebhookService
}

// NewScheduledCreditService creates a new scheduled credit service
func NewScheduledCreditService() *ScheduledCreditService {
	return &ScheduledCreditService{
		db:             database.DB,
		webhookService: NewWebhookService(),
	}
}

// SetDB sets the database connection for the service
func (s *ScheduledCreditService) SetDB(db *gorm.DB) {
	s.db = db
}

// ProcessScheduledCredits processes all active subscriptions and adds scheduled credits
// This is the main function that will be called by the Cloud Scheduler
func (s *ScheduledCreditService) ProcessScheduledCredits() (int, error) {
	// Get all active subscriptions
	var subscriptions []models.Subscription
	if err := s.db.Where("status = ?", "active").
		Preload("SubscriptionTier").
		Find(&subscriptions).Error; err != nil {
		return 0, fmt.Errorf("failed to fetch active subscriptions: %w", err)
	}

	log.Printf("Found %d active subscriptions to process", len(subscriptions))

	// Process each subscription
	processedCount := 0
	for _, subscription := range subscriptions {
		// Check if it's time to add credits
		if s.shouldAddCredits(subscription) {
			if err := s.AddScheduledCreditsToSubscription(subscription); err != nil {
				log.Printf("Error adding scheduled credits to subscription %s: %v", subscription.ID, err)
				continue
			}
			processedCount++
		}
	}

	return processedCount, nil
}

// shouldAddCredits determines if a subscription should receive scheduled credits
func (s *ScheduledCreditService) shouldAddCredits(subscription models.Subscription) bool {
	// Get the last scheduled credit transaction for this subscription
	var lastTransaction models.Transaction
	result := s.db.Where("subscription_id = ? AND type = ?", subscription.ID, "credit_scheduled").
		Order("created_at DESC").
		First(&lastTransaction)

	// If no previous scheduled transaction, check if subscription is at least 1 day old
	if result.Error == gorm.ErrRecordNotFound {
		// For new subscriptions, only add credits if they're at least 1 day old
		// This prevents double-crediting on the first day
		return time.Since(subscription.StartDate) >= 24*time.Hour
	}

	// If there was an error other than "not found"
	if result.Error != nil {
		log.Printf("Error checking last transaction for subscription %s: %v", subscription.ID, result.Error)
		return false
	}

	// Check if it's been a month since the last scheduled credit
	// For demo purposes, we're using a shorter interval (1 day)
	// In production, you'd use 30 days or calculate based on subscription renewal date
	return time.Since(lastTransaction.CreatedAt) >= 24*time.Hour
}

// AddScheduledCreditsToSubscription adds the scheduled credits to a subscription
func (s *ScheduledCreditService) AddScheduledCreditsToSubscription(subscription models.Subscription) error {
	// Start a transaction
	tx := s.db.Begin()

	// Get the credit amount from the subscription tier
	creditAmount := subscription.SubscriptionTier.CreditLimit

	// Update credit balance
	subscription.CreditBalance += creditAmount
	if err := tx.Save(&subscription).Error; err != nil {
		tx.Rollback()
		return fmt.Errorf("failed to update credit balance: %w", err)
	}

	// Create transaction record
	transaction := models.Transaction{
		ID:             uuid.New(),
		UserID:         subscription.UserID,
		SubscriptionID: subscription.ID,
		Type:           "credit_scheduled",
		Amount:         creditAmount,
		Description:    fmt.Sprintf("Scheduled credit addition for %s subscription", subscription.SubscriptionTier.Name),
	}

	if err := tx.Create(&transaction).Error; err != nil {
		tx.Rollback()
		return fmt.Errorf("failed to create transaction record: %w", err)
	}

	// Commit transaction
	if err := tx.Commit().Error; err != nil {
		return fmt.Errorf("failed to commit transaction: %w", err)
	}

	// Send webhook notification
	go func() {
		webhookData := map[string]interface{}{
			"user_id":         subscription.UserID.String(),
			"credits":         creditAmount,
			"credit_balance":  subscription.CreditBalance,
			"transaction_id":  transaction.ID.String(),
			"subscription_id": subscription.ID.String(),
			"description":     transaction.Description,
			"scheduled":       true,
		}
		s.webhookService.SendWebhook(subscription.UserID, "credit.scheduled", webhookData)
	}()

	log.Printf("Added %d scheduled credits to subscription %s", creditAmount, subscription.ID)
	return nil
}

// GetNextScheduledCreditDate returns the date when the next scheduled credit will be added
func (s *ScheduledCreditService) GetNextScheduledCreditDate(subscriptionID uuid.UUID) (*time.Time, error) {
	// Validate subscription ID
	if subscriptionID == uuid.Nil {
		return nil, nil
	}

	// Get the subscription
	var subscription models.Subscription
	if err := s.db.Where("id = ?", subscriptionID).First(&subscription).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			// Return nil instead of error if subscription not found
			return nil, nil
		}
		return nil, fmt.Errorf("failed to fetch subscription: %w", err)
	}

	// Get the last scheduled credit transaction
	var lastTransaction models.Transaction
	result := s.db.Where("subscription_id = ? AND type = ?", subscriptionID, "credit_scheduled").
		Order("created_at DESC").
		First(&lastTransaction)

	var nextDate time.Time
	if result.Error == gorm.ErrRecordNotFound {
		// If no previous scheduled transaction, base it on subscription start date
		if subscription.StartDate.IsZero() {
			// If start date is not set, use current time
			nextDate = time.Now().Add(24 * time.Hour)
		} else {
			nextDate = subscription.StartDate.Add(24 * time.Hour)
		}
	} else if result.Error != nil {
		return nil, fmt.Errorf("failed to fetch last transaction: %w", result.Error)
	} else {
		// Base it on the last transaction date
		nextDate = lastTransaction.CreatedAt.Add(24 * time.Hour)
	}

	// If the next date is in the past, set it to tomorrow
	if nextDate.Before(time.Now()) {
		tomorrow := time.Now().Add(24 * time.Hour)
		nextDate = time.Date(tomorrow.Year(), tomorrow.Month(), tomorrow.Day(), 0, 0, 0, 0, tomorrow.Location())
	}

	return &nextDate, nil
}
