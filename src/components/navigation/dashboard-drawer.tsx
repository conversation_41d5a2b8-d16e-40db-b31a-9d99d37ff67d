"use client";

import { useState } from "react";
import { usePathname } from "next/navigation";
import Link from "next/link";
import { 
  <PERSON><PERSON>, 
  <PERSON><PERSON><PERSON><PERSON><PERSON>, 
  <PERSON><PERSON><PERSON><PERSON><PERSON>, 
  <PERSON><PERSON><PERSON><PERSON><PERSON>, 
  <PERSON><PERSON><PERSON><PERSON>ger,
  SheetClose
} from "@/components/ui/sheet";
import { Button } from "@/components/ui/button";
import { 
  House, 
  Camera, 
  CreditCard, 
  Storefront, 
  User, 
  List, 
  QrCode,
  Plus,
  ChartBar,
  Gear
} from "phosphor-react";

interface NavigationItem {
  href: string;
  label: string;
  icon: React.ReactNode;
  mode: "merchant" | "customer" | "both";
}

export function DashboardDrawer() {
  const [open, setOpen] = useState(false);
  const pathname = usePathname();
  
  // Determine if we're in merchant or customer mode
  const isMerchantMode = pathname.includes('/dashboard/merchant');
  const isCustomerMode = pathname.includes('/dashboard/customer');
  
  // Navigation items
  const navigationItems: NavigationItem[] = [
    // Merchant navigation items
    {
      href: "/dashboard/merchant",
      label: "Dashboard",
      icon: <House size={24} />,
      mode: "merchant"
    },
    {
      href: "/dashboard/merchant/new",
      label: "New Shop",
      icon: <Plus size={24} />,
      mode: "merchant"
    },
    {
      href: "/dashboard/merchant/credits",
      label: "Credits",
      icon: <CreditCard size={24} />,
      mode: "merchant"
    },
    {
      href: "/dashboard/merchant/codes",
      label: "QR Codes",
      icon: <QrCode size={24} />,
      mode: "merchant"
    },
    {
      href: "/dashboard/merchant/customers",
      label: "Customers",
      icon: <User size={24} />,
      mode: "merchant"
    },
    {
      href: "/dashboard/merchant/analytics",
      label: "Analytics",
      icon: <ChartBar size={24} />,
      mode: "merchant"
    },
    {
      href: "/dashboard/merchant/settings",
      label: "Settings",
      icon: <Gear size={24} />,
      mode: "merchant"
    },
    
    // Customer navigation items
    {
      href: "/dashboard/customer",
      label: "Home",
      icon: <House size={24} />,
      mode: "customer"
    },
    {
      href: "/dashboard/customer/scan",
      label: "Scan",
      icon: <Camera size={24} />,
      mode: "customer"
    },
    {
      href: "/dashboard/customer/redeem",
      label: "Redeem",
      icon: <CreditCard size={24} />,
      mode: "customer"
    },
    {
      href: "/dashboard/customer/shops",
      label: "Shops",
      icon: <Storefront size={24} />,
      mode: "customer"
    },
    {
      href: "/dashboard/customer/profile",
      label: "Profile",
      icon: <User size={24} />,
      mode: "customer"
    },
  ];
  
  // Filter navigation items based on current mode
  const filteredItems = navigationItems.filter(item => 
    item.mode === (isMerchantMode ? "merchant" : "customer") || item.mode === "both"
  );
  
  return (
    <Sheet open={open} onOpenChange={setOpen}>
      <SheetTrigger asChild>
        <Button 
          variant="ghost" 
          size="icon" 
          className="fixed bottom-4 right-4 z-50 h-12 w-12 rounded-full bg-[#e5ccb2] text-[#181510] shadow-lg hover:bg-[#d9b99a]"
        >
          <List size={24} weight="bold" />
        </Button>
      </SheetTrigger>
      <SheetContent side="bottom" className="h-[70vh] rounded-t-xl bg-[#fbfaf9]">
        <SheetHeader className="text-left">
          <SheetTitle className="text-[#181510] text-xl">
            {isMerchantMode ? "Merchant Navigation" : "Customer Navigation"}
          </SheetTitle>
        </SheetHeader>
        <div className="grid grid-cols-3 gap-4 p-4 overflow-y-auto">
          {filteredItems.map((item) => (
            <SheetClose asChild key={item.href}>
              <Link 
                href={item.href}
                className={`flex flex-col items-center justify-center gap-2 rounded-lg p-4 transition-colors ${
                  pathname === item.href 
                    ? "bg-[#e5ccb2] text-[#181510]" 
                    : "bg-[#f1edea] text-[#8a745c] hover:bg-[#e5e0dc]"
                }`}
              >
                <div className="flex h-10 w-10 items-center justify-center rounded-full bg-white">
                  {item.icon}
                </div>
                <span className="text-center text-sm font-medium">{item.label}</span>
              </Link>
            </SheetClose>
          ))}
        </div>
      </SheetContent>
    </Sheet>
  );
}
