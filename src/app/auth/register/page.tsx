"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import Link from "next/link";
import { signIn } from "next-auth/react";

export default function RegisterPage() {
  const router = useRouter();
  const [name, setName] = useState("");
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [errorMessage, setErrorMessage] = useState("");

  // Function to hash password (simple implementation for demo purposes)
  const hashPassword = async (password: string) => {
    // In a real app, you would use bcrypt or another secure hashing algorithm
    // This is just a simple hash for demonstration
    const encoder = new TextEncoder();
    const data = encoder.encode(password);
    const hashBuffer = await crypto.subtle.digest('SHA-256', data);
    const hashArray = Array.from(new Uint8Array(hashBuffer));
    return hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
  };

  // Function to register user locally (fallback when backend is not available)
  const registerUserLocally = async (userData: { name: string; email: string; password: string }) => {
    try {
      // Check if email already exists
      const existingUsers = JSON.parse(localStorage.getItem('users') || '[]');
      const userExists = existingUsers.some((user: any) => user.email === userData.email);

      if (userExists) {
        return { success: false, error: "User with this email already exists" };
      }

      // Hash the password
      const hashedPassword = await hashPassword(userData.password);

      // Create new user
      const newUser = {
        id: crypto.randomUUID(),
        name: userData.name,
        email: userData.email,
        password: hashedPassword, // In a real app, never store passwords in localStorage
        role: "user",
        createdAt: new Date().toISOString()
      };

      // Save to localStorage
      existingUsers.push(newUser);
      localStorage.setItem('users', JSON.stringify(existingUsers));

      return { success: true, user: { ...newUser, password: undefined } };
    } catch (error) {
      console.error("Local registration error:", error);
      return { success: false, error: "Failed to register locally" };
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setErrorMessage("");

    // Validate passwords match
    if (password !== confirmPassword) {
      setErrorMessage("Passwords do not match");
      setIsLoading(false);
      return;
    }

    // Validate password strength
    if (password.length < 8) {
      setErrorMessage("Password must be at least 8 characters long");
      setIsLoading(false);
      return;
    }

    try {
      // Try to register with the backend first
      let backendSuccess = false;

      try {
        const response = await fetch(`${process.env.NEXT_PUBLIC_BACKEND_URL}/api/v1/auth/register`, {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            name,
            email,
            password,
          }),
        });

        const data = await response.json();

        if (response.ok) {
          backendSuccess = true;
        } else {
          console.warn("Backend registration failed:", data.error);
        }
      } catch (backendError) {
        console.warn("Backend not available, falling back to local registration");
      }

      // If backend registration failed, try local registration
      if (!backendSuccess) {
        const result = await registerUserLocally({ name, email, password });

        if (!result.success) {
          setErrorMessage(result.error || "Registration failed");
          setIsLoading(false);
          return;
        }

        console.log("User registered locally:", result.user);
      }

      // Auto sign in after successful registration
      try {
        const result = await signIn("credentials", {
          email,
          password,
          redirect: false,
        });

        if (result?.error) {
          setErrorMessage("Registration successful but sign in failed. Please sign in manually.");
          router.push("/auth/signin");
        } else {
          router.push("/dashboard");
        }
      } catch (signInError) {
        console.error("Sign in error:", signInError);
        setErrorMessage("Registration successful but sign in failed. Please sign in manually.");
        setTimeout(() => {
          router.push("/auth/signin");
        }, 2000);
      }
    } catch (error) {
      console.error("Registration error:", error);
      setErrorMessage("An error occurred during registration");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="flex min-h-screen items-center justify-center bg-gray-50 dark:bg-gray-900 px-4">
      <Card className="w-full max-w-md">
        <CardHeader className="space-y-1 text-center">
          <CardTitle className="text-2xl font-bold">Create an account</CardTitle>
          <CardDescription>
            Enter your information to create an account
          </CardDescription>
        </CardHeader>
        <CardContent>
          {errorMessage && (
            <div className="p-3 mb-4 bg-red-50 text-red-500 text-sm rounded-md">
              {errorMessage}
            </div>
          )}

          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="name">Name</Label>
              <Input
                id="name"
                type="text"
                placeholder="John Doe"
                value={name}
                onChange={(e) => setName(e.target.value)}
                required
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="email">Email</Label>
              <Input
                id="email"
                type="email"
                placeholder="<EMAIL>"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                required
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="password">Password</Label>
              <Input
                id="password"
                type="password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                required
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="confirmPassword">Confirm Password</Label>
              <Input
                id="confirmPassword"
                type="password"
                value={confirmPassword}
                onChange={(e) => setConfirmPassword(e.target.value)}
                required
              />
            </div>
            <Button type="submit" className="w-full" disabled={isLoading}>
              {isLoading ? "Creating account..." : "Create account"}
            </Button>
          </form>
        </CardContent>
        <CardFooter className="flex flex-col space-y-4">
          <div className="text-sm text-center text-gray-500 dark:text-gray-400">
            Already have an account?{" "}
            <Link href="/auth/signin" className="underline hover:text-primary">
              Sign in
            </Link>
          </div>
          <div className="text-sm text-center text-gray-500 dark:text-gray-400">
            By registering, you agree to our{" "}
            <Link href="/terms" className="underline hover:text-primary">
              Terms of Service
            </Link>{" "}
            and{" "}
            <Link href="/privacy" className="underline hover:text-primary">
              Privacy Policy
            </Link>
            .
          </div>
        </CardFooter>
      </Card>
    </div>
  );
}
