package api

import (
	"testing"

	"github.com/adc-credit/backend/internal/handlers"
	testutils "github.com/adc-credit/backend/internal/testing"
	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/suite"
)

type UsersTestSuite struct {
	suite.Suite
	router *gin.Engine
}

func (suite *UsersTestSuite) SetupSuite() {
	// Setup test environment
	err := testutils.SetupTestEnvironment()
	suite.Require().NoError(err)

	// Setup router with user routes
	suite.router = gin.New()
	
	// Add middleware
	suite.router.Use(gin.Recovery())
	
	// Setup user routes
	userHandler := handlers.NewUserHandler(testutils.TestDB)
	api := suite.router.Group("/api/v1")
	{
		users := api.Group("/users")
		{
			users.GET("/me", userHandler.GetCurrentUser)
			users.PUT("/me", userHandler.UpdateCurrentUser)
			users.DELETE("/me", userHandler.DeleteCurrentUser)
			users.GET("/:id", userHandler.GetUser)
			users.GET("", userHandler.ListUsers)
		}
	}
}

func (suite *UsersTestSuite) TearDownSuite() {
	testutils.CleanupTestEnvironment()
}

func (suite *UsersTestSuite) SetupTest() {
	testutils.CleanTestData()
}

func (suite *UsersTestSuite) TestGetCurrentUser() {
	t := suite.T()

	// Create test user
	user, token := testutils.CreateTestUserWithAuth(t, "<EMAIL>", "Test User")
	defer testutils.CleanupTestUser(t, user.ID)

	// Test successful request
	req := testutils.WithAuth(testutils.TestRequest{
		Method: "GET",
		URL:    "/api/v1/users/me",
	}, user.ID, user.Email)

	resp := testutils.MakeRequest(t, suite.router, req)

	// Assertions
	testutils.AssertSuccessResponse(t, resp)
	assert.Equal(t, 200, resp.StatusCode)
	
	// Check response structure
	testutils.AssertJSONResponse(t, resp, map[string]interface{}{
		"email": user.Email,
		"name":  user.Name,
	})
	
	testutils.AssertValidUUID(t, resp.Body["id"])
	testutils.AssertValidTimestamp(t, resp.Body["created_at"])
}

func (suite *UsersTestSuite) TestGetCurrentUserUnauthorized() {
	t := suite.T()

	// Test request without authentication
	req := testutils.TestRequest{
		Method: "GET",
		URL:    "/api/v1/users/me",
	}

	resp := testutils.MakeRequest(t, suite.router, req)

	// Should return 401 Unauthorized
	testutils.AssertErrorResponse(t, resp, 401)
}

func (suite *UsersTestSuite) TestUpdateCurrentUser() {
	t := suite.T()

	// Create test user
	user, token := testutils.CreateTestUserWithAuth(t, "<EMAIL>", "Test User")
	defer testutils.CleanupTestUser(t, user.ID)

	// Test successful update
	updateData := map[string]interface{}{
		"name": "Updated Name",
	}

	req := testutils.WithAuth(testutils.TestRequest{
		Method: "PUT",
		URL:    "/api/v1/users/me",
		Body:   updateData,
	}, user.ID, user.Email)

	resp := testutils.MakeRequest(t, suite.router, req)

	// Assertions
	testutils.AssertSuccessResponse(t, resp)
	assert.Equal(t, 200, resp.StatusCode)
	
	// Check updated data
	testutils.AssertJSONResponse(t, resp, map[string]interface{}{
		"name":  "Updated Name",
		"email": user.Email,
	})
}

func (suite *UsersTestSuite) TestUpdateCurrentUserInvalidData() {
	t := suite.T()

	// Create test user
	user, token := testutils.CreateTestUserWithAuth(t, "<EMAIL>", "Test User")
	defer testutils.CleanupTestUser(t, user.ID)

	// Test with invalid email
	updateData := map[string]interface{}{
		"email": "invalid-email",
	}

	req := testutils.WithAuth(testutils.TestRequest{
		Method: "PUT",
		URL:    "/api/v1/users/me",
		Body:   updateData,
	}, user.ID, user.Email)

	resp := testutils.MakeRequest(t, suite.router, req)

	// Should return 400 Bad Request
	testutils.AssertErrorResponse(t, resp, 400)
}

func (suite *UsersTestSuite) TestGetUser() {
	t := suite.T()

	// Create test users
	user1, token := testutils.CreateTestUserWithAuth(t, "<EMAIL>", "User 1")
	user2, _ := testutils.CreateTestUser("<EMAIL>", "User 2")
	defer func() {
		testutils.CleanupTestUser(t, user1.ID)
		testutils.CleanupTestUser(t, user2.ID)
	}()

	// Test getting another user's info
	req := testutils.WithAuth(testutils.TestRequest{
		Method: "GET",
		URL:    "/api/v1/users/" + user2.ID.String(),
	}, user1.ID, user1.Email)

	resp := testutils.MakeRequest(t, suite.router, req)

	// Assertions
	testutils.AssertSuccessResponse(t, resp)
	assert.Equal(t, 200, resp.StatusCode)
	
	// Check response structure (should not include sensitive data)
	testutils.AssertJSONResponse(t, resp, map[string]interface{}{
		"name": user2.Name,
	})
	
	// Should not include email for other users
	assert.NotContains(t, resp.Body, "email")
}

func (suite *UsersTestSuite) TestGetUserNotFound() {
	t := suite.T()

	// Create test user
	user, token := testutils.CreateTestUserWithAuth(t, "<EMAIL>", "Test User")
	defer testutils.CleanupTestUser(t, user.ID)

	// Test getting non-existent user
	req := testutils.WithAuth(testutils.TestRequest{
		Method: "GET",
		URL:    "/api/v1/users/00000000-0000-0000-0000-000000000000",
	}, user.ID, user.Email)

	resp := testutils.MakeRequest(t, suite.router, req)

	// Should return 404 Not Found
	testutils.AssertErrorResponse(t, resp, 404)
}

func (suite *UsersTestSuite) TestListUsers() {
	t := suite.T()

	// Create test users
	user1, token := testutils.CreateTestUserWithAuth(t, "<EMAIL>", "User 1")
	user2, _ := testutils.CreateTestUser("<EMAIL>", "User 2")
	user3, _ := testutils.CreateTestUser("<EMAIL>", "User 3")
	defer func() {
		testutils.CleanupTestUser(t, user1.ID)
		testutils.CleanupTestUser(t, user2.ID)
		testutils.CleanupTestUser(t, user3.ID)
	}()

	// Test listing users
	req := testutils.WithAuth(testutils.TestRequest{
		Method: "GET",
		URL:    "/api/v1/users",
		QueryParams: map[string]string{
			"limit": "10",
			"page":  "1",
		},
	}, user1.ID, user1.Email)

	resp := testutils.MakeRequest(t, suite.router, req)

	// Assertions
	testutils.AssertSuccessResponse(t, resp)
	assert.Equal(t, 200, resp.StatusCode)
	
	// Check pagination structure
	testutils.AssertPaginatedResponse(t, resp)
	testutils.AssertArrayResponse(t, resp, 3) // At least 3 users
}

func (suite *UsersTestSuite) TestDeleteCurrentUser() {
	t := suite.T()

	// Create test user
	user, token := testutils.CreateTestUserWithAuth(t, "<EMAIL>", "Test User")

	// Test user deletion
	req := testutils.WithAuth(testutils.TestRequest{
		Method: "DELETE",
		URL:    "/api/v1/users/me",
	}, user.ID, user.Email)

	resp := testutils.MakeRequest(t, suite.router, req)

	// Assertions
	testutils.AssertSuccessResponse(t, resp)
	assert.Equal(t, 204, resp.StatusCode)

	// Verify user is deleted
	var count int64
	testutils.TestDB.Model(&testutils.TestDB).Where("id = ?", user.ID).Count(&count)
	assert.Equal(t, int64(0), count)
}

// Run the test suite
func TestUsersTestSuite(t *testing.T) {
	suite.Run(t, new(UsersTestSuite))
}
