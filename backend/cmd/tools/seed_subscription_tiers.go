package main

import (
	"log"

	"github.com/adc-credit/backend/internal/database"
	"github.com/adc-credit/backend/internal/models"
	"github.com/joho/godotenv"
)

func main() {
	// Load environment variables
	if err := godotenv.Load(); err != nil {
		log.Println("No .env file found, using environment variables")
	}

	// Initialize database
	database.InitDB()

	// Seed subscription tiers
	if err := seedSubscriptionTiers(); err != nil {
		log.Fatalf("Failed to seed subscription tiers: %v", err)
	}

	log.Println("Successfully seeded subscription tiers")
}

func seedSubscriptionTiers() error {
	db := database.DB

	// Define subscription tiers with new limits
	tiers := []models.SubscriptionTier{
		{
			Name:                 "Starter",
			Description:          "Perfect for small businesses and testing",
			Price:                0.00,
			CreditLimit:          100000, // 100K credits
			Features:             models.StringSlice{"Basic API access", "Community support", "Basic analytics"},
			DefaultRateLimitMax:  10,
			DefaultRateLimitRate: 1.0,
			MaxWebhooks:          0,
			AdvancedAnalytics:    false,
			
			// New configurable limits
			MaxShops:             1,
			MaxCustomersPerShop:  50,
			MaxAPIKeysPerShop:    1,
			MaxBranchesPerShop:   0,
			MaxQRCodesPerMonth:   500,
			AnalyticsHistoryDays: 30,
			SupportLevel:         "community",
			AllowedShopTypes:     models.StringSlice{"retail"},
			UnlimitedShops:       false,
			UnlimitedCustomers:   false,
			UnlimitedQRCodes:     false,
			UnlimitedBranches:    false,
		},
		{
			Name:                 "Business",
			Description:          "Growing retail businesses and small chains",
			Price:                49.00,
			CreditLimit:          1000000, // 1M credits per shop
			Features:             models.StringSlice{"Full API access", "Email support", "Priority support", "Advanced analytics", "Multiple shops"},
			DefaultRateLimitMax:  60,
			DefaultRateLimitRate: 1.0,
			MaxWebhooks:          3,
			AdvancedAnalytics:    true,
			
			// New configurable limits
			MaxShops:             5,
			MaxCustomersPerShop:  1000,
			MaxAPIKeysPerShop:    5,
			MaxBranchesPerShop:   10,
			MaxQRCodesPerMonth:   2000,
			AnalyticsHistoryDays: 365,
			SupportLevel:         "email",
			AllowedShopTypes:     models.StringSlice{"retail", "api_service"},
			UnlimitedShops:       false,
			UnlimitedCustomers:   false,
			UnlimitedQRCodes:     false,
			UnlimitedBranches:    false,
		},
		{
			Name:                 "Enterprise",
			Description:          "Large businesses, franchises, and high-volume retailers",
			Price:                199.00,
			CreditLimit:          5000000, // 5M credits per shop
			Features:             models.StringSlice{"Full API access", "24/7 support", "Phone support", "Premium analytics", "Unlimited shops", "Custom integrations"},
			DefaultRateLimitMax:  600,
			DefaultRateLimitRate: 10.0,
			MaxWebhooks:          15,
			AdvancedAnalytics:    true,
			
			// New configurable limits
			MaxShops:             0, // Will be unlimited
			MaxCustomersPerShop:  0, // Will be unlimited
			MaxAPIKeysPerShop:    20,
			MaxBranchesPerShop:   0, // Will be unlimited
			MaxQRCodesPerMonth:   0, // Will be unlimited
			AnalyticsHistoryDays: 0, // Unlimited (represented as 0)
			SupportLevel:         "priority",
			AllowedShopTypes:     models.StringSlice{"retail", "api_service", "enterprise"},
			UnlimitedShops:       true,
			UnlimitedCustomers:   true,
			UnlimitedQRCodes:     true,
			UnlimitedBranches:    true,
		},
		{
			Name:                 "API Platform",
			Description:          "Payment processors, large enterprises, and multi-tenant platforms",
			Price:                499.00,
			CreditLimit:          25000000, // 25M credits total
			Features:             models.StringSlice{"Full API access", "24/7 support", "Dedicated engineer", "Real-time analytics", "White-label", "Custom rate limiting"},
			DefaultRateLimitMax:  0, // Custom
			DefaultRateLimitRate: 0, // Custom
			MaxWebhooks:          50,
			AdvancedAnalytics:    true,
			
			// New configurable limits
			MaxShops:             0, // Will be unlimited
			MaxCustomersPerShop:  0, // N/A for API platform
			MaxAPIKeysPerShop:    100, // Total API keys
			MaxBranchesPerShop:   0, // Will be unlimited
			MaxQRCodesPerMonth:   0, // Will be unlimited
			AnalyticsHistoryDays: 0, // Unlimited (real-time)
			SupportLevel:         "dedicated",
			AllowedShopTypes:     models.StringSlice{"api_service", "enterprise"},
			UnlimitedShops:       true,
			UnlimitedCustomers:   true,
			UnlimitedQRCodes:     true,
			UnlimitedBranches:    true,
		},
		{
			Name:                 "Ultra Enterprise",
			Description:          "Banks, large payment processors, and government institutions",
			Price:                2000.00, // Starting price, usually custom
			CreditLimit:          *********, // 100M+ credits
			Features:             models.StringSlice{"Full API access", "Dedicated team", "On-site support", "99.99% SLA", "Dedicated infrastructure", "Custom everything"},
			DefaultRateLimitMax:  0, // Custom/Dedicated
			DefaultRateLimitRate: 0, // Custom/Dedicated
			MaxWebhooks:          0, // Unlimited
			AdvancedAnalytics:    true,
			
			// New configurable limits
			MaxShops:             0, // Will be unlimited
			MaxCustomersPerShop:  0, // Will be unlimited
			MaxAPIKeysPerShop:    0, // Will be unlimited
			MaxBranchesPerShop:   0, // Will be unlimited
			MaxQRCodesPerMonth:   0, // Will be unlimited
			AnalyticsHistoryDays: 0, // Unlimited (real-time)
			SupportLevel:         "enterprise",
			AllowedShopTypes:     models.StringSlice{"retail", "api_service", "enterprise"},
			UnlimitedShops:       true,
			UnlimitedCustomers:   true,
			UnlimitedQRCodes:     true,
			UnlimitedBranches:    true,
		},
	}

	// Create or update each tier
	for _, tier := range tiers {
		var existingTier models.SubscriptionTier
		result := db.Where("name = ?", tier.Name).First(&existingTier)
		
		if result.Error != nil {
			// Create new tier
			if err := db.Create(&tier).Error; err != nil {
				return err
			}
			log.Printf("Created subscription tier: %s", tier.Name)
		} else {
			// Update existing tier
			tier.ID = existingTier.ID
			if err := db.Save(&tier).Error; err != nil {
				return err
			}
			log.Printf("Updated subscription tier: %s", tier.Name)
		}
	}

	return nil
}
