# GCP Deployment Guide

This guide explains how to deploy the ADC Credit application to Google Cloud Platform (GCP).

## Prerequisites

1. **Google Cloud Platform Account**
   - Create a GCP account if you don't have one: [https://cloud.google.com/](https://cloud.google.com/)
   - Create a new project in the GCP Console

2. **Install Google Cloud SDK**
   - Download and install the Google Cloud SDK: [https://cloud.google.com/sdk/docs/install](https://cloud.google.com/sdk/docs/install)
   - Initialize the SDK and authenticate:
     ```bash
     gcloud init
     gcloud auth login
     ```

3. **Enable Required APIs**
   - Cloud Run API
   - Cloud Functions API
   - Cloud Scheduler API
   - Cloud Build API
   - Container Registry API
   - Artifact Registry API

   You can enable these APIs using the following command:
   ```bash
   gcloud services enable run.googleapis.com \
     cloudfunctions.googleapis.com \
     cloudscheduler.googleapis.com \
     cloudbuild.googleapis.com \
     containerregistry.googleapis.com \
     artifactregistry.googleapis.com
   ```

4. **Set up a PostgreSQL Database**
   - You can use Cloud SQL for PostgreSQL or any other PostgreSQL provider
   - Make sure the database is accessible from GCP services

## Deployment Steps

### 1. Set Environment Variables

Create a `.env` file in the project root with the following variables:

```bash
# GCP Configuration
PROJECT_ID=your-gcp-project-id
REGION=us-central1

# Database Configuration
DATABASE_URL=postgresql://username:password@host:port/database

# Authentication
JWT_SECRET=your-jwt-secret
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret

# Application URLs
FRONTEND_URL=https://your-frontend-url.com
BACKEND_URL=https://your-backend-url.run.app

# Scheduled Credits
SCHEDULER_API_KEY=your-secure-api-key
```

### 2. Deploy the Backend to Cloud Run

You can deploy the backend to Cloud Run using the provided Makefile target:

```bash
make deploy-backend
```

This will:
- Build a Docker container for the backend
- Push the container to Google Container Registry
- Deploy the container to Cloud Run
- Configure environment variables

Alternatively, you can manually submit the Cloud Build configuration:

```bash
gcloud builds submit --config=backend/cloudbuild.yaml \
  --substitutions=_DATABASE_URL="$DATABASE_URL",_JWT_SECRET="$JWT_SECRET",_GOOGLE_CLIENT_ID="$GOOGLE_CLIENT_ID",_GOOGLE_CLIENT_SECRET="$GOOGLE_CLIENT_SECRET",_FRONTEND_URL="$FRONTEND_URL",_SCHEDULER_API_KEY="$SCHEDULER_API_KEY"
```

### 3. Deploy the Scheduled Credits Function

You can deploy the scheduled credits function using the provided Makefile target:

```bash
make deploy-scheduled-credits
```

This will:
- Deploy the Cloud Function for processing scheduled credits
- Create a Cloud Scheduler job to trigger the function daily at midnight

Alternatively, you can run the deployment script directly:

```bash
cd backend
SCHEDULER_API_KEY=$SCHEDULER_API_KEY DATABASE_URL=$DATABASE_URL ./scripts/deploy_scheduled_credits.sh
```

### 4. Deploy Backend

#### Using the Unified Deployment Method (Recommended)

The unified deployment method uses a single Cloud Build configuration file to deploy the backend:

```bash
# Set all required environment variables
export PROJECT_ID=your-gcp-project-id
export REGION=us-central1
export DATABASE_URL=postgresql://username:password@host:port/database
export JWT_SECRET=your-jwt-secret
export GOOGLE_CLIENT_ID=your-google-client-id
export GOOGLE_CLIENT_SECRET=your-google-client-secret
export FRONTEND_URL=https://your-frontend-url.com
export SCHEDULER_API_KEY=your-secure-api-key

# Deploy backend
make deploy
```

This will:
- Build and deploy the backend to Cloud Run
- Configure all environment variables for the service

#### Using the Legacy Deployment Method

To deploy using the separate deployment commands:

```bash
make deploy-legacy
```

This method deploys the backend and scheduled credits function separately.

### 5. Deploy Frontend

The frontend deployment is handled separately due to the complexity of Next.js deployments. You can get instructions for deploying the frontend by running:

```bash
make deploy-frontend
```

This will provide you with step-by-step instructions for deploying the frontend to Cloud Run, including:
1. Building the frontend locally
2. Deploying to Cloud Run using the Google Cloud Console
3. Setting the required environment variables

## Verifying the Deployment

### Frontend Application

1. Check the Cloud Run service status:
   ```bash
   gcloud run services describe adc-credit-frontend --region=$REGION
   ```

2. Open the frontend URL in a browser:
   ```bash
   FRONTEND_URL=$(gcloud run services describe adc-credit-frontend --region=$REGION --format="value(status.url)")
   echo "Frontend URL: $FRONTEND_URL"
   ```

### Backend API

1. Check the Cloud Run service status:
   ```bash
   gcloud run services describe adc-credit-backend --region=$REGION
   ```

2. Test the API endpoint:
   ```bash
   BACKEND_URL=$(gcloud run services describe adc-credit-backend --region=$REGION --format="value(status.url)")
   curl $BACKEND_URL/api/v1/health
   ```

### Scheduled Credits Function

1. Check the Cloud Function status:
   ```bash
   gcloud functions describe process-scheduled-credits --gen2 --region=$REGION
   ```

2. Check the Cloud Scheduler job:
   ```bash
   gcloud scheduler jobs describe daily-credit-refresh --location=$REGION
   ```

3. Manually trigger the function:
   ```bash
   FUNCTION_URL=$(gcloud functions describe process-scheduled-credits --gen2 --region=$REGION --format="value(serviceConfig.uri)")
   curl -X POST -H "X-API-Key: $SCHEDULER_API_KEY" $FUNCTION_URL
   ```

## Troubleshooting

### Frontend Issues

- Check the logs in the GCP Console under Cloud Run > adc-credit-frontend > Logs
- Verify that all environment variables are set correctly, especially:
  - NEXT_PUBLIC_BACKEND_URL
  - NEXTAUTH_URL
  - NEXTAUTH_SECRET
  - GOOGLE_CLIENT_ID
  - GOOGLE_CLIENT_SECRET
- Ensure the backend API is accessible from the frontend

### Backend Issues

- Check the logs in the GCP Console under Cloud Run > adc-credit-backend > Logs
- Verify that all environment variables are set correctly
- Ensure the database is accessible from Cloud Run

### Cloud Function Issues

- Check the logs in the GCP Console under Cloud Functions > process-scheduled-credits > Logs
- Verify that the SCHEDULER_API_KEY is set correctly
- Ensure the database is accessible from Cloud Functions

### Cloud Scheduler Issues

- Check the job execution history in the GCP Console
- Verify that the function URL is correct
- Ensure the API key is included in the headers

## Monitoring and Maintenance

- Set up Cloud Monitoring alerts for the Cloud Run service and Cloud Function
- Configure logging to capture important events
- Regularly check the Cloud Scheduler job execution history
- Monitor database performance and usage

## Security Considerations

- Use a strong, unique SCHEDULER_API_KEY
- Restrict access to the Cloud Function using IAM permissions
- Use VPC Service Controls to restrict network access if needed
- Regularly rotate API keys and secrets
- Enable audit logging for all GCP services
