package integration

import (
	"bytes"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/adc-credit/backend/internal/database"
	"github.com/adc-credit/backend/internal/models"
	"github.com/adc-credit/backend/internal/routes"
	testutils "github.com/adc-credit/backend/internal/testing"
	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/stretchr/testify/suite"
)

type SubscriptionLimitsAPITestSuite struct {
	suite.Suite
	router *gin.Engine
}

func (suite *SubscriptionLimitsAPITestSuite) SetupSuite() {
	err := testutils.SetupTestDB()
	suite.Require().NoError(err, "Failed to setup test database")

	err = database.InitDB()
	suite.Require().NoError(err, "Failed to initialize database")

	gin.SetMode(gin.TestMode)
	suite.router = gin.New()
	routes.RegisterRoutes(suite.router)
}

func (suite *SubscriptionLimitsAPITestSuite) TearDownSuite() {
	testutils.CleanupTestDB()
}

func (suite *SubscriptionLimitsAPITestSuite) SetupTest() {
	testutils.CleanupAllTestData()
}

// Helper function to create subscription tiers
func (suite *SubscriptionLimitsAPITestSuite) createTestSubscriptionTiers() (starter, business, enterprise *models.SubscriptionTier) {
	db := database.DB

	starter = &models.SubscriptionTier{
		Name:                 "Starter",
		Description:          "Starter plan for testing",
		Price:                0.00,
		CreditLimit:          100000,
		Features:             models.StringSlice{"Basic API access"},
		MaxShops:             1,
		MaxCustomersPerShop:  50,
		MaxAPIKeysPerShop:    1,
		MaxBranchesPerShop:   0,
		MaxQRCodesPerMonth:   500,
		MaxWebhooks:          0,
		AnalyticsHistoryDays: 30,
		SupportLevel:         "community",
		AllowedShopTypes:     models.StringSlice{"retail"},
		UnlimitedShops:       false,
		UnlimitedCustomers:   false,
		UnlimitedQRCodes:     false,
		UnlimitedBranches:    false,
	}

	business = &models.SubscriptionTier{
		Name:                 "Business",
		Description:          "Business plan for testing",
		Price:                49.00,
		CreditLimit:          1000000,
		Features:             models.StringSlice{"Full API access", "Multiple shops"},
		MaxShops:             5,
		MaxCustomersPerShop:  1000,
		MaxAPIKeysPerShop:    5,
		MaxBranchesPerShop:   10,
		MaxQRCodesPerMonth:   2000,
		MaxWebhooks:          3,
		AnalyticsHistoryDays: 365,
		SupportLevel:         "email",
		AllowedShopTypes:     models.StringSlice{"retail", "api_service"},
		UnlimitedShops:       false,
		UnlimitedCustomers:   false,
		UnlimitedQRCodes:     false,
		UnlimitedBranches:    false,
	}

	enterprise = &models.SubscriptionTier{
		Name:                 "Enterprise",
		Description:          "Enterprise plan for testing",
		Price:                199.00,
		CreditLimit:          5000000,
		Features:             models.StringSlice{"Full API access", "Unlimited everything"},
		MaxShops:             0, // Unlimited
		MaxCustomersPerShop:  0, // Unlimited
		MaxAPIKeysPerShop:    20,
		MaxBranchesPerShop:   0, // Unlimited
		MaxQRCodesPerMonth:   0, // Unlimited
		MaxWebhooks:          15,
		AnalyticsHistoryDays: 0, // Unlimited
		SupportLevel:         "priority",
		AllowedShopTypes:     models.StringSlice{"retail", "api_service", "enterprise"},
		UnlimitedShops:       true,
		UnlimitedCustomers:   true,
		UnlimitedQRCodes:     true,
		UnlimitedBranches:    true,
	}

	suite.Require().NoError(db.Create(starter).Error)
	suite.Require().NoError(db.Create(business).Error)
	suite.Require().NoError(db.Create(enterprise).Error)

	return starter, business, enterprise
}

// Helper function to create user with subscription
func (suite *SubscriptionLimitsAPITestSuite) createUserWithSubscription(email, name string, tier *models.SubscriptionTier) (*models.User, *models.Subscription, string) {
	user, token := testutils.CreateTestUserWithAuth(suite.T(), email, name)

	subscription := &models.Subscription{
		ID:                 uuid.New(),
		UserID:             user.ID,
		SubscriptionTierID: tier.ID,
		StartDate:          time.Now(),
		AutoRenew:          true,
		Status:             "active",
		CreditBalance:      tier.CreditLimit,
		SubscriptionType:   "personal",
	}

	suite.Require().NoError(database.DB.Create(subscription).Error)

	return user, subscription, token
}

// Test subscription limits API endpoints
func (suite *SubscriptionLimitsAPITestSuite) TestGetUserLimits() {
	starter, _, _ := suite.createTestSubscriptionTiers()
	user, _, token := suite.createUserWithSubscription("<EMAIL>", "Test User", starter)

	req := httptest.NewRequest("GET", "/api/v1/subscription-limits", nil)
	req.Header.Set("Authorization", "Bearer "+token)

	w := httptest.NewRecorder()
	suite.router.ServeHTTP(w, req)

	suite.Equal(http.StatusOK, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	suite.Require().NoError(err)

	// Check subscription tier info
	suite.Contains(response, "subscription_tier")
	suite.Contains(response, "limits")

	limits := response["limits"].(map[string]interface{})

	// Check shop limits
	shops := limits["shops"].(map[string]interface{})
	suite.Equal(float64(1), shops["max"])
	suite.Equal(false, shops["unlimited"])

	// Check QR code limits
	qrCodes := limits["qr_codes_per_month"].(map[string]interface{})
	suite.Equal(float64(500), qrCodes["max"])
	suite.Equal(false, qrCodes["unlimited"])

	// Cleanup
	testutils.CleanupTestUser(suite.T(), user.ID)
}

// Test individual limit check endpoints
func (suite *SubscriptionLimitsAPITestSuite) TestCheckShopLimitAPI() {
	starter, _, _ := suite.createTestSubscriptionTiers()
	user, _, token := suite.createUserWithSubscription("<EMAIL>", "Test User", starter)

	// Check shop limit when no shops exist
	req := httptest.NewRequest("GET", "/api/v1/subscription-limits/shops/check", nil)
	req.Header.Set("Authorization", "Bearer "+token)

	w := httptest.NewRecorder()
	suite.router.ServeHTTP(w, req)

	suite.Equal(http.StatusOK, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	suite.Require().NoError(err)

	suite.Equal(true, response["allowed"])
	suite.Equal(float64(0), response["current_usage"])
	suite.Equal(float64(1), response["limit"])

	// Create a shop
	_, err = testutils.CreateTestShop("Test Shop", "test-shop", user.ID, "retail")
	suite.Require().NoError(err)

	// Check shop limit again
	req2 := httptest.NewRequest("GET", "/api/v1/subscription-limits/shops/check", nil)
	req2.Header.Set("Authorization", "Bearer "+token)

	w2 := httptest.NewRecorder()
	suite.router.ServeHTTP(w2, req2)

	suite.Equal(http.StatusOK, w2.Code)

	var response2 map[string]interface{}
	err = json.Unmarshal(w2.Body.Bytes(), &response2)
	suite.Require().NoError(err)

	suite.Equal(false, response2["allowed"]) // Should be false since limit is reached
	suite.Equal(float64(1), response2["current_usage"])
	suite.Equal(float64(1), response2["limit"])

	// Cleanup
	testutils.CleanupTestUser(suite.T(), user.ID)
}

// Test QR code limit check
func (suite *SubscriptionLimitsAPITestSuite) TestCheckQRCodeLimitAPI() {
	starter, _, _ := suite.createTestSubscriptionTiers()
	user, _, token := suite.createUserWithSubscription("<EMAIL>", "Test User", starter)

	req := httptest.NewRequest("GET", "/api/v1/subscription-limits/qr-codes/check", nil)
	req.Header.Set("Authorization", "Bearer "+token)

	w := httptest.NewRecorder()
	suite.router.ServeHTTP(w, req)

	suite.Equal(http.StatusOK, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	suite.Require().NoError(err)

	suite.Equal(true, response["allowed"])
	suite.Equal(float64(0), response["current_usage"])
	suite.Equal(float64(500), response["limit"])

	// Cleanup
	testutils.CleanupTestUser(suite.T(), user.ID)
}

// Test shop type check
func (suite *SubscriptionLimitsAPITestSuite) TestCheckShopTypeAPI() {
	starter, _, _ := suite.createTestSubscriptionTiers()
	user, _, token := suite.createUserWithSubscription("<EMAIL>", "Test User", starter)

	// Test allowed shop type (retail)
	req := httptest.NewRequest("GET", "/api/v1/subscription-limits/shop-types/check?shop_type=retail", nil)
	req.Header.Set("Authorization", "Bearer "+token)

	w := httptest.NewRecorder()
	suite.router.ServeHTTP(w, req)

	suite.Equal(http.StatusOK, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	suite.Require().NoError(err)

	suite.Equal(true, response["allowed"])

	// Test disallowed shop type (api_service)
	req2 := httptest.NewRequest("GET", "/api/v1/subscription-limits/shop-types/check?shop_type=api_service", nil)
	req2.Header.Set("Authorization", "Bearer "+token)

	w2 := httptest.NewRecorder()
	suite.router.ServeHTTP(w2, req2)

	suite.Equal(http.StatusOK, w2.Code)

	var response2 map[string]interface{}
	err = json.Unmarshal(w2.Body.Bytes(), &response2)
	suite.Require().NoError(err)

	suite.Equal(false, response2["allowed"])

	// Cleanup
	testutils.CleanupTestUser(suite.T(), user.ID)
}

// Test branch limits
func (suite *SubscriptionLimitsAPITestSuite) TestBranchLimits() {
	starter, business, _ := suite.createTestSubscriptionTiers()

	// Test Starter plan (0 branches allowed)
	user1, _, token1 := suite.createUserWithSubscription("<EMAIL>", "Starter User", starter)
	shop1, err := testutils.CreateTestShop("Test Shop 1", "test-shop-1", user1.ID, "retail")
	suite.Require().NoError(err)

	branchData := map[string]interface{}{
		"name":        "Test Branch",
		"description": "Test branch description",
		"branch_type": "location",
		"shop_id":     shop1.ID.String(),
	}

	body, _ := json.Marshal(branchData)
	req := httptest.NewRequest("POST", "/api/v1/shops/branches", bytes.NewBuffer(body))
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", "Bearer "+token1)

	w := httptest.NewRecorder()
	suite.router.ServeHTTP(w, req)

	suite.Equal(http.StatusForbidden, w.Code) // Should fail for starter plan

	// Test Business plan (10 branches allowed)
	user2, _, token2 := suite.createUserWithSubscription("<EMAIL>", "Business User", business)
	shop2, err := testutils.CreateTestShop("Test Shop 2", "test-shop-2", user2.ID, "retail")
	suite.Require().NoError(err)

	branchData2 := map[string]interface{}{
		"name":        "Business Branch",
		"description": "Business branch description",
		"branch_type": "location",
		"shop_id":     shop2.ID.String(),
	}

	body2, _ := json.Marshal(branchData2)
	req2 := httptest.NewRequest("POST", "/api/v1/shops/branches", bytes.NewBuffer(body2))
	req2.Header.Set("Content-Type", "application/json")
	req2.Header.Set("Authorization", "Bearer "+token2)

	w2 := httptest.NewRecorder()
	suite.router.ServeHTTP(w2, req2)

	suite.Equal(http.StatusCreated, w2.Code) // Should succeed for business plan

	// Cleanup
	testutils.CleanupTestUser(suite.T(), user1.ID)
	testutils.CleanupTestUser(suite.T(), user2.ID)
}

func TestSubscriptionLimitsAPITestSuite(t *testing.T) {
	suite.Run(t, new(SubscriptionLimitsAPITestSuite))
}
