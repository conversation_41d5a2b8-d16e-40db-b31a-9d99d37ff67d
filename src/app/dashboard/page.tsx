"use client";

import { useState, useEffect } from "react";
import { useSession } from "next-auth/react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import Link from "next/link";
import { APIKey, APIKeySummary, EndpointSummary } from "@/types";
import { PageHeader } from "@/components/layout/page-header";
import {
  RefreshCw,
  AlertCircle,
  CheckCircle2,
  Clock,
  CreditCard,
  Key,
  Zap,
  Settings,
  Store
} from "lucide-react";
import {
  useGetCreditBalanceQuery,
  useGetAPIKeysQuery,
  useGetUsageSummaryQuery,
  useGetTransactionsQuery
} from "@/lib/api/apiSlice";
import { RealUsageCard } from "@/components/dashboard/RealUsageCard";
import { RealUsageDetails } from "@/components/dashboard/RealUsageDetails";
import { ScheduledCredits } from "@/components/dashboard/scheduled-credits";

export default function DashboardPage() {
  const { data: session } = useSession();
  // Loading state
  const [isLoading, setLoading] = useState(false);

  // Store access token in session storage if available
  useEffect(() => {
    // Use type assertion to access accessToken
    const userAccessToken = (session?.user as any)?.accessToken;
    const sessionAccessToken = (session as any)?.accessToken;

    if (userAccessToken) {
      console.log('Setting access token from user:', userAccessToken.substring(0, 10) + '...');
      sessionStorage.setItem('accessToken', userAccessToken);
    } else if (sessionAccessToken) {
      console.log('Setting access token from session:', sessionAccessToken.substring(0, 10) + '...');
      sessionStorage.setItem('accessToken', sessionAccessToken);
    } else {
      console.warn('No access token found in session');
    }
  }, [session]);

  // Use RTK Query hooks to fetch data
  const {
    data: creditInfo,
    isLoading: isLoadingCredit,
    refetch: refetchCredit
  } = useGetCreditBalanceQuery(undefined);

  const {
    data: apiKeys = [],
    isLoading: isLoadingApiKeys,
    refetch: refetchApiKeys
  } = useGetAPIKeysQuery(undefined);

  const {
    data: usageSummary,
    isLoading: isLoadingUsage,
    refetch: refetchUsage
  } = useGetUsageSummaryQuery({ period: "month" });

  const {
    data: transactions = [],
    isLoading: isLoadingTransactions,
    refetch: refetchTransactions
  } = useGetTransactionsQuery(undefined);

  // Determine if any data is still loading
  const loading = isLoadingCredit || isLoadingApiKeys || isLoadingUsage || isLoadingTransactions;

  // Function to refresh all data
  const refreshData = () => {
    setLoading(true);
    refetchCredit();
    refetchApiKeys();
    refetchUsage();
    refetchTransactions();
    setTimeout(() => setLoading(false), 500);
  };

  // Get the appropriate data based on authentication method
  const displayCreditInfo = creditInfo;
  const displayApiKeys = apiKeys || [];
  const displayUsageSummary = usageSummary;
  const displayTransactions = transactions || [];

  if (loading || isLoading) {
    return (
      <div className="flex h-[calc(100vh-4rem)] items-center justify-center">
        <div className="h-8 w-8 animate-spin rounded-full border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div>
      <PageHeader
        heading="Dashboard"
        description="Overview of your API usage and credits"
        className="pb-4 sm:pb-8"
      >
        <Button
          variant="outline"
          size="sm"
          className="w-full sm:w-auto gap-1"
          onClick={refreshData}
          disabled={loading || isLoading}
        >
          <RefreshCw className={`h-3.5 w-3.5 ${(loading || isLoading) ? 'animate-spin' : ''}`} />
          {(loading || isLoading) ? 'Loading...' : 'Refresh'}
        </Button>
      </PageHeader>

      <Tabs defaultValue="overview" className="w-full">
        <TabsList className="grid w-full grid-cols-3 mb-4 sm:mb-8">
          <TabsTrigger value="overview" className="text-xs sm:text-sm py-1.5 sm:py-2">Overview</TabsTrigger>
          <TabsTrigger value="usage" className="text-xs sm:text-sm py-1.5 sm:py-2">Usage Analytics</TabsTrigger>
          <TabsTrigger value="activity" className="text-xs sm:text-sm py-1.5 sm:py-2">Recent Activity</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-8">
          {/* Overview Cards */}
          <div className="grid gap-4 sm:gap-6 grid-cols-1 sm:grid-cols-2 lg:grid-cols-4">
            {/* Credit Balance Card */}
            <Card className="sm:col-span-1 lg:col-span-1">
              <CardHeader className="pb-2 space-y-1">
                <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-1">
                  <CardTitle className="text-base font-medium flex items-center gap-2">
                    <CreditCard className="h-4 w-4 text-primary" />
                    Credit Balance
                  </CardTitle>
                  <Badge variant="outline" className="text-xs font-normal w-fit">
                    {displayCreditInfo?.subscription.subscription_tier.name}
                  </Badge>
                </div>
                <CardDescription>Your current API credit balance</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-2xl sm:text-3xl font-bold flex flex-wrap items-baseline gap-1">
                  {displayCreditInfo?.credit_balance.toLocaleString()}
                  <span className="text-xs sm:text-sm text-muted-foreground">
                    / {displayCreditInfo?.credit_limit.toLocaleString()}
                  </span>
                </div>
                <div className="mt-4">
                  <div className="flex items-center justify-between mb-1 text-xs">
                    <span>0</span>
                    <span>{displayCreditInfo?.credit_limit.toLocaleString()}</span>
                  </div>
                  <Progress
                    value={displayCreditInfo ? (displayCreditInfo.credit_balance / displayCreditInfo.credit_limit) * 100 : 0}
                    className="h-2"
                  />
                </div>
              </CardContent>
              <CardFooter className="pt-0">
                <Link href="/dashboard/subscriptions" className="w-full">
                  <Button variant="outline" size="sm" className="w-full">
                    Manage Subscription
                  </Button>
                </Link>
              </CardFooter>
            </Card>

            {/* API Keys Card */}
            <Card className="sm:col-span-1 lg:col-span-1">
              <CardHeader className="pb-2 space-y-1">
                <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-1">
                  <CardTitle className="text-base font-medium flex items-center gap-2">
                    <Key className="h-4 w-4 text-primary" />
                    API Keys
                  </CardTitle>
                  <Badge variant="outline" className="text-xs font-normal w-fit">
                    {displayApiKeys.filter((key: APIKey) => key.enabled).length} active
                  </Badge>
                </div>
                <CardDescription>Your API keys</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-2xl sm:text-3xl font-bold">{displayApiKeys.length}</div>
                <div className="mt-4 space-y-2">
                  {displayApiKeys.slice(0, 2).map((key: APIKey) => (
                    <div key={key.id} className="flex items-center justify-between text-sm">
                      <span className="truncate max-w-[120px] sm:max-w-[180px]">{key.name}</span>
                      <Badge variant={key.enabled ? "default" : "outline"} className="text-xs">
                        {key.enabled ? "Active" : "Inactive"}
                      </Badge>
                    </div>
                  ))}
                  {displayApiKeys.length > 2 && (
                    <div className="text-xs text-muted-foreground text-center">
                      +{displayApiKeys.length - 2} more keys
                    </div>
                  )}
                </div>
              </CardContent>
              <CardFooter className="pt-0">
                <Link href="/dashboard/api-keys" className="w-full">
                  <Button variant="outline" size="sm" className="w-full">
                    Manage API Keys
                  </Button>
                </Link>
              </CardFooter>
            </Card>

            {/* Real-Time Usage Card */}
            <div className="sm:col-span-2 lg:col-span-1">
              <RealUsageCard />
            </div>

            {/* Scheduled Credits Card */}
            <div className="sm:col-span-2 lg:col-span-1">
              <ScheduledCredits />
            </div>
          </div>

          {/* Quick Actions */}
          <Card>
            <CardHeader>
              <CardTitle className="text-base sm:text-lg font-medium">Quick Actions</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 sm:grid-cols-2 md:grid-cols-4 gap-2 sm:gap-4">
                <Link href="/dashboard/api-keys/new" className="w-full">
                  <Button variant="outline" className="w-full h-16 sm:h-24 flex flex-col gap-1 sm:gap-2 px-2 sm:px-4">
                    <Key className="h-4 w-4 sm:h-5 sm:w-5" />
                    <span className="text-xs sm:text-sm text-center">Create API Key</span>
                  </Button>
                </Link>
                <Link href="/dashboard/shops" className="w-full">
                  <Button variant="outline" className="w-full h-16 sm:h-24 flex flex-col gap-1 sm:gap-2 px-2 sm:px-4">
                    <Store className="h-4 w-4 sm:h-5 sm:w-5" />
                    <span className="text-xs sm:text-sm text-center">Manage Shops</span>
                  </Button>
                </Link>
                <Link href="/dashboard/subscriptions" className="w-full">
                  <Button variant="outline" className="w-full h-16 sm:h-24 flex flex-col gap-1 sm:gap-2 px-2 sm:px-4">
                    <Zap className="h-4 w-4 sm:h-5 sm:w-5" />
                    <span className="text-xs sm:text-sm text-center">Upgrade Plan</span>
                  </Button>
                </Link>
                <Link href="/dashboard/settings" className="w-full">
                  <Button variant="outline" className="w-full h-16 sm:h-24 flex flex-col gap-1 sm:gap-2 px-2 sm:px-4">
                    <Settings className="h-4 w-4 sm:h-5 sm:w-5" />
                    <span className="text-xs sm:text-sm text-center">Settings</span>
                  </Button>
                </Link>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="usage" className="space-y-8">
          <RealUsageDetails />
        </TabsContent>

        <TabsContent value="activity" className="space-y-8">
          {/* Recent Activity */}
          <Card>
            <CardHeader className="space-y-1">
              <CardTitle className="text-base sm:text-lg font-medium">Recent Activity</CardTitle>
              <CardDescription>Latest events and notifications</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-3 sm:space-y-4">
                {displayTransactions.length > 0 ? (
                  displayTransactions.slice(0, 5).map((transaction) => {
                    // Determine icon and title based on transaction type
                    let icon;
                    let title;

                    if (transaction.type === "credit_add") {
                      icon = <CheckCircle2 className="h-3.5 w-3.5 sm:h-4 sm:w-4 text-green-500" />;
                      title = "Credits Added";
                    } else if (transaction.type === "credit_use") {
                      icon = <Clock className="h-3.5 w-3.5 sm:h-4 sm:w-4 text-blue-500" />;
                      title = "Credits Used";
                    } else if (transaction.type === "credit_scheduled") {
                      icon = <RefreshCw className="h-3.5 w-3.5 sm:h-4 sm:w-4 text-green-500" />;
                      title = "Scheduled Credits";
                    } else {
                      icon = <AlertCircle className="h-3.5 w-3.5 sm:h-4 sm:w-4 text-amber-500" />;
                      title = "Transaction";
                    }

                    // Format time
                    const transactionTime = new Date(transaction.created_at);
                    const now = new Date();
                    const diffInHours = Math.floor((now.getTime() - transactionTime.getTime()) / (1000 * 60 * 60));

                    let timeString;
                    if (diffInHours < 1) {
                      timeString = "Just now";
                    } else if (diffInHours < 24) {
                      timeString = `${diffInHours} hour${diffInHours > 1 ? 's' : ''} ago`;
                    } else if (diffInHours < 48) {
                      timeString = "Yesterday";
                    } else {
                      timeString = transactionTime.toLocaleDateString();
                    }

                    return (
                      <ActivityItem
                        key={transaction.id}
                        icon={icon}
                        title={title}
                        description={transaction.description || `${Math.abs(transaction.amount)} credits ${transaction.amount > 0 ? 'added' : 'used'}`}
                        time={timeString}
                      />
                    );
                  })
                ) : (
                  <div className="py-4 text-center text-muted-foreground text-sm">
                    No recent activity
                  </div>
                )}
              </div>
            </CardContent>
            <CardFooter>
              <Button variant="outline" size="sm" className="w-full">
                View All Activity
              </Button>
            </CardFooter>
          </Card>

          {/* API Usage by Endpoint (simplified version) */}
          <Card>
            <CardHeader className="space-y-1">
              <CardTitle className="text-base sm:text-lg font-medium">API Usage by Endpoint</CardTitle>
            </CardHeader>
            <CardContent>
              {displayUsageSummary && displayUsageSummary.api_keys.length > 0 ? (
                <div className="space-y-6 sm:space-y-8">
                  {displayUsageSummary.api_keys.map((apiKey: APIKeySummary) => (
                    <div key={apiKey.id} className="space-y-3 sm:space-y-4">
                      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-1">
                        <h3 className="font-medium text-sm sm:text-base truncate">{apiKey.name}</h3>
                        <span className="text-xs sm:text-sm text-muted-foreground whitespace-nowrap">
                          {apiKey.total_credits.toLocaleString()} credits
                        </span>
                      </div>
                      <div className="space-y-2">
                        {apiKey.endpoints.map((endpoint: EndpointSummary, i: number) => (
                          <div key={i} className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-1">
                            <span className="text-xs sm:text-sm font-mono truncate max-w-full sm:max-w-[300px]">
                              {endpoint.endpoint}
                            </span>
                            <span className="text-xs sm:text-sm text-muted-foreground whitespace-nowrap">
                              {endpoint.total_credits.toLocaleString()} credits
                            </span>
                          </div>
                        ))}
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="py-6 sm:py-8 text-center text-muted-foreground text-sm">
                  No usage data available
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}

// Activity Item Component
function ActivityItem({
  icon,
  title,
  description,
  time
}: {
  icon: React.ReactNode;
  title: string;
  description: string;
  time: string;
}) {
  return (
    <div className="flex items-start space-x-2 sm:space-x-3">
      <div className="mt-0.5 flex-shrink-0">{icon}</div>
      <div className="flex-1 min-w-0">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-1">
          <div className="font-medium truncate">{title}</div>
          <div className="text-xs text-muted-foreground whitespace-nowrap">{time}</div>
        </div>
        <div className="text-xs sm:text-sm text-muted-foreground truncate">{description}</div>
      </div>
    </div>
  );
}
