package database

import (
	"fmt"
	"log"
	"os"
	"time"

	"github.com/adc-credit/backend/internal/models"
	"gorm.io/driver/postgres"
	"gorm.io/gorm"
)

var DB *gorm.DB

// InitDB initializes the database connection
func InitDB() error {
	// Check if DATABASE_URL is provided (for Supabase or other PostgreSQL providers)
	var dsn string
	if dbURL := os.Getenv("DATABASE_URL"); dbURL != "" {
		dsn = dbURL
	} else {
		// Fallback to individual connection parameters
		dsn = fmt.Sprintf(
			"host=%s user=%s password=%s dbname=%s port=%s sslmode=disable TimeZone=UTC",
			os.Getenv("DB_HOST"),
			os.<PERSON>env("DB_USER"),
			os.Getenv("DB_PASSWORD"),
			os.Getenv("DB_NAME"),
			os.<PERSON>env("DB_PORT"),
		)
	}

	var err error
	DB, err = gorm.Open(postgres.Open(dsn), &gorm.Config{
		PrepareStmt: true, // Caches prepared statements for better performance
	})
	if err != nil {
		return err
	}

	// Configure connection pooling
	sqlDB, err := DB.DB()
	if err != nil {
		return err
	}

	// Set connection pool settings
	sqlDB.SetMaxIdleConns(10)               // Maximum number of idle connections
	sqlDB.SetMaxOpenConns(100)              // Maximum number of open connections
	sqlDB.SetConnMaxLifetime(1 * time.Hour) // Maximum lifetime of a connection

	// Run migrations
	if err := RunMigrations(); err != nil {
		return err
	}

	// Seed subscription tiers if they don't exist
	if err := seedSubscriptionTiers(); err != nil {
		log.Printf("Error seeding subscription tiers: %v", err)
	}

	return nil
}

// seedSubscriptionTiers creates default subscription tiers if they don't exist
func seedSubscriptionTiers() error {
	var count int64
	DB.Model(&models.SubscriptionTier{}).Count(&count)
	if count > 0 {
		return nil
	}

	tiers := []models.SubscriptionTier{
		{
			Name:                 "Free",
			Description:          "Basic tier with limited credits",
			Price:                0,
			CreditLimit:          1000,
			DefaultRateLimitMax:  10,
			DefaultRateLimitRate: 1.0 / 6.0, // 10 requests per minute
			MaxWebhooks:          1,
			AdvancedAnalytics:    false,
			Features:             []string{"1,000 credits per month", "Basic API access", "Standard support", "1 webhook", "Basic rate limiting"},
		},
		{
			Name:                 "Pro",
			Description:          "Professional tier with more credits",
			Price:                29.99,
			CreditLimit:          10000,
			DefaultRateLimitMax:  60,
			DefaultRateLimitRate: 1.0, // 60 requests per minute
			MaxWebhooks:          5,
			AdvancedAnalytics:    true,
			Features:             []string{"10,000 credits per month", "Full API access", "Priority support", "Detailed analytics", "5 webhooks", "Enhanced rate limiting"},
		},
		{
			Name:                 "Enterprise",
			Description:          "Enterprise tier with unlimited credits",
			Price:                99.99,
			CreditLimit:          50000,
			DefaultRateLimitMax:  600,
			DefaultRateLimitRate: 10.0, // 600 requests per minute
			MaxWebhooks:          20,
			AdvancedAnalytics:    true,
			Features:             []string{"50,000 credits per month", "Full API access", "24/7 support", "Advanced analytics", "Custom integrations", "20 webhooks", "Custom rate limiting"},
		},
	}

	return DB.Create(&tiers).Error
}
