import { render, screen, waitFor, fireEvent } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { jest } from '@jest/globals';
import SubscriptionLimitsCard from '@/components/subscription/SubscriptionLimitsCard';
import { useSubscriptionLimits } from '@/hooks/useSubscriptionLimits';

// Mock the hooks
jest.mock('@/hooks/useSubscriptionLimits');

const mockUseSubscriptionLimits = useSubscriptionLimits as jest.MockedFunction<typeof useSubscriptionLimits>;

// Mock data
const mockStarterLimits = {
  subscription_tier: {
    id: 1,
    name: 'Starter',
    description: 'Perfect for small businesses and testing',
    price: 0,
    credit_limit: 100000,
    features: ['Basic API access', 'Community support'],
    max_shops: 1,
    max_customers_per_shop: 50,
    max_api_keys_per_shop: 1,
    max_branches_per_shop: 0,
    max_qr_codes_per_month: 500,
    analytics_history_days: 30,
    support_level: 'community',
    allowed_shop_types: ['retail'],
    unlimited_shops: false,
    unlimited_customers: false,
    unlimited_qr_codes: false,
    unlimited_branches: false,
  },
  limits: {
    shops: {
      max: 1,
      current: 0,
      unlimited: false,
      allowed: true,
    },
    customers_per_shop: {
      max: 50,
      unlimited: false,
    },
    api_keys_per_shop: {
      max: 1,
    },
    branches_per_shop: {
      max: 0,
      unlimited: false,
    },
    qr_codes_per_month: {
      max: 500,
      current: 25,
      unlimited: false,
      allowed: true,
    },
    webhooks: {
      max: 0,
      current: 0,
      allowed: false,
    },
    credits: {
      limit: 100000,
      current: 95000,
    },
    analytics_history_days: 30,
    support_level: 'community',
    allowed_shop_types: ['retail'],
  },
};

const mockBusinessLimits = {
  subscription_tier: {
    id: 2,
    name: 'Business',
    description: 'Growing retail businesses and small chains',
    price: 49,
    credit_limit: 1000000,
    features: ['Full API access', 'Email support', 'Multiple shops'],
    max_shops: 5,
    max_customers_per_shop: 1000,
    max_api_keys_per_shop: 5,
    max_branches_per_shop: 10,
    max_qr_codes_per_month: 2000,
    analytics_history_days: 365,
    support_level: 'email',
    allowed_shop_types: ['retail', 'api_service'],
    unlimited_shops: false,
    unlimited_customers: false,
    unlimited_qr_codes: false,
    unlimited_branches: false,
  },
  limits: {
    shops: {
      max: 5,
      current: 2,
      unlimited: false,
      allowed: true,
    },
    customers_per_shop: {
      max: 1000,
      unlimited: false,
    },
    api_keys_per_shop: {
      max: 5,
    },
    branches_per_shop: {
      max: 10,
      unlimited: false,
    },
    qr_codes_per_month: {
      max: 2000,
      current: 150,
      unlimited: false,
      allowed: true,
    },
    webhooks: {
      max: 3,
      current: 1,
      allowed: true,
    },
    credits: {
      limit: 1000000,
      current: 850000,
    },
    analytics_history_days: 365,
    support_level: 'email',
    allowed_shop_types: ['retail', 'api_service'],
  },
};

const mockEnterpriseLimits = {
  subscription_tier: {
    id: 3,
    name: 'Enterprise',
    description: 'Large businesses, franchises, and high-volume retailers',
    price: 199,
    credit_limit: 5000000,
    features: ['Full API access', '24/7 support', 'Unlimited shops'],
    max_shops: 0,
    max_customers_per_shop: 0,
    max_api_keys_per_shop: 20,
    max_branches_per_shop: 0,
    max_qr_codes_per_month: 0,
    analytics_history_days: 0,
    support_level: 'priority',
    allowed_shop_types: ['retail', 'api_service', 'enterprise'],
    unlimited_shops: true,
    unlimited_customers: true,
    unlimited_qr_codes: true,
    unlimited_branches: true,
  },
  limits: {
    shops: {
      max: 0,
      current: 15,
      unlimited: true,
      allowed: true,
    },
    customers_per_shop: {
      max: 0,
      unlimited: true,
    },
    api_keys_per_shop: {
      max: 20,
    },
    branches_per_shop: {
      max: 0,
      unlimited: true,
    },
    qr_codes_per_month: {
      max: 0,
      current: 5000,
      unlimited: true,
      allowed: true,
    },
    webhooks: {
      max: 15,
      current: 5,
      allowed: true,
    },
    credits: {
      limit: 5000000,
      current: 4200000,
    },
    analytics_history_days: 0,
    support_level: 'priority',
    allowed_shop_types: ['retail', 'api_service', 'enterprise'],
  },
};

const renderWithQueryClient = (component: React.ReactElement) => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
      },
    },
  });

  return render(
    <QueryClientProvider client={queryClient}>
      {component}
    </QueryClientProvider>
  );
};

describe('SubscriptionLimitsCard Integration Tests', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('displays Starter plan limits correctly', async () => {
    mockUseSubscriptionLimits.mockReturnValue({
      data: mockStarterLimits,
      isLoading: false,
      error: null,
    } as any);

    renderWithQueryClient(<SubscriptionLimitsCard />);

    await waitFor(() => {
      expect(screen.getByText('Starter Plan')).toBeInTheDocument();
    });

    // Check price display
    expect(screen.getByText('$0')).toBeInTheDocument();
    expect(screen.getByText('/month')).toBeInTheDocument();

    // Check support level
    expect(screen.getByText('Community Support')).toBeInTheDocument();

    // Check shop limits
    expect(screen.getByText('Shops')).toBeInTheDocument();
    expect(screen.getByText('0 / 1')).toBeInTheDocument();

    // Check QR code limits
    expect(screen.getByText('QR Codes')).toBeInTheDocument();
    expect(screen.getByText('25 / 500 this month')).toBeInTheDocument();

    // Check credits
    expect(screen.getByText('Credits')).toBeInTheDocument();
    expect(screen.getByText('95,000 / 100,000')).toBeInTheDocument();

    // Check allowed shop types
    expect(screen.getByText('RETAIL')).toBeInTheDocument();

    // Check upgrade CTA for starter plan
    expect(screen.getByText('Need more resources?')).toBeInTheDocument();
    expect(screen.getByText('Upgrade Plan')).toBeInTheDocument();
  });

  test('displays Business plan limits correctly', async () => {
    mockUseSubscriptionLimits.mockReturnValue({
      data: mockBusinessLimits,
      isLoading: false,
      error: null,
    } as any);

    renderWithQueryClient(<SubscriptionLimitsCard />);

    await waitFor(() => {
      expect(screen.getByText('Business Plan')).toBeInTheDocument();
    });

    // Check price display
    expect(screen.getByText('$49')).toBeInTheDocument();

    // Check support level
    expect(screen.getByText('Email Support')).toBeInTheDocument();

    // Check shop limits
    expect(screen.getByText('2 / 5')).toBeInTheDocument();

    // Check QR code limits
    expect(screen.getByText('150 / 2,000 this month')).toBeInTheDocument();

    // Check webhooks
    expect(screen.getByText('Webhooks')).toBeInTheDocument();
    expect(screen.getByText('1 / 3')).toBeInTheDocument();

    // Check allowed shop types
    expect(screen.getByText('RETAIL')).toBeInTheDocument();
    expect(screen.getByText('API_SERVICE')).toBeInTheDocument();

    // Should not show upgrade CTA for business plan
    expect(screen.queryByText('Need more resources?')).not.toBeInTheDocument();
  });

  test('displays Enterprise plan with unlimited features', async () => {
    mockUseSubscriptionLimits.mockReturnValue({
      data: mockEnterpriseLimits,
      isLoading: false,
      error: null,
    } as any);

    renderWithQueryClient(<SubscriptionLimitsCard />);

    await waitFor(() => {
      expect(screen.getByText('Enterprise Plan')).toBeInTheDocument();
    });

    // Check price display
    expect(screen.getByText('$199')).toBeInTheDocument();

    // Check support level
    expect(screen.getByText('Priority Support')).toBeInTheDocument();

    // Check unlimited features
    expect(screen.getByText('15 / Unlimited')).toBeInTheDocument(); // Shops
    expect(screen.getByText('5,000 / Unlimited this month')).toBeInTheDocument(); // QR Codes

    // Check unlimited indicators in additional limits
    expect(screen.getByText('Unlimited')).toBeInTheDocument(); // Should appear multiple times

    // Check all shop types are allowed
    expect(screen.getByText('RETAIL')).toBeInTheDocument();
    expect(screen.getByText('API_SERVICE')).toBeInTheDocument();
    expect(screen.getByText('ENTERPRISE')).toBeInTheDocument();
  });

  test('displays loading state correctly', async () => {
    mockUseSubscriptionLimits.mockReturnValue({
      data: undefined,
      isLoading: true,
      error: null,
    } as any);

    renderWithQueryClient(<SubscriptionLimitsCard />);

    expect(screen.getByText('Subscription Limits')).toBeInTheDocument();
    expect(screen.getByText('Your current subscription usage and limits')).toBeInTheDocument();

    // Should show skeleton loaders
    const skeletons = screen.getAllByTestId('skeleton');
    expect(skeletons).toHaveLength(6);
  });

  test('displays error state correctly', async () => {
    mockUseSubscriptionLimits.mockReturnValue({
      data: undefined,
      isLoading: false,
      error: new Error('Failed to load'),
    } as any);

    renderWithQueryClient(<SubscriptionLimitsCard />);

    await waitFor(() => {
      expect(screen.getByText('Unable to load subscription limits')).toBeInTheDocument();
    });

    expect(screen.getByText('Retry')).toBeInTheDocument();
  });

  test('shows warning indicators for high usage', async () => {
    const highUsageLimits = {
      ...mockStarterLimits,
      limits: {
        ...mockStarterLimits.limits,
        qr_codes_per_month: {
          max: 500,
          current: 475, // 95% usage
          unlimited: false,
          allowed: true,
        },
        credits: {
          limit: 100000,
          current: 5000, // 5% remaining
        },
      },
    };

    mockUseSubscriptionLimits.mockReturnValue({
      data: highUsageLimits,
      isLoading: false,
      error: null,
    } as any);

    renderWithQueryClient(<SubscriptionLimitsCard />);

    await waitFor(() => {
      expect(screen.getByText('475 / 500 this month')).toBeInTheDocument();
    });

    // Progress bars should show warning/danger colors for high usage
    const progressBars = screen.getAllByRole('progressbar');
    expect(progressBars.length).toBeGreaterThan(0);
  });

  test('handles limit exceeded state', async () => {
    const exceededLimits = {
      ...mockStarterLimits,
      limits: {
        ...mockStarterLimits.limits,
        shops: {
          max: 1,
          current: 1,
          unlimited: false,
          allowed: false, // Limit exceeded
        },
        qr_codes_per_month: {
          max: 500,
          current: 500,
          unlimited: false,
          allowed: false, // Limit exceeded
        },
      },
    };

    mockUseSubscriptionLimits.mockReturnValue({
      data: exceededLimits,
      isLoading: false,
      error: null,
    } as any);

    renderWithQueryClient(<SubscriptionLimitsCard />);

    await waitFor(() => {
      expect(screen.getByText('1 / 1')).toBeInTheDocument();
    });

    // Should show alert indicators for exceeded limits
    const alertIcons = screen.getAllByTestId('alert-triangle');
    expect(alertIcons.length).toBeGreaterThan(0);
  });

  test('upgrade button navigates correctly', async () => {
    mockUseSubscriptionLimits.mockReturnValue({
      data: mockStarterLimits,
      isLoading: false,
      error: null,
    } as any);

    renderWithQueryClient(<SubscriptionLimitsCard />);

    await waitFor(() => {
      expect(screen.getByText('Upgrade Plan')).toBeInTheDocument();
    });

    const upgradeButton = screen.getByText('Upgrade Plan');
    expect(upgradeButton.closest('a')).toHaveAttribute('href', '/dashboard/subscriptions');
  });
});

describe('Subscription Limits Hook Integration', () => {
  beforeEach(() => {
    global.fetch = jest.fn();
    jest.clearAllMocks();
  });

  test('fetches user limits successfully', async () => {
    const mockFetch = global.fetch as jest.MockedFunction<typeof fetch>;
    mockFetch.mockResolvedValueOnce({
      ok: true,
      json: async () => mockStarterLimits,
    } as Response);

    const { result } = renderHook(() => useSubscriptionLimits(), {
      wrapper: ({ children }) => (
        <QueryClientProvider client={new QueryClient()}>
          {children}
        </QueryClientProvider>
      ),
    });

    await waitFor(() => {
      expect(result.current.isSuccess).toBe(true);
    });

    expect(result.current.data).toEqual(mockStarterLimits);
    expect(mockFetch).toHaveBeenCalledWith('/api/subscription-limits', {
      headers: {
        'Authorization': 'Bearer null',
      },
    });
  });

  test('handles fetch error correctly', async () => {
    const mockFetch = global.fetch as jest.MockedFunction<typeof fetch>;
    mockFetch.mockResolvedValueOnce({
      ok: false,
      status: 500,
    } as Response);

    const { result } = renderHook(() => useSubscriptionLimits(), {
      wrapper: ({ children }) => (
        <QueryClientProvider client={new QueryClient()}>
          {children}
        </QueryClientProvider>
      ),
    });

    await waitFor(() => {
      expect(result.current.isError).toBe(true);
    });

    expect(result.current.error).toBeDefined();
  });
});
