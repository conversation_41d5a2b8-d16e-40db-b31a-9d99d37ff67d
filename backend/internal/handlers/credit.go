package handlers

import (
	"net/http"
	"time"

	"github.com/adc-credit/backend/internal/database"
	"github.com/adc-credit/backend/internal/models"
	"github.com/adc-credit/backend/internal/services"
	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

// Initialize services
var (
	webhookService   = services.NewWebhookService()
	analyticsService = services.NewAnalyticsService()
)

// GetCreditBalance returns the credit balance for the current user
func GetCreditBalance(c *gin.Context) {
	user := c.MustGet("user").(models.User)

	var subscription models.Subscription
	if err := database.DB.Where("user_id = ? AND status = ?", user.ID, "active").
		Preload("SubscriptionTier").
		First(&subscription).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "No active subscription found"})
		return
	}

	c.J<PERSON>(http.StatusOK, gin.H{
		"credit_balance": subscription.CreditBalance,
		"credit_limit":   subscription.SubscriptionTier.CreditLimit,
		"subscription":   subscription,
	})
}

// AddCredits adds credits to the user's account
func AddCredits(c *gin.Context) {
	user := c.MustGet("user").(models.User)

	type AddCreditsRequest struct {
		Amount      int    `json:"amount" binding:"required,min=1"`
		Description string `json:"description"`
		Reference   string `json:"reference"`
	}

	var req AddCreditsRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Get active subscription
	var subscription models.Subscription
	if err := database.DB.Where("user_id = ? AND status = ?", user.ID, "active").First(&subscription).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "No active subscription found"})
		return
	}

	// Start a transaction
	tx := database.DB.Begin()

	// Update credit balance
	subscription.CreditBalance += req.Amount
	if err := tx.Save(&subscription).Error; err != nil {
		tx.Rollback()
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update credit balance"})
		return
	}

	// Create transaction record
	transaction := models.Transaction{
		ID:             uuid.New(),
		UserID:         user.ID,
		SubscriptionID: subscription.ID,
		Type:           "credit_add",
		Amount:         req.Amount,
		Description:    req.Description,
		Reference:      req.Reference,
	}

	if err := tx.Create(&transaction).Error; err != nil {
		tx.Rollback()
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create transaction record"})
		return
	}

	// Commit transaction
	if err := tx.Commit().Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to commit transaction"})
		return
	}

	// Send webhook notification
	go func() {
		webhookData := map[string]interface{}{
			"user_id":         user.ID.String(),
			"credits":         req.Amount,
			"credit_balance":  subscription.CreditBalance,
			"transaction_id":  transaction.ID.String(),
			"subscription_id": subscription.ID.String(),
			"description":     req.Description,
		}
		webhookService.SendWebhook(user.ID, "credit.added", webhookData)
	}()

	c.JSON(http.StatusOK, gin.H{
		"message":        "Credits added successfully",
		"credit_balance": subscription.CreditBalance,
		"transaction":    transaction,
	})
}

// ConsumeCredits consumes credits for an API operation
func ConsumeCredits(c *gin.Context) {
	apiKey := c.MustGet("apiKey").(models.APIKey)
	user := c.MustGet("user").(models.User)

	type ConsumeCreditsRequest struct {
		Endpoint  string `json:"endpoint" binding:"required"`
		Method    string `json:"method" binding:"required"`
		Credits   int    `json:"credits" binding:"required,min=1"`
		IPAddress string `json:"ip_address"`
		UserAgent string `json:"user_agent"`
	}

	var req ConsumeCreditsRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Get active subscription
	var subscription models.Subscription
	if err := database.DB.Where("user_id = ? AND status = ?", user.ID, "active").First(&subscription).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "No active subscription found"})
		return
	}

	// Check if enough credits
	if subscription.CreditBalance < req.Credits {
		c.JSON(http.StatusPaymentRequired, gin.H{"error": "Insufficient credits"})
		return
	}

	// Start a transaction
	tx := database.DB.Begin()

	// Update credit balance
	subscription.CreditBalance -= req.Credits
	if err := tx.Save(&subscription).Error; err != nil {
		tx.Rollback()
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update credit balance"})
		return
	}

	// Create usage record
	usage := models.Usage{
		ID:           uuid.New(),
		APIKeyID:     apiKey.ID,
		Endpoint:     req.Endpoint,
		Method:       req.Method,
		Credits:      req.Credits,
		Timestamp:    time.Now(),
		Success:      true,
		IPAddress:    req.IPAddress,
		UserAgent:    req.UserAgent,
		ResponseTime: 0, // Will be updated later
		StatusCode:   http.StatusOK,
	}

	if err := tx.Create(&usage).Error; err != nil {
		tx.Rollback()
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create usage record"})
		return
	}

	// Create transaction record
	transaction := models.Transaction{
		ID:             uuid.New(),
		UserID:         user.ID,
		SubscriptionID: subscription.ID,
		Type:           "credit_use",
		Amount:         -req.Credits,
		Description:    "API usage: " + req.Method + " " + req.Endpoint,
		Reference:      usage.ID.String(),
	}

	if err := tx.Create(&transaction).Error; err != nil {
		tx.Rollback()
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create transaction record"})
		return
	}

	// Commit transaction
	if err := tx.Commit().Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to commit transaction"})
		return
	}

	// Send webhook notification
	go func() {
		webhookData := map[string]interface{}{
			"user_id":         user.ID.String(),
			"api_key_id":      apiKey.ID.String(),
			"credits":         req.Credits,
			"endpoint":        req.Endpoint,
			"method":          req.Method,
			"credit_balance":  subscription.CreditBalance,
			"transaction_id":  transaction.ID.String(),
			"subscription_id": subscription.ID.String(),
		}
		webhookService.SendWebhook(user.ID, "credit.consumed", webhookData)
	}()

	// Record analytics data
	go func() {
		// Update response time with actual value (in a real app, you'd measure this)
		usage.ResponseTime = 100 // Example: 100ms response time
		analyticsService.RecordAPIUsage(usage)
	}()

	c.JSON(http.StatusOK, gin.H{
		"message":        "Credits consumed successfully",
		"credit_balance": subscription.CreditBalance,
		"usage":          usage,
	})
}

// GetTransactions returns the transaction history for the current user
func GetTransactions(c *gin.Context) {
	user := c.MustGet("user").(models.User)

	var transactions []models.Transaction
	if err := database.DB.Where("user_id = ?", user.ID).Order("created_at DESC").Find(&transactions).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch transactions"})
		return
	}

	c.JSON(http.StatusOK, transactions)
}

// VerifyAPIKey verifies if an API key is valid and has enough credits
func VerifyAPIKey(c *gin.Context) {
	type VerifyAPIKeyRequest struct {
		APIKey  string `json:"api_key" binding:"required"`
		Credits int    `json:"credits" binding:"required,min=1"`
	}

	var req VerifyAPIKeyRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	var apiKey models.APIKey
	if err := database.DB.Where("key = ? AND enabled = ?", req.APIKey, true).First(&apiKey).Error; err != nil {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid API key"})
		return
	}

	// Get user associated with API key
	var user models.User
	if err := database.DB.First(&user, "id = ?", apiKey.UserID).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to find user associated with API key"})
		return
	}

	// Get active subscription
	var subscription models.Subscription
	if err := database.DB.Where("user_id = ? AND status = ?", user.ID, "active").First(&subscription).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "No active subscription found"})
		return
	}

	// Check if enough credits
	if subscription.CreditBalance < req.Credits {
		c.JSON(http.StatusPaymentRequired, gin.H{
			"error":          "Insufficient credits",
			"credit_balance": subscription.CreditBalance,
			"required":       req.Credits,
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"valid":          true,
		"credit_balance": subscription.CreditBalance,
	})
}
