package main

import (
	"fmt"
	"log"
	"os"

	"github.com/joho/godotenv"
	"gorm.io/driver/postgres"
	"gorm.io/gorm"
)

func main() {
	// Load environment variables
	if err := godotenv.Load(); err != nil {
		// Try to load from the project root directory
		if err := godotenv.Load("../../.env"); err != nil {
			log.Println("No .env file found, using environment variables")
		}
	}

	// Get database URL
	databaseURL := os.Getenv("DATABASE_URL")
	if databaseURL == "" {
		log.Fatal("DATABASE_URL environment variable is required")
	}

	fmt.Printf("🔗 Connecting to database: %s\n", databaseURL[:50]+"...")

	// Connect to database directly
	db, err := gorm.Open(postgres.Open(databaseURL), &gorm.Config{})
	if err != nil {
		log.Fatalf("Failed to connect to database: %v", err)
	}

	fmt.Println("🚀 Starting PRODUCTION migration to unified Shop model...")
	fmt.Println("⚠️  This will modify your production database!")

	if err := productionMigrateToUnifiedShops(db); err != nil {
		log.Fatalf("Migration failed: %v", err)
	}

	fmt.Println("✅ PRODUCTION migration completed successfully!")
}

func productionMigrateToUnifiedShops(db *gorm.DB) error {
	// Step 1: Create the new shops table
	fmt.Println("📋 Step 1: Creating shops table...")
	createShopsSQL := `
	CREATE TABLE IF NOT EXISTS shops (
		id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
		slug TEXT,
		name TEXT,
		description TEXT,
		shop_type TEXT DEFAULT 'retail',
		contact_email TEXT,
		contact_phone TEXT,
		owner_user_id UUID NOT NULL,
		created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
		updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
		deleted_at TIMESTAMP WITH TIME ZONE
	);`

	if err := db.Exec(createShopsSQL).Error; err != nil {
		return fmt.Errorf("failed to create shops table: %w", err)
	}

	// Step 2: Create indexes for shops table
	fmt.Println("📋 Step 2: Creating shops table indexes...")
	indexSQL := []string{
		"CREATE INDEX IF NOT EXISTS idx_shops_deleted_at ON shops(deleted_at);",
		"CREATE UNIQUE INDEX IF NOT EXISTS idx_shops_slug ON shops(slug) WHERE deleted_at IS NULL;",
		"CREATE INDEX IF NOT EXISTS idx_shops_owner_user_id ON shops(owner_user_id);",
		"CREATE INDEX IF NOT EXISTS idx_shops_shop_type ON shops(shop_type);",
	}

	for _, sql := range indexSQL {
		if err := db.Exec(sql).Error; err != nil {
			return fmt.Errorf("failed to create index: %w", err)
		}
	}

	// Step 3: Migrate data from organizations to shops
	fmt.Println("📋 Step 3: Migrating organizations to shops...")
	migrateOrgSQL := `
	INSERT INTO shops (id, slug, name, description, shop_type, owner_user_id, created_at, updated_at, deleted_at)
	SELECT id, slug, name, description, 'api_service', owner_user_id, created_at, updated_at, deleted_at
	FROM organizations
	ON CONFLICT (id) DO NOTHING;`

	result := db.Exec(migrateOrgSQL)
	if result.Error != nil {
		return fmt.Errorf("failed to migrate organizations: %w", result.Error)
	}
	fmt.Printf("   ✅ Migrated %d organizations to shops\n", result.RowsAffected)

	// Step 4: Migrate data from merchant_shops to shops
	fmt.Println("📋 Step 4: Migrating merchant_shops to shops...")
	migrateMerchantSQL := `
	INSERT INTO shops (id, slug, name, description, shop_type, contact_email, contact_phone, owner_user_id, created_at, updated_at, deleted_at)
	SELECT id, slug, name, description, 'retail', contact_email, contact_phone, owner_user_id, created_at, updated_at, deleted_at
	FROM merchant_shops
	ON CONFLICT (id) DO NOTHING;`

	result = db.Exec(migrateMerchantSQL)
	if result.Error != nil {
		return fmt.Errorf("failed to migrate merchant shops: %w", result.Error)
	}
	fmt.Printf("   ✅ Migrated %d merchant shops to shops\n", result.RowsAffected)

	// Step 5: Create shop_branches table
	fmt.Println("📋 Step 5: Creating shop_branches table...")
	createShopBranchesSQL := `
	CREATE TABLE IF NOT EXISTS shop_branches (
		id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
		shop_id UUID NOT NULL,
		name TEXT,
		description TEXT,
		branch_type TEXT DEFAULT 'location',
		contact_email TEXT,
		contact_phone TEXT,
		address TEXT,
		created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
		updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
		deleted_at TIMESTAMP WITH TIME ZONE
	);`

	if err := db.Exec(createShopBranchesSQL).Error; err != nil {
		return fmt.Errorf("failed to create shop_branches table: %w", err)
	}

	// Step 6: Create indexes for shop_branches table
	fmt.Println("📋 Step 6: Creating shop_branches table indexes...")
	branchIndexSQL := []string{
		"CREATE INDEX IF NOT EXISTS idx_shop_branches_deleted_at ON shop_branches(deleted_at);",
		"CREATE INDEX IF NOT EXISTS idx_shop_branches_shop_id ON shop_branches(shop_id);",
		"CREATE INDEX IF NOT EXISTS idx_shop_branches_branch_type ON shop_branches(branch_type);",
	}

	for _, sql := range branchIndexSQL {
		if err := db.Exec(sql).Error; err != nil {
			return fmt.Errorf("failed to create branch index: %w", err)
		}
	}

	// Step 7: Migrate branches to shop_branches
	fmt.Println("📋 Step 7: Migrating branches to shop_branches...")
	migrateBranchesSQL := `
	INSERT INTO shop_branches (id, shop_id, name, description, branch_type, created_at, updated_at, deleted_at)
	SELECT id, organization_id, name, description, 'department', created_at, updated_at, deleted_at
	FROM branches
	WHERE organization_id IS NOT NULL
	ON CONFLICT (id) DO NOTHING;`

	result = db.Exec(migrateBranchesSQL)
	if result.Error != nil {
		return fmt.Errorf("failed to migrate branches: %w", result.Error)
	}
	fmt.Printf("   ✅ Migrated %d branches to shop_branches\n", result.RowsAffected)

	// Step 8: Add new columns to existing tables
	fmt.Println("📋 Step 8: Adding new columns to existing tables...")
	alterSQL := []string{
		"ALTER TABLE users ADD COLUMN IF NOT EXISTS shop_id UUID;",
		"ALTER TABLE users ADD COLUMN IF NOT EXISTS shop_branch_id UUID;",
		"ALTER TABLE api_keys ADD COLUMN IF NOT EXISTS shop_id UUID;",
		"ALTER TABLE api_keys ADD COLUMN IF NOT EXISTS shop_branch_id UUID;",
		"ALTER TABLE subscriptions ADD COLUMN IF NOT EXISTS shop_id UUID;",
	}

	for _, sql := range alterSQL {
		if err := db.Exec(sql).Error; err != nil {
			return fmt.Errorf("failed to add column: %w", err)
		}
	}

	// Step 9: Update foreign key references
	fmt.Println("📋 Step 9: Updating foreign key references...")
	updateSQL := []string{
		"UPDATE users SET shop_id = organization_id, shop_branch_id = branch_id WHERE organization_id IS NOT NULL;",
		"UPDATE subscriptions SET shop_id = merchant_shop_id WHERE merchant_shop_id IS NOT NULL;",
	}

	for _, sql := range updateSQL {
		result := db.Exec(sql)
		if result.Error != nil {
			return fmt.Errorf("failed to update references: %w", result.Error)
		}
		fmt.Printf("   ✅ Updated %d records\n", result.RowsAffected)
	}

	// Step 10: Add foreign key constraints (PostgreSQL compatible syntax)
	fmt.Println("📋 Step 10: Adding foreign key constraints...")
	constraintSQL := []string{
		`DO $$ BEGIN
			IF NOT EXISTS (SELECT 1 FROM pg_constraint WHERE conname = 'fk_shops_owner') THEN
				ALTER TABLE shops ADD CONSTRAINT fk_shops_owner FOREIGN KEY (owner_user_id) REFERENCES users(id);
			END IF;
		END $$;`,
		`DO $$ BEGIN
			IF NOT EXISTS (SELECT 1 FROM pg_constraint WHERE conname = 'fk_shop_branches_shop') THEN
				ALTER TABLE shop_branches ADD CONSTRAINT fk_shop_branches_shop FOREIGN KEY (shop_id) REFERENCES shops(id);
			END IF;
		END $$;`,
		`DO $$ BEGIN
			IF NOT EXISTS (SELECT 1 FROM pg_constraint WHERE conname = 'fk_users_shop') THEN
				ALTER TABLE users ADD CONSTRAINT fk_users_shop FOREIGN KEY (shop_id) REFERENCES shops(id);
			END IF;
		END $$;`,
		`DO $$ BEGIN
			IF NOT EXISTS (SELECT 1 FROM pg_constraint WHERE conname = 'fk_users_shop_branch') THEN
				ALTER TABLE users ADD CONSTRAINT fk_users_shop_branch FOREIGN KEY (shop_branch_id) REFERENCES shop_branches(id);
			END IF;
		END $$;`,
		`DO $$ BEGIN
			IF NOT EXISTS (SELECT 1 FROM pg_constraint WHERE conname = 'fk_api_keys_shop') THEN
				ALTER TABLE api_keys ADD CONSTRAINT fk_api_keys_shop FOREIGN KEY (shop_id) REFERENCES shops(id);
			END IF;
		END $$;`,
		`DO $$ BEGIN
			IF NOT EXISTS (SELECT 1 FROM pg_constraint WHERE conname = 'fk_api_keys_shop_branch') THEN
				ALTER TABLE api_keys ADD CONSTRAINT fk_api_keys_shop_branch FOREIGN KEY (shop_branch_id) REFERENCES shop_branches(id);
			END IF;
		END $$;`,
		`DO $$ BEGIN
			IF NOT EXISTS (SELECT 1 FROM pg_constraint WHERE conname = 'fk_subscriptions_shop') THEN
				ALTER TABLE subscriptions ADD CONSTRAINT fk_subscriptions_shop FOREIGN KEY (shop_id) REFERENCES shops(id);
			END IF;
		END $$;`,
	}

	for _, sql := range constraintSQL {
		if err := db.Exec(sql).Error; err != nil {
			// Log warning but don't fail - constraints might already exist
			log.Printf("Warning: Failed to add constraint: %v", err)
		}
	}

	// Step 11: Verify migration
	fmt.Println("📋 Step 11: Verifying migration...")
	var shopCount, branchCount int64
	
	if err := db.Model(&struct{ ID string }{}).Table("shops").Count(&shopCount).Error; err != nil {
		return fmt.Errorf("failed to count shops: %w", err)
	}
	
	if err := db.Model(&struct{ ID string }{}).Table("shop_branches").Count(&branchCount).Error; err != nil {
		return fmt.Errorf("failed to count shop_branches: %w", err)
	}

	fmt.Printf("   ✅ Total shops: %d\n", shopCount)
	fmt.Printf("   ✅ Total shop branches: %d\n", branchCount)

	// Step 12: Create unique constraint on slug (after data migration)
	fmt.Println("📋 Step 12: Adding unique constraint on slug...")
	uniqueSlugSQL := `
	DO $$ BEGIN
		IF NOT EXISTS (SELECT 1 FROM pg_constraint WHERE conname = 'shops_slug_key') THEN
			ALTER TABLE shops ADD CONSTRAINT shops_slug_key UNIQUE (slug);
		END IF;
	END $$;`
	
	if err := db.Exec(uniqueSlugSQL).Error; err != nil {
		log.Printf("Warning: Failed to add unique constraint on slug: %v", err)
	}

	return nil
}
