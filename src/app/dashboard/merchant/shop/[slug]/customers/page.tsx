"use client";

import React, { useState } from "react";
import { useRouter } from "next/navigation";
import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useToast } from "@/components/ui/use-toast";
import { Plus, User } from "phosphor-react";
import { MerchantBreadcrumbs } from "@/components/navigation/breadcrumbs";
import { MerchantPageHeader } from "@/components/layout/page-header-mobile";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import {
  useGetMerchantShopBySlugQuery,
  useGetShopCustomersQuery,
  useAddShopCustomerMutation,
  useAddShopCreditMutation
} from "@/lib/api/apiSlice";
import { ShopCustomer } from "@/types";

// Define form schema with Zod
const formSchema = z.object({
  name: z.string().min(1, { message: "Customer name is required" }),
  email: z.string().email({ message: "Invalid email address" }),
  phone: z.string().optional(),
});

type FormValues = z.infer<typeof formSchema>;

export default function CustomersPage({ params }: { params: { slug: string } }) {
  // Unwrap params using React.use() as recommended by Next.js
  const unwrappedParams = React.use(params as any) as { slug: string };
  const shopSlug = unwrappedParams.slug;

  const router = useRouter();
  const { toast } = useToast();

  // Fetch shop and customer data using slug
  const { data: shop, isLoading: isShopLoading } = useGetMerchantShopBySlugQuery(shopSlug);
  const { data: customers, isLoading: isCustomersLoading, refetch: refetchCustomers } = 
    useGetShopCustomersQuery(shop?.id || "", { skip: !shop?.id });

  // Mutations
  const [addCustomer, { isLoading: isAddingCustomer }] = useAddShopCustomerMutation();
  const [addCredit] = useAddShopCreditMutation();

  // Dialog state
  const [isAddCustomerDialogOpen, setIsAddCustomerDialogOpen] = useState(false);

  // Initialize React Hook Form
  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: "",
      email: "",
      phone: "",
    },
  });

  const onSubmit = async (data: FormValues) => {
    if (!shop?.id) return;

    try {
      await addCustomer({
        shopId: shop.id,
        name: data.name,
        email: data.email,
        phone: data.phone || ""
      }).unwrap();

      toast({
        title: "Success",
        description: "Customer added successfully",
      });

      setIsAddCustomerDialogOpen(false);
      form.reset();
      refetchCustomers();
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.data?.message || "Failed to add customer",
        variant: "destructive",
      });
    }
  };

  const handleAddCredit = async (customerId: string) => {
    router.push(`/dashboard/merchant/shop/${shopSlug}/customers/${customerId}/add-credit`);
  };

  if (isShopLoading) {
    return <div className="p-4 text-center">Loading shop details...</div>;
  }

  if (!shop) {
    return <div className="p-4 text-center">Shop not found</div>;
  }

  return (
    <div className="relative flex size-full min-h-screen flex-col bg-[#fbfaf9] justify-between group/design-root overflow-x-hidden">
      <div>
        <MerchantPageHeader
          title="Customers"
          backHref={`/dashboard/merchant/shop/${shopSlug}`}
        />

        {/* Breadcrumbs */}
        <MerchantBreadcrumbs
          segments={[
            { label: "Shops", href: "/dashboard/merchant" },
            { label: shop?.name || "Shop", href: `/dashboard/merchant/shop/${shopSlug}` },
            { label: "Customers", href: `/dashboard/merchant/shop/${shopSlug}/customers`, isCurrentPage: true }
          ]}
        />

        {isCustomersLoading ? (
          <div className="flex justify-center items-center h-32">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#e5ccb2]"></div>
          </div>
        ) : customers && customers.length > 0 ? (
          <div>
            {customers.map((customer: ShopCustomer) => (
              <div key={customer.id} className="flex items-center gap-4 bg-[#fbfaf9] px-4 min-h-[72px] py-2">
                <div className="bg-center bg-no-repeat aspect-square bg-cover rounded-full h-14 w-14 flex items-center justify-center bg-[#e6e0db]">
                  <User size={24} weight="regular" className="text-[#8a745c]" />
                </div>
                <div className="flex flex-col justify-center flex-1">
                  <p className="text-[#181510] text-base font-medium leading-normal line-clamp-1">{customer.user?.name}</p>
                  <p className="text-[#8a745c] text-sm font-normal leading-normal line-clamp-2">{customer.user?.email}</p>
                </div>
                <div className="text-[#181510] text-base font-medium">
                  ${customer.credit_balance}
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="p-4 text-center">
            <User size={48} weight="regular" className="text-[#8a745c] mx-auto mb-4" />
            <p className="text-[#8a745c] mb-4">No customers yet</p>
            <p className="text-[#8a745c] text-sm mb-4">Add your first customer to get started</p>
          </div>
        )}
      </div>

      <div>
        <div className="flex justify-end overflow-hidden px-5 pb-5">
          <Button
            className="flex max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-lg h-14 px-5 bg-[#e5ccb2] text-[#181510] text-base font-bold leading-normal tracking-[0.015em] min-w-0 gap-4 pl-4 pr-6"
            onClick={() => setIsAddCustomerDialogOpen(true)}
          >
            <Plus size={24} weight="regular" className="text-[#181510]" />
            <span className="truncate">Add Customer</span>
          </Button>
        </div>
        <div className="h-5 bg-[#fbfaf9]"></div>
      </div>

      {/* Add Customer Dialog */}
      <Dialog open={isAddCustomerDialogOpen} onOpenChange={setIsAddCustomerDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Add Customer</DialogTitle>
            <DialogDescription>
              Add a new customer to your shop
            </DialogDescription>
          </DialogHeader>

          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Name</FormLabel>
                    <FormControl>
                      <Input placeholder="Customer name" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="email"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Email</FormLabel>
                    <FormControl>
                      <Input type="email" placeholder="<EMAIL>" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="phone"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Phone (Optional)</FormLabel>
                    <FormControl>
                      <Input type="tel" placeholder="+****************" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <DialogFooter>
                <Button variant="outline" type="button" onClick={() => setIsAddCustomerDialogOpen(false)}>
                  Cancel
                </Button>
                <Button type="submit" disabled={isAddingCustomer}>
                  {isAddingCustomer ? "Adding..." : "Add Customer"}
                </Button>
              </DialogFooter>
            </form>
          </Form>
        </DialogContent>
      </Dialog>
    </div>
  );
}
