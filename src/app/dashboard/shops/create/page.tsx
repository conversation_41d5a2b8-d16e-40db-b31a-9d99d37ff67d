"use client";

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { ArrowLeft, Store, Building, Briefcase } from 'lucide-react';
import { ADCCreditSDK } from '@/sdk';

const sdk = new ADCCreditSDK({
  apiUrl: process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8400',
  debug: true,
});

const createShopSchema = z.object({
  name: z.string().min(1, 'Shop name is required').max(100, 'Shop name must be less than 100 characters'),
  description: z.string().optional(),
  shop_type: z.enum(['retail', 'api_service', 'enterprise'], {
    required_error: 'Please select a shop type',
  }),
  contact_email: z.string().email('Invalid email address').optional().or(z.literal('')),
  contact_phone: z.string().optional(),
});

type CreateShopForm = z.infer<typeof createShopSchema>;

const shopTypes = [
  {
    value: 'retail',
    label: 'Retail Store',
    description: 'Coffee shops, restaurants, retail stores with customer loyalty programs',
    icon: Store,
  },
  {
    value: 'api_service',
    label: 'API Service',
    description: 'Companies providing APIs to other businesses with credit-based usage',
    icon: Briefcase,
  },
  {
    value: 'enterprise',
    label: 'Enterprise',
    description: 'Large organizations with multiple locations and departments',
    icon: Building,
  },
];

export default function CreateShopPage() {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const router = useRouter();

  const form = useForm<CreateShopForm>({
    resolver: zodResolver(createShopSchema),
    defaultValues: {
      name: '',
      description: '',
      shop_type: 'retail',
      contact_email: '',
      contact_phone: '',
    },
  });

  const onSubmit = async (data: CreateShopForm) => {
    try {
      setLoading(true);
      setError(null);

      const response = await sdk.shops.createShop({
        name: data.name,
        description: data.description || undefined,
        shop_type: data.shop_type,
        contact_email: data.contact_email || undefined,
        contact_phone: data.contact_phone || undefined,
      });

      if (response.error) {
        setError(response.error);
      } else {
        // Redirect to the new shop's page
        router.push(`/dashboard/shops/${response.data?.slug}`);
      }
    } catch (err) {
      setError('Failed to create shop');
      console.error('Error creating shop:', err);
    } finally {
      setLoading(false);
    }
  };

  const selectedShopType = form.watch('shop_type');
  const selectedTypeInfo = shopTypes.find(type => type.value === selectedShopType);

  return (
    <div className="container mx-auto p-6 max-w-2xl">
      <div className="flex items-center space-x-4 mb-6">
        <Button
          variant="ghost"
          size="sm"
          onClick={() => router.back()}
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back
        </Button>
        <div>
          <h1 className="text-3xl font-bold">Create New Shop</h1>
          <p className="text-muted-foreground">
            Set up your business in the unified credit system
          </p>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Shop Information</CardTitle>
          <CardDescription>
            Provide basic information about your shop. You can add branches, customers, and API keys later.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Shop Name</FormLabel>
                    <FormControl>
                      <Input placeholder="Enter shop name" {...field} />
                    </FormControl>
                    <FormDescription>
                      This will be displayed to customers and in your dashboard
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="description"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Description</FormLabel>
                    <FormControl>
                      <Textarea 
                        placeholder="Describe your shop (optional)"
                        className="resize-none"
                        {...field}
                      />
                    </FormControl>
                    <FormDescription>
                      A brief description of your shop or business
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="shop_type"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Shop Type</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select shop type" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {shopTypes.map((type) => (
                          <SelectItem key={type.value} value={type.value}>
                            <div className="flex items-center space-x-2">
                              <type.icon className="h-4 w-4" />
                              <span>{type.label}</span>
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    {selectedTypeInfo && (
                      <FormDescription>
                        {selectedTypeInfo.description}
                      </FormDescription>
                    )}
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="contact_email"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Contact Email</FormLabel>
                      <FormControl>
                        <Input 
                          type="email" 
                          placeholder="<EMAIL>" 
                          {...field} 
                        />
                      </FormControl>
                      <FormDescription>
                        Primary contact email (optional)
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="contact_phone"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Contact Phone</FormLabel>
                      <FormControl>
                        <Input 
                          type="tel" 
                          placeholder="+****************" 
                          {...field} 
                        />
                      </FormControl>
                      <FormDescription>
                        Primary contact phone (optional)
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              {error && (
                <div className="p-4 border border-red-200 bg-red-50 rounded-md">
                  <p className="text-red-600 text-sm">{error}</p>
                </div>
              )}

              <div className="flex justify-end space-x-4">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => router.back()}
                  disabled={loading}
                >
                  Cancel
                </Button>
                <Button type="submit" disabled={loading}>
                  {loading ? 'Creating...' : 'Create Shop'}
                </Button>
              </div>
            </form>
          </Form>
        </CardContent>
      </Card>
    </div>
  );
}
