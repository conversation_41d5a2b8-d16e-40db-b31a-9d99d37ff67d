"use client";

import { useRouter } from 'next/navigation';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Plus, Store, Building, Briefcase, MapPin, Users, Key } from 'lucide-react';
import { MerchantShop } from '@/types';
import { useGetMerchantShopsQuery } from '@/lib/api/apiSlice';

export default function ShopsPage() {
  const router = useRouter();

  // Use RTK Query hook for fetching shops with automatic authentication
  const {
    data: shops = [],
    isLoading: loading,
    error,
    refetch: fetchShops
  } = useGetMerchantShopsQuery();

  // For now, all merchant shops are retail type since we're using MerchantShop model
  const getShopTypeIcon = () => {
    return <Store className="h-5 w-5" />;
  };

  const getShopTypeBadge = () => {
    return (
      <Badge variant="default">
        RETAIL
      </Badge>
    );
  };

  if (loading) {
    return (
      <div className="container mx-auto p-6">
        <div className="flex items-center justify-between mb-6">
          <h1 className="text-3xl font-bold">Shops</h1>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {[1, 2, 3].map((i) => (
            <Card key={i} className="animate-pulse">
              <CardHeader>
                <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                <div className="h-3 bg-gray-200 rounded w-1/2"></div>
              </CardHeader>
              <CardContent>
                <div className="h-3 bg-gray-200 rounded w-full mb-2"></div>
                <div className="h-3 bg-gray-200 rounded w-2/3"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto p-6">
        <div className="flex items-center justify-between mb-6">
          <h1 className="text-3xl font-bold">Shops</h1>
        </div>
        <Card>
          <CardContent className="p-6">
            <p className="text-red-600">Error: {error.toString()}</p>
            <Button onClick={() => fetchShops()} className="mt-4">
              Try Again
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6">
      <div className="flex items-center justify-between mb-6">
        <div>
          <h1 className="text-3xl font-bold">Shops</h1>
          <p className="text-muted-foreground">
            Manage your retail stores, API services, and enterprise locations
          </p>
        </div>
        <Button onClick={() => router.push('/dashboard/merchant/shops')}>
          <Plus className="h-4 w-4 mr-2" />
          Create Shop
        </Button>
      </div>

      {shops.length === 0 ? (
        <Card>
          <CardContent className="p-12 text-center">
            <Store className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
            <h3 className="text-lg font-semibold mb-2">No shops yet</h3>
            <p className="text-muted-foreground mb-4">
              Create your first shop to get started with the unified credit system
            </p>
            <Button onClick={() => router.push('/dashboard/merchant/shops')}>
              <Plus className="h-4 w-4 mr-2" />
              Create Your First Shop
            </Button>
          </CardContent>
        </Card>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {shops.map((shop) => (
            <Card
              key={shop.id}
              className="cursor-pointer hover:shadow-lg transition-shadow"
              onClick={() => router.push(`/dashboard/merchant/shop/${shop.slug}`)}
            >
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    {getShopTypeIcon()}
                    <CardTitle className="text-lg">{shop.name}</CardTitle>
                  </div>
                  {getShopTypeBadge()}
                </div>
                <CardDescription className="line-clamp-2">
                  {shop.description || 'No description provided'}
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  {shop.contact_email && (
                    <div className="flex items-center text-sm text-muted-foreground">
                      <span className="font-medium">Email:</span>
                      <span className="ml-2">{shop.contact_email}</span>
                    </div>
                  )}
                  {shop.contact_phone && (
                    <div className="flex items-center text-sm text-muted-foreground">
                      <span className="font-medium">Phone:</span>
                      <span className="ml-2">{shop.contact_phone}</span>
                    </div>
                  )}

                  <div className="flex items-center justify-between pt-4 border-t">
                    <div className="flex items-center space-x-4 text-sm text-muted-foreground">
                      {shop.customers && shop.customers.length > 0 && (
                        <div className="flex items-center">
                          <Users className="h-4 w-4 mr-1" />
                          {shop.customers.length} customer{shop.customers.length !== 1 ? 's' : ''}
                        </div>
                      )}
                      {shop.credit_codes && shop.credit_codes.length > 0 && (
                        <div className="flex items-center">
                          <Key className="h-4 w-4 mr-1" />
                          {shop.credit_codes.length} code{shop.credit_codes.length !== 1 ? 's' : ''}
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
}
