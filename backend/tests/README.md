# ADC Credit Backend Testing Framework

This directory contains comprehensive integration tests for the ADC Credit System backend API.

## Overview

The testing framework provides:
- **API Integration Tests** - Test all API endpoints with real HTTP requests
- **Database Testing** - Isolated test database with automatic cleanup
- **Authentication Testing** - JWT and API key authentication testing
- **Test Utilities** - Helper functions for creating test data and assertions
- **Coverage Reports** - Code coverage analysis

## Test Structure

```
backend/tests/
├── README.md                    # This file
├── api/                        # API integration tests
│   ├── users_test.go           # User management API tests
│   ├── apikeys_test.go         # API key management tests
│   ├── credits_test.go         # Credit system tests
│   ├── subscriptions_test.go   # Subscription management tests
│   └── merchant_shops_test.go  # Merchant shop tests
└── internal/
    └── testing/                # Test utilities
        ├── setup.go            # Test environment setup
        └── helpers.go          # Test helper functions
```

## Running Tests

### Prerequisites

1. **PostgreSQL Database**: Ensure PostgreSQL is running
2. **Test Database**: Create a test database (automatically handled)
3. **Environment Variables**: Set up test environment variables

### Quick Start

```bash
# Run all tests
make test

# Run only backend tests
make test-backend

# Run only API integration tests
make test-api

# Run tests with coverage report
make test-coverage
```

### Manual Test Execution

```bash
# Navigate to backend directory
cd backend

# Install dependencies
go mod tidy

# Run all tests
go test -v ./tests/...

# Run specific test suite
go test -v ./tests/api/users_test.go

# Run with coverage
go test -v -coverprofile=coverage.out ./tests/...
go tool cover -html=coverage.out -o coverage.html
```

## Test Configuration

### Environment Variables

Create a `.env.test` file in the backend directory:

```env
# Test Database
TEST_DATABASE_URL=postgres://postgres:password@localhost:5432/adc_credit_test?sslmode=disable

# JWT Configuration
JWT_SECRET=test-jwt-secret-key-for-testing-only

# API Configuration
PORT=8400
GIN_MODE=test

# Test-specific settings
TEST_MODE=true
LOG_LEVEL=error
```

### Database Setup

The test framework automatically:
1. Creates a separate test database
2. Runs migrations before tests
3. Cleans up test data after each test
4. Drops test database after test suite completion

## Test Suites

### 1. User API Tests (`users_test.go`)

Tests user management endpoints:
- `GET /api/v1/users/me` - Get current user
- `PUT /api/v1/users/me` - Update current user
- `DELETE /api/v1/users/me` - Delete current user
- `GET /api/v1/users/:id` - Get user by ID
- `GET /api/v1/users` - List users

**Key Test Cases:**
- Authentication and authorization
- Data validation
- Error handling
- Response format validation

### 2. API Key Tests (`apikeys_test.go`)

Tests API key management:
- `POST /api/v1/apikeys` - Create API key
- `GET /api/v1/apikeys` - List API keys
- `GET /api/v1/apikeys/:id` - Get API key
- `PUT /api/v1/apikeys/:id` - Update API key
- `DELETE /api/v1/apikeys/:id` - Delete API key
- `POST /api/v1/apikeys/:id/regenerate` - Regenerate API key

**Key Test Cases:**
- API key generation and validation
- Permission-based access control
- Key regeneration
- Security (key masking in responses)

### 3. Credit System Tests (`credits_test.go`)

Tests credit management:
- `GET /api/v1/credits/balance` - Get credit balance
- `POST /api/v1/credits/consume` - Consume credits
- `POST /api/v1/credits/add` - Add credits
- `GET /api/v1/credits/history` - Get credit history
- `POST /api/v1/external/consume` - External credit consumption

**Key Test Cases:**
- Credit balance tracking
- Credit consumption validation
- Insufficient balance handling
- Transaction history
- External API authentication

### 4. Subscription Tests (`subscriptions_test.go`)

Tests subscription management:
- `GET /api/v1/subscription-tiers` - List subscription tiers
- `POST /api/v1/subscriptions` - Create subscription
- `GET /api/v1/subscriptions` - List subscriptions
- `PUT /api/v1/subscriptions/:id` - Update subscription
- `POST /api/v1/subscriptions/:id/upgrade` - Upgrade subscription

**Key Test Cases:**
- Subscription tier management
- Subscription lifecycle
- Upgrade/downgrade functionality
- Billing integration points

### 5. Merchant Shop Tests (`merchant_shops_test.go`)

Tests merchant functionality:
- `POST /api/v1/merchant-shops` - Create merchant shop
- `GET /api/v1/merchant-shops` - List merchant shops
- `GET /api/v1/merchant-shops/:slug` - Get merchant shop
- `PUT /api/v1/merchant-shops/:slug` - Update merchant shop
- `DELETE /api/v1/merchant-shops/:slug` - Delete merchant shop

**Key Test Cases:**
- Shop creation and management
- Slug-based routing
- Owner authorization
- Data validation

## Test Utilities

### Test Helpers (`helpers.go`)

- `MakeRequest()` - Make HTTP requests to test router
- `WithAuth()` - Add JWT authentication to requests
- `WithAPIKey()` - Add API key authentication to requests
- `AssertSuccessResponse()` - Assert successful HTTP responses
- `AssertErrorResponse()` - Assert error responses
- `AssertJSONResponse()` - Assert JSON response structure

### Test Setup (`setup.go`)

- `SetupTestEnvironment()` - Initialize test environment
- `CleanTestData()` - Clean up test data between tests
- `CreateTestUser()` - Create test users
- `CreateTestAPIKey()` - Create test API keys
- `CreateTestSubscription()` - Create test subscriptions

## Best Practices

### Writing Tests

1. **Use Test Suites**: Organize related tests into test suites using testify/suite
2. **Clean Setup/Teardown**: Always clean up test data between tests
3. **Isolated Tests**: Each test should be independent and not rely on other tests
4. **Comprehensive Assertions**: Test both success and error cases
5. **Realistic Data**: Use realistic test data that matches production scenarios

### Test Data Management

1. **Automatic Cleanup**: Test framework automatically cleans up after each test
2. **Unique Identifiers**: Use UUIDs and unique names to avoid conflicts
3. **Minimal Data**: Create only the minimum data needed for each test
4. **Consistent Patterns**: Use helper functions for consistent test data creation

### Authentication Testing

1. **JWT Tokens**: Use `WithAuth()` helper for JWT authentication
2. **API Keys**: Use `WithAPIKey()` helper for API key authentication
3. **Authorization**: Test both authorized and unauthorized access
4. **Token Validation**: Test with invalid, expired, and malformed tokens

## Coverage Reports

Generate and view coverage reports:

```bash
# Generate coverage report
make test-coverage

# View HTML coverage report
open backend/coverage.html
```

The coverage report shows:
- Line-by-line code coverage
- Function coverage
- Package coverage statistics
- Uncovered code sections

## Continuous Integration

For CI/CD integration, add these commands to your pipeline:

```yaml
# Example GitHub Actions step
- name: Run Backend Tests
  run: |
    cd backend
    go mod tidy
    go test -v -coverprofile=coverage.out ./tests/...
    go tool cover -func=coverage.out
```

## Troubleshooting

### Common Issues

1. **Database Connection Errors**
   - Ensure PostgreSQL is running
   - Check TEST_DATABASE_URL environment variable
   - Verify database permissions

2. **Import Errors**
   - Run `go mod tidy` to update dependencies
   - Check Go module path in imports

3. **Test Failures**
   - Check test database is clean
   - Verify environment variables are set
   - Review test logs for specific errors

### Debug Mode

Enable debug mode for detailed test output:

```bash
cd backend
go test -v -debug ./tests/api/users_test.go
```

## Contributing

When adding new tests:

1. Follow existing test patterns and naming conventions
2. Add comprehensive test cases for both success and error scenarios
3. Update this README if adding new test suites
4. Ensure all tests pass before submitting PRs
5. Maintain test coverage above 80%
