"use client";

import { useSession } from "next-auth/react";
import { redirect, usePathname, useRouter } from "next/navigation";
import { ReactNode, useEffect } from "react";
import { Sidebar } from "@/components/layout/sidebar";
import { MobileNav } from "@/components/layout/mobile-nav";
import { Container } from "@/components/layout/container";
import Link from "next/link";
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Storefront, User } from "phosphor-react";
import { DashboardDrawer } from "@/components/navigation/dashboard-drawer";

export default function DashboardLayout({
  children,
}: {
  children: ReactNode;
}) {
  const { data: session, status } = useSession();
  const pathname = usePathname();
  const router = useRouter();

  // Determine if we're in merchant or customer mode
  const isMerchantMode = pathname.includes('/dashboard/merchant');
  const isCustomerMode = pathname.includes('/dashboard/customer');
  const isRootDashboard = pathname === '/dashboard';

  // If we're at the root dashboard, default to merchant mode
  useEffect(() => {
    if (isRootDashboard) {
      router.push('/dashboard/merchant');
    }
  }, [isRootDashboard, router]);

  useEffect(() => {
    if (status === "unauthenticated") {
      redirect("/auth/signin");
    }
  }, [status]);

  // Handle tab change
  const handleTabChange = (value: string) => {
    if (value === 'merchant') {
      router.push('/dashboard/merchant');
    } else if (value === 'customer') {
      router.push('/dashboard/customer');
    }
  };

  return (
    <div className="flex w-full">
      <div className="flex-1 min-h-screen">
        <div className="md:hidden flex items-center h-16 px-4 border-b">
          <MobileNav />
          <div className="ml-2 font-semibold">Dashboard</div>
        </div>

        {/* Mode Selector Tabs */}
        {(isMerchantMode || isCustomerMode) && (
          <div className="bg-[#fbfaf9] border-b border-[#f1edea] px-4 py-2">
            <Tabs
              value={isMerchantMode ? 'merchant' : 'customer'}
              onValueChange={handleTabChange}
              className="w-full max-w-md mx-auto"
            >
              <TabsList className="grid w-full grid-cols-2">
                <TabsTrigger
                  value="merchant"
                  className="data-[state=active]:bg-[#e5ccb2] data-[state=active]:text-[#181510] flex items-center gap-2"
                >
                  <Storefront className="h-4 w-4" />
                  <span>Merchant Mode</span>
                </TabsTrigger>
                <TabsTrigger
                  value="customer"
                  className="data-[state=active]:bg-[#e5ccb2] data-[state=active]:text-[#181510] flex items-center gap-2"
                >
                  <User className="h-4 w-4" />
                  <span>Customer Mode</span>
                </TabsTrigger>
              </TabsList>
            </Tabs>
          </div>
        )}

        <Container className="py-4 md:py-8">
          {children}
        </Container>

        {/* Drawer Navigation */}
        {(isMerchantMode || isCustomerMode) && <DashboardDrawer />}
      </div>
    </div>
  );
}
