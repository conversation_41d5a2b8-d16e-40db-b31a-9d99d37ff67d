package integration

import (
	"fmt"
	"testing"
	"time"

	"github.com/adc-credit/backend/internal/database"
	"github.com/adc-credit/backend/internal/models"
	"github.com/adc-credit/backend/internal/services"
	testutils "github.com/adc-credit/backend/internal/testing"
	"github.com/google/uuid"
	"github.com/stretchr/testify/suite"
)

type ComprehensiveSubscriptionGuardTestSuite struct {
	suite.Suite
	guard *services.SubscriptionGuard
}

func (suite *ComprehensiveSubscriptionGuardTestSuite) SetupSuite() {
	// Initialize test database
	err := testutils.SetupTestDB()
	suite.Require().NoError(err, "Failed to setup test database")

	// Initialize database connection
	err = database.InitDB()
	suite.Require().NoError(err, "Failed to initialize database")

	// Initialize subscription guard
	suite.guard = services.NewSubscriptionGuard()
}

func (suite *ComprehensiveSubscriptionGuardTestSuite) TearDownSuite() {
	testutils.CleanupTestDB()
}

func (suite *ComprehensiveSubscriptionGuardTestSuite) SetupTest() {
	testutils.CleanupAllTestData()
}

// Helper function to create subscription tiers with comprehensive limits
func (suite *ComprehensiveSubscriptionGuardTestSuite) createComprehensiveSubscriptionTiers() (starter, business, enterprise *models.SubscriptionTier) {
	db := database.DB

	starter = &models.SubscriptionTier{
		Name:                 "Starter",
		Description:          "Starter plan for comprehensive testing",
		Price:                0.00,
		CreditLimit:          1000,
		Features:             models.StringSlice{"Basic API access"},
		MaxShops:             1,
		MaxCustomersPerShop:  10,
		MaxAPIKeysPerShop:    1,
		MaxBranchesPerShop:   0,
		MaxQRCodesPerMonth:   100,
		MaxWebhooks:          0,
		AnalyticsHistoryDays: 7,
		SupportLevel:         "community",
		AllowedShopTypes:     models.StringSlice{"retail"},
		UnlimitedShops:       false,
		UnlimitedCustomers:   false,
		UnlimitedQRCodes:     false,
		UnlimitedBranches:    false,
	}

	business = &models.SubscriptionTier{
		Name:                 "Business",
		Description:          "Business plan for comprehensive testing",
		Price:                49.00,
		CreditLimit:          10000,
		Features:             models.StringSlice{"Full API access", "Multiple shops"},
		MaxShops:             3,
		MaxCustomersPerShop:  100,
		MaxAPIKeysPerShop:    3,
		MaxBranchesPerShop:   5,
		MaxQRCodesPerMonth:   1000,
		MaxWebhooks:          3,
		AnalyticsHistoryDays: 90,
		SupportLevel:         "email",
		AllowedShopTypes:     models.StringSlice{"retail", "api_service"},
		UnlimitedShops:       false,
		UnlimitedCustomers:   false,
		UnlimitedQRCodes:     false,
		UnlimitedBranches:    false,
	}

	enterprise = &models.SubscriptionTier{
		Name:                 "Enterprise",
		Description:          "Enterprise plan for comprehensive testing",
		Price:                199.00,
		CreditLimit:          100000,
		Features:             models.StringSlice{"Full API access", "Unlimited everything"},
		MaxShops:             0, // Unlimited
		MaxCustomersPerShop:  0, // Unlimited
		MaxAPIKeysPerShop:    10,
		MaxBranchesPerShop:   0, // Unlimited
		MaxQRCodesPerMonth:   0, // Unlimited
		MaxWebhooks:          10,
		AnalyticsHistoryDays: 0, // Unlimited
		SupportLevel:         "priority",
		AllowedShopTypes:     models.StringSlice{"retail", "api_service", "enterprise"},
		UnlimitedShops:       true,
		UnlimitedCustomers:   true,
		UnlimitedQRCodes:     true,
		UnlimitedBranches:    true,
	}

	suite.Require().NoError(db.Create(starter).Error)
	suite.Require().NoError(db.Create(business).Error)
	suite.Require().NoError(db.Create(enterprise).Error)

	// Fix the MaxWebhooks value for starter tier (GORM default overrides 0 values)
	suite.Require().NoError(db.Model(starter).Update("max_webhooks", 0).Error)

	return starter, business, enterprise
}

// Helper function to create user with subscription
func (suite *ComprehensiveSubscriptionGuardTestSuite) createUserWithSubscription(email, name string, tier *models.SubscriptionTier) (*models.User, *models.Subscription) {
	user, err := testutils.CreateTestUser(email, name)
	suite.Require().NoError(err)

	subscription := &models.Subscription{
		ID:                 uuid.New(),
		UserID:             user.ID,
		SubscriptionTierID: tier.ID,
		StartDate:          time.Now(),
		AutoRenew:          true,
		Status:             "active",
		CreditBalance:      tier.CreditLimit,
		SubscriptionType:   "personal",
	}

	suite.Require().NoError(database.DB.Create(subscription).Error)

	return user, subscription
}

// Helper function to create a merchant shop for testing
func (suite *ComprehensiveSubscriptionGuardTestSuite) createTestShop(name, slug string, ownerID uuid.UUID) (*models.MerchantShop, error) {
	return testutils.CreateTestMerchantShop(name, slug, ownerID)
}

// Test QR Code generation limits
func (suite *ComprehensiveSubscriptionGuardTestSuite) TestQRCodeLimits() {
	starter, business, enterprise := suite.createComprehensiveSubscriptionTiers()

	// Test Starter plan (100 QR codes per month)
	user1, _ := suite.createUserWithSubscription("<EMAIL>", "Starter User", starter)
	shop1, err := suite.createTestShop("Starter Shop", "starter-shop", user1.ID)
	suite.Require().NoError(err)

	// Check initial QR code limit
	canCreate, err := suite.guard.CheckQRCodeLimit(user1.ID)
	suite.Require().NoError(err)
	suite.True(canCreate.Allowed, "Should be able to create QR codes initially")
	suite.Equal(100, canCreate.Limit)
	suite.Equal(0, canCreate.CurrentUsage)

	// Create some QR codes (credit codes) to test usage tracking
	for i := 1; i <= 50; i++ {
		creditCode := &models.CreditCode{
			ID:          uuid.New(),
			ShopID:      shop1.ID,
			Code:        fmt.Sprintf("QR%03d", i),
			Amount:      100,
			Description: fmt.Sprintf("Test QR Code %d", i),
			IsRedeemed:  false,
		}
		suite.Require().NoError(database.DB.Create(creditCode).Error)
	}

	// Check usage after creating 50 QR codes
	canCreate, err = suite.guard.CheckQRCodeLimit(user1.ID)
	suite.Require().NoError(err)
	suite.True(canCreate.Allowed, "Should still be able to create more QR codes")
	suite.Equal(100, canCreate.Limit)
	suite.Equal(50, canCreate.CurrentUsage)

	// Test Business plan (1000 QR codes per month)
	user2, _ := suite.createUserWithSubscription("<EMAIL>", "Business User", business)

	canCreate, err = suite.guard.CheckQRCodeLimit(user2.ID)
	suite.Require().NoError(err)
	suite.True(canCreate.Allowed, "Business plan should allow QR codes")
	suite.Equal(1000, canCreate.Limit)
	suite.Equal(0, canCreate.CurrentUsage)

	// Test Enterprise plan (unlimited QR codes)
	user3, _ := suite.createUserWithSubscription("<EMAIL>", "Enterprise User", enterprise)

	canCreate, err = suite.guard.CheckQRCodeLimit(user3.ID)
	suite.Require().NoError(err)
	suite.True(canCreate.Allowed, "Enterprise plan should allow unlimited QR codes")
	suite.True(canCreate.Unlimited, "Should be marked as unlimited")

	// Cleanup
	testutils.CleanupTestUser(suite.T(), user1.ID)
	testutils.CleanupTestUser(suite.T(), user2.ID)
	testutils.CleanupTestUser(suite.T(), user3.ID)
}

// Test credit balance checks
func (suite *ComprehensiveSubscriptionGuardTestSuite) TestCreditBalanceChecks() {
	starter, business, _ := suite.createComprehensiveSubscriptionTiers()

	// Test Starter plan (1000 credits)
	user1, subscription1 := suite.createUserWithSubscription("<EMAIL>", "Starter User", starter)

	// Check sufficient credits
	canUse, err := suite.guard.CheckCreditBalance(user1.ID, 500)
	suite.Require().NoError(err)
	suite.True(canUse.Allowed, "Should have sufficient credits")
	suite.Equal(1000, canUse.CurrentUsage) // CurrentUsage represents available credits

	// Check insufficient credits
	canUse, err = suite.guard.CheckCreditBalance(user1.ID, 1500)
	suite.Require().NoError(err)
	suite.False(canUse.Allowed, "Should not have sufficient credits")

	// Update credit balance and test again
	suite.Require().NoError(database.DB.Model(subscription1).Update("credit_balance", 200).Error)

	canUse, err = suite.guard.CheckCreditBalance(user1.ID, 500)
	suite.Require().NoError(err)
	suite.False(canUse.Allowed, "Should not have sufficient credits after update")

	canUse, err = suite.guard.CheckCreditBalance(user1.ID, 100)
	suite.Require().NoError(err)
	suite.True(canUse.Allowed, "Should have sufficient credits for smaller amount")

	// Test Business plan (10000 credits)
	user2, _ := suite.createUserWithSubscription("<EMAIL>", "Business User", business)

	canUse, err = suite.guard.CheckCreditBalance(user2.ID, 5000)
	suite.Require().NoError(err)
	suite.True(canUse.Allowed, "Business plan should have sufficient credits")
	suite.Equal(10000, canUse.CurrentUsage)

	// Cleanup
	testutils.CleanupTestUser(suite.T(), user1.ID)
	testutils.CleanupTestUser(suite.T(), user2.ID)
}

// Test API key limits per shop (temporarily disabled due to Shop model foreign key issues)
func (suite *ComprehensiveSubscriptionGuardTestSuite) TestAPIKeyLimits() {
	// TODO: Re-enable this test once Shop model foreign key constraints are fixed
	suite.T().Skip("Skipping API key limits test due to Shop model foreign key issues")
}

// Test branch creation limits (temporarily disabled due to Shop model foreign key issues)
func (suite *ComprehensiveSubscriptionGuardTestSuite) TestBranchLimits() {
	// TODO: Re-enable this test once Shop model foreign key constraints are fixed
	suite.T().Skip("Skipping branch limits test due to Shop model foreign key issues")
}

// Test customer limits per shop using MerchantShop
func (suite *ComprehensiveSubscriptionGuardTestSuite) TestCustomerLimits() {
	starter, business, enterprise := suite.createComprehensiveSubscriptionTiers()

	// Test Starter plan (10 customers per shop)
	user1, _ := suite.createUserWithSubscription("<EMAIL>", "Starter User", starter)
	shop1, err := suite.createTestShop("Starter Shop", "starter-shop", user1.ID)
	suite.Require().NoError(err)

	// Note: CheckCustomerLimit expects Shop model, but we can test the logic manually
	// For now, let's create customers and verify the count logic works

	// Create 5 customers with real users
	for i := 1; i <= 5; i++ {
		// Create a real user for each customer
		customerUser, err := testutils.CreateTestUser(fmt.Sprintf("<EMAIL>", i), fmt.Sprintf("Customer %d", i))
		suite.Require().NoError(err)

		customer := &models.ShopCustomer{
			ID:            uuid.New(),
			ShopID:        shop1.ID,
			UserID:        customerUser.ID,
			CreditBalance: 0,
		}
		suite.Require().NoError(database.DB.Create(customer).Error)
	}

	// Manually verify customer count
	var customerCount int64
	err = database.DB.Model(&models.ShopCustomer{}).Where("shop_id = ?", shop1.ID).Count(&customerCount).Error
	suite.Require().NoError(err)
	suite.Equal(int64(5), customerCount, "Should have 5 customers")

	// Test Business plan (100 customers per shop)
	user2, _ := suite.createUserWithSubscription("<EMAIL>", "Business User", business)
	shop2, err := suite.createTestShop("Business Shop", "business-shop", user2.ID)
	suite.Require().NoError(err)

	// Create 50 customers for business shop with real users
	for i := 1; i <= 50; i++ {
		// Create a real user for each customer
		customerUser, err := testutils.CreateTestUser(fmt.Sprintf("<EMAIL>", i), fmt.Sprintf("Business Customer %d", i))
		suite.Require().NoError(err)

		customer := &models.ShopCustomer{
			ID:            uuid.New(),
			ShopID:        shop2.ID,
			UserID:        customerUser.ID,
			CreditBalance: 0,
		}
		suite.Require().NoError(database.DB.Create(customer).Error)
	}

	// Verify customer count for business shop
	err = database.DB.Model(&models.ShopCustomer{}).Where("shop_id = ?", shop2.ID).Count(&customerCount).Error
	suite.Require().NoError(err)
	suite.Equal(int64(50), customerCount, "Should have 50 customers")

	// Test Enterprise plan (unlimited customers)
	user3, _ := suite.createUserWithSubscription("<EMAIL>", "Enterprise User", enterprise)
	shop3, err := suite.createTestShop("Enterprise Shop", "enterprise-shop", user3.ID)
	suite.Require().NoError(err)

	// Create 200 customers for enterprise shop (more than business limit) with real users
	for i := 1; i <= 200; i++ {
		// Create a real user for each customer
		customerUser, err := testutils.CreateTestUser(fmt.Sprintf("<EMAIL>", i), fmt.Sprintf("Enterprise Customer %d", i))
		suite.Require().NoError(err)

		customer := &models.ShopCustomer{
			ID:            uuid.New(),
			ShopID:        shop3.ID,
			UserID:        customerUser.ID,
			CreditBalance: 0,
		}
		suite.Require().NoError(database.DB.Create(customer).Error)
	}

	// Verify customer count for enterprise shop
	err = database.DB.Model(&models.ShopCustomer{}).Where("shop_id = ?", shop3.ID).Count(&customerCount).Error
	suite.Require().NoError(err)
	suite.Equal(int64(200), customerCount, "Should have 200 customers")

	// Cleanup
	testutils.CleanupTestUser(suite.T(), user1.ID)
	testutils.CleanupTestUser(suite.T(), user2.ID)
	testutils.CleanupTestUser(suite.T(), user3.ID)
}

// Test unlimited features for enterprise plan
func (suite *ComprehensiveSubscriptionGuardTestSuite) TestUnlimitedFeatures() {
	_, _, enterprise := suite.createComprehensiveSubscriptionTiers()

	// Test Enterprise plan unlimited features
	user, _ := suite.createUserWithSubscription("<EMAIL>", "Enterprise User", enterprise)

	// Test unlimited QR codes
	canCreate, err := suite.guard.CheckQRCodeLimit(user.ID)
	suite.Require().NoError(err)
	suite.True(canCreate.Allowed, "Enterprise plan should allow QR codes")
	suite.True(canCreate.Unlimited, "Should be marked as unlimited")

	// Test credit balance (should have high limit)
	canUse, err := suite.guard.CheckCreditBalance(user.ID, 50000)
	suite.Require().NoError(err)
	suite.True(canUse.Allowed, "Enterprise plan should have high credit balance")
	suite.Equal(100000, canUse.CurrentUsage) // Enterprise tier has 100k credits

	// Test shop type restrictions (should allow all types)
	canCreateRetail, err := suite.guard.CheckShopTypeAllowed(user.ID, "retail")
	suite.Require().NoError(err)
	suite.True(canCreateRetail.Allowed, "Enterprise should allow retail shops")

	canCreateAPI, err := suite.guard.CheckShopTypeAllowed(user.ID, "api_service")
	suite.Require().NoError(err)
	suite.True(canCreateAPI.Allowed, "Enterprise should allow api_service shops")

	canCreateEnterprise, err := suite.guard.CheckShopTypeAllowed(user.ID, "enterprise")
	suite.Require().NoError(err)
	suite.True(canCreateEnterprise.Allowed, "Enterprise should allow enterprise shops")

	// Cleanup
	testutils.CleanupTestUser(suite.T(), user.ID)
}

// Test subscription tier feature restrictions
func (suite *ComprehensiveSubscriptionGuardTestSuite) TestFeatureRestrictions() {
	starter, business, _ := suite.createComprehensiveSubscriptionTiers()

	// Test Starter plan restrictions
	user1, _ := suite.createUserWithSubscription("<EMAIL>", "Starter User", starter)

	// Should not allow api_service shops
	canCreate, err := suite.guard.CheckShopTypeAllowed(user1.ID, "api_service")
	suite.Require().NoError(err)
	suite.False(canCreate.Allowed, "Starter plan should not allow api_service shops")

	// Should not allow enterprise shops
	canCreate, err = suite.guard.CheckShopTypeAllowed(user1.ID, "enterprise")
	suite.Require().NoError(err)
	suite.False(canCreate.Allowed, "Starter plan should not allow enterprise shops")

	// Should allow retail shops
	canCreate, err = suite.guard.CheckShopTypeAllowed(user1.ID, "retail")
	suite.Require().NoError(err)
	suite.True(canCreate.Allowed, "Starter plan should allow retail shops")

	// Test Business plan restrictions
	user2, _ := suite.createUserWithSubscription("<EMAIL>", "Business User", business)

	// Should allow api_service shops
	canCreate, err = suite.guard.CheckShopTypeAllowed(user2.ID, "api_service")
	suite.Require().NoError(err)
	suite.True(canCreate.Allowed, "Business plan should allow api_service shops")

	// Should not allow enterprise shops
	canCreate, err = suite.guard.CheckShopTypeAllowed(user2.ID, "enterprise")
	suite.Require().NoError(err)
	suite.False(canCreate.Allowed, "Business plan should not allow enterprise shops")

	// Cleanup
	testutils.CleanupTestUser(suite.T(), user1.ID)
	testutils.CleanupTestUser(suite.T(), user2.ID)
}

func TestComprehensiveSubscriptionGuardTestSuite(t *testing.T) {
	suite.Run(t, new(ComprehensiveSubscriptionGuardTestSuite))
}
