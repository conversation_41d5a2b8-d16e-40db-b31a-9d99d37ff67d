"use client";

import { useState } from "react";
import { useSession } from "next-auth/react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { useToast } from "@/components/ui/use-toast";
import { useRouter } from "next/navigation";
import { Plus, Storefront, CurrencyDollar, User, House, QrCode, Copy, ArrowUp, ArrowDown } from "phosphor-react";
import Link from "next/link";
import {
  useGetMerchantShopsQuery,
  useGetCreditCodesQuery,
  useGenerateCreditCodeMutation,
  useGetMerchantCreditStatsQuery
} from "@/lib/api/apiSlice";
import { MerchantShop, CreditCode } from "@/types";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Skeleton } from "@/components/ui/skeleton";
import { Textarea } from "@/components/ui/textarea";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";

export default function MerchantCreditsPage() {
  const { data: session } = useSession();
  const { data: shops, isLoading: isShopsLoading } = useGetMerchantShopsQuery();

  const [selectedShopId, setSelectedShopId] = useState<string>("");
  const { data: creditCodes, isLoading: isCodesLoading, refetch: refetchCodes } =
    useGetCreditCodesQuery(selectedShopId, { skip: !selectedShopId });

  const {
    data: creditStats,
    isLoading: isStatsLoading
  } = useGetMerchantCreditStatsQuery();

  const [generateCode] = useGenerateCreditCodeMutation();
  const [isCodeDialogOpen, setIsCodeDialogOpen] = useState(false);
  const [codeData, setCodeData] = useState({ amount: 0, description: "", expiresIn: 30 });

  const { toast } = useToast();
  const router = useRouter();

  // Get credit statistics from API
  const totalCreditsIssued = creditStats?.total_credits_issued || 0;
  const totalCreditsRedeemed = creditStats?.total_credits_redeemed || 0;

  const handleGenerateCode = async () => {
    if (!selectedShopId) {
      toast({
        title: "Error",
        description: "Please select a shop first.",
        variant: "destructive"
      });
      return;
    }

    try {
      await generateCode({
        shopId: selectedShopId,
        ...codeData
      }).unwrap();
      toast({
        title: "Code generated",
        description: "Credit code has been generated successfully."
      });
      setIsCodeDialogOpen(false);
      setCodeData({ amount: 0, description: "", expiresIn: 30 });
      refetchCodes();
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to generate code. Please try again.",
        variant: "destructive"
      });
    }
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    toast({
      title: "Copied",
      description: "Code copied to clipboard."
    });
  };

  const handleShopChange = (shopId: string) => {
    setSelectedShopId(shopId);
  };

  return (
    <div className="relative flex size-full min-h-screen flex-col bg-[#fbfaf9] justify-between group/design-root overflow-x-hidden">
      <div>
        <div className="flex items-center bg-[#fbfaf9] p-4 pb-2 justify-between">
          <h2 className="text-[#181510] text-lg font-bold leading-tight tracking-[-0.015em] flex-1 text-center pl-12">Credits</h2>
          <div className="flex w-12 items-center justify-end">
            <Button
              className="flex max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-lg h-12 bg-transparent text-[#181510] gap-2 text-base font-bold leading-normal tracking-[0.015em] min-w-0 p-0"
              variant="ghost"
              onClick={() => setIsCodeDialogOpen(true)}
            >
              <Plus size={24} weight="regular" className="text-[#181510]" />
            </Button>
          </div>
        </div>

        <div className="flex flex-wrap gap-4 p-4">
          <div className="flex min-w-[158px] flex-1 flex-col gap-2 rounded-lg p-6 bg-[#f1edea]">
            <p className="text-[#181510] text-base font-medium leading-normal">Total Credits Issued</p>
            {isStatsLoading ? (
              <Skeleton className="h-8 w-32 bg-gray-200" />
            ) : (
              <p className="text-[#181510] tracking-light text-2xl font-bold leading-tight">${totalCreditsIssued.toLocaleString()}</p>
            )}
          </div>
          <div className="flex min-w-[158px] flex-1 flex-col gap-2 rounded-lg p-6 bg-[#f1edea]">
            <p className="text-[#181510] text-base font-medium leading-normal">Total Credits Redeemed</p>
            {isStatsLoading ? (
              <Skeleton className="h-8 w-32 bg-gray-200" />
            ) : (
              <p className="text-[#181510] tracking-light text-2xl font-bold leading-tight">${totalCreditsRedeemed.toLocaleString()}</p>
            )}
          </div>
        </div>

        <div className="p-4">
          <Card>
            <CardHeader>
              <CardTitle>Credit Management</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="grid gap-2">
                  <Label htmlFor="shop-select">Select Shop</Label>
                  <Select value={selectedShopId} onValueChange={handleShopChange}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select a shop" />
                    </SelectTrigger>
                    <SelectContent>
                      {isShopsLoading ? (
                        <SelectItem value="loading" disabled>Loading shops...</SelectItem>
                      ) : shops && shops.length > 0 ? (
                        shops.map((shop: MerchantShop) => (
                          <SelectItem key={shop.id} value={shop.id}>
                            {shop.name}
                          </SelectItem>
                        ))
                      ) : (
                        <SelectItem value="none" disabled>No shops available</SelectItem>
                      )}
                    </SelectContent>
                  </Select>
                </div>

                {selectedShopId && (
                  <Tabs defaultValue="codes" className="w-full">
                    <TabsList className="grid w-full grid-cols-2">
                      <TabsTrigger value="codes">Credit Codes</TabsTrigger>
                      <TabsTrigger value="transactions">Transactions</TabsTrigger>
                    </TabsList>

                    <TabsContent value="codes" className="space-y-4 pt-4">
                      <div className="flex justify-between items-center">
                        <h3 className="text-lg font-medium">Credit Codes</h3>
                        <Button onClick={() => setIsCodeDialogOpen(true)}>Generate Code</Button>
                      </div>

                      {isCodesLoading ? (
                        <div>Loading credit codes...</div>
                      ) : creditCodes && creditCodes.length > 0 ? (
                        <Table>
                          <TableHeader>
                            <TableRow>
                              <TableHead>Code</TableHead>
                              <TableHead>Amount</TableHead>
                              <TableHead>Status</TableHead>
                              <TableHead>Expires</TableHead>
                              <TableHead>Actions</TableHead>
                            </TableRow>
                          </TableHeader>
                          <TableBody>
                            {creditCodes.map((code: CreditCode) => (
                              <TableRow key={code.id}>
                                <TableCell>{code.code}</TableCell>
                                <TableCell>${code.amount}</TableCell>
                                <TableCell>{code.is_used ? "Used" : "Available"}</TableCell>
                                <TableCell>{code.expires_at ? new Date(code.expires_at).toLocaleDateString() : "Never"}</TableCell>
                                <TableCell>
                                  <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={() => copyToClipboard(code.code)}
                                    disabled={code.is_used}
                                  >
                                    <Copy size={16} className="mr-2" /> Copy
                                  </Button>
                                </TableCell>
                              </TableRow>
                            ))}
                          </TableBody>
                        </Table>
                      ) : (
                        <div className="text-center p-4">
                          <p className="mb-4">No credit codes generated yet.</p>
                          <Button onClick={() => setIsCodeDialogOpen(true)}>Generate Your First Code</Button>
                        </div>
                      )}
                    </TabsContent>

                    <TabsContent value="transactions" className="space-y-4 pt-4">
                      <div className="flex justify-between items-center">
                        <h3 className="text-lg font-medium">Credit Transactions</h3>
                      </div>

                      <Table>
                        <TableHeader>
                          <TableRow>
                            <TableHead>Date</TableHead>
                            <TableHead>Customer</TableHead>
                            <TableHead>Type</TableHead>
                            <TableHead>Amount</TableHead>
                            <TableHead>Description</TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          <TableRow>
                            <TableCell>2023-06-15</TableCell>
                            <TableCell>John Doe</TableCell>
                            <TableCell className="flex items-center text-green-600">
                              <ArrowUp size={16} className="mr-1" /> Added
                            </TableCell>
                            <TableCell>$50</TableCell>
                            <TableCell>Monthly credit</TableCell>
                          </TableRow>
                          <TableRow>
                            <TableCell>2023-06-10</TableCell>
                            <TableCell>Jane Smith</TableCell>
                            <TableCell className="flex items-center text-red-600">
                              <ArrowDown size={16} className="mr-1" /> Used
                            </TableCell>
                            <TableCell>$25</TableCell>
                            <TableCell>Product purchase</TableCell>
                          </TableRow>
                          <TableRow>
                            <TableCell>2023-06-05</TableCell>
                            <TableCell>Jane Smith</TableCell>
                            <TableCell className="flex items-center text-green-600">
                              <ArrowUp size={16} className="mr-1" /> Added
                            </TableCell>
                            <TableCell>$100</TableCell>
                            <TableCell>Welcome bonus</TableCell>
                          </TableRow>
                        </TableBody>
                      </Table>
                    </TabsContent>
                  </Tabs>
                )}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Generate Code Dialog */}
      <Dialog open={isCodeDialogOpen} onOpenChange={setIsCodeDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Generate Credit Code</DialogTitle>
            <DialogDescription>Create a new credit code for your shop.</DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            {!selectedShopId && (
              <div className="grid gap-2">
                <Label htmlFor="dialogShopSelect">Select Shop</Label>
                <Select value={selectedShopId} onValueChange={setSelectedShopId}>
                  <SelectTrigger id="dialogShopSelect">
                    <SelectValue placeholder="Select a shop" />
                  </SelectTrigger>
                  <SelectContent>
                    {isShopsLoading ? (
                      <SelectItem value="loading" disabled>Loading shops...</SelectItem>
                    ) : shops && shops.length > 0 ? (
                      shops.map((shop: MerchantShop) => (
                        <SelectItem key={shop.id} value={shop.id}>
                          {shop.name}
                        </SelectItem>
                      ))
                    ) : (
                      <SelectItem value="none" disabled>No shops available</SelectItem>
                    )}
                  </SelectContent>
                </Select>
              </div>
            )}
            <div className="grid gap-2">
              <Label htmlFor="codeAmount">Amount</Label>
              <Input
                id="codeAmount"
                type="number"
                value={codeData.amount || ""}
                onChange={(e) => setCodeData({ ...codeData, amount: parseInt(e.target.value) })}
              />
            </div>
            <div className="grid gap-2">
              <Label htmlFor="codeDescription">Description (Optional)</Label>
              <Input
                id="codeDescription"
                value={codeData.description}
                onChange={(e) => setCodeData({ ...codeData, description: e.target.value })}
              />
            </div>
            <div className="grid gap-2">
              <Label htmlFor="codeExpires">Expires In (Days, 0 for never)</Label>
              <Input
                id="codeExpires"
                type="number"
                value={codeData.expiresIn || ""}
                onChange={(e) => setCodeData({ ...codeData, expiresIn: parseInt(e.target.value) })}
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsCodeDialogOpen(false)}>
              Cancel
            </Button>
            <Button
              onClick={handleGenerateCode}
              disabled={!selectedShopId || codeData.amount <= 0}
            >
              Generate Code
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      <div>
        <div className="flex gap-2 border-t border-[#f1edea] bg-[#fbfaf9] px-4 pb-3 pt-2">
          <Link className="just flex flex-1 flex-col items-center justify-end gap-1 text-[#8a745c]" href="/dashboard/merchant">
            <div className="text-[#8a745c] flex h-8 items-center justify-center">
              <House size={24} weight="regular" />
            </div>
            <p className="text-[#8a745c] text-xs font-medium leading-normal tracking-[0.015em]">Home</p>
          </Link>
          <Link className="just flex flex-1 flex-col items-center justify-end gap-1 rounded-full text-[#181510]" href="/dashboard/merchant/credits">
            <div className="text-[#181510] flex h-8 items-center justify-center">
              <CurrencyDollar size={24} weight="fill" />
            </div>
            <p className="text-[#181510] text-xs font-medium leading-normal tracking-[0.015em]">Credits</p>
          </Link>
          <Link className="just flex flex-1 flex-col items-center justify-end gap-1 text-[#8a745c]" href="/dashboard/merchant/codes">
            <div className="text-[#8a745c] flex h-8 items-center justify-center">
              <QrCode size={24} weight="regular" />
            </div>
            <p className="text-[#8a745c] text-xs font-medium leading-normal tracking-[0.015em]">Codes</p>
          </Link>
          <Link className="just flex flex-1 flex-col items-center justify-end gap-1 text-[#8a745c]" href="/dashboard/merchant/profile">
            <div className="text-[#8a745c] flex h-8 items-center justify-center">
              <User size={24} weight="regular" />
            </div>
            <p className="text-[#8a745c] text-xs font-medium leading-normal tracking-[0.015em]">Profile</p>
          </Link>
        </div>
        <div className="h-5 bg-[#fbfaf9]"></div>
      </div>
    </div>
  );
}
