/**
 * ADC Credit SDK Organizations Module
 */
import { ApiClient } from './client';
import { APIResponse } from './types';
import { User } from './users';

/**
 * Organization object returned from the API
 */
export interface Organization {
  id: string;
  name: string;
  slug: string;
  description: string;
  owner_user_id: string;
  branches?: Branch[];
  created_at: string;
  updated_at: string;
}

/**
 * Branch object returned from the API
 */
export interface Branch {
  id: string;
  organization_id: string;
  name: string;
  description: string;
  users?: User[];
  created_at: string;
  updated_at: string;
}

/**
 * Create organization request
 */
export interface CreateOrganizationRequest {
  name: string;
  description?: string;
}

/**
 * Update organization request
 */
export interface UpdateOrganizationRequest {
  name?: string;
  description?: string;
}

/**
 * Create branch request
 */
export interface CreateBranchRequest {
  organization_id: string;
  name: string;
  description?: string;
}

/**
 * Update branch request
 */
export interface UpdateBranchRequest {
  name?: string;
  description?: string;
}

/**
 * Organizations module for the ADC Credit SDK
 */
export class Organizations {
  private client: ApiClient;

  /**
   * Creates a new Organizations instance
   * @param client API client
   */
  constructor(client: ApiClient) {
    this.client = client;
  }

  /**
   * Gets all organizations
   * @returns List of organizations
   */
  public async getAll(): Promise<APIResponse<Organization[]>> {
    return this.client.get<Organization[]>('/organizations');
  }

  /**
   * Gets a specific organization by slug
   * @param slug Organization slug
   * @returns Organization
   */
  public async getBySlug(slug: string): Promise<APIResponse<Organization>> {
    return this.client.get<Organization>(`/organizations/${slug}`);
  }

  /**
   * Gets a specific organization by ID (legacy)
   * @param id Organization ID
   * @returns Organization
   */
  public async getById(id: string): Promise<APIResponse<Organization>> {
    return this.client.get<Organization>(`/organizations/id/${id}`);
  }

  /**
   * Creates a new organization
   * @param data Organization data
   * @returns Created organization
   */
  public async create(data: CreateOrganizationRequest): Promise<APIResponse<Organization>> {
    return this.client.post<Organization>('/organizations', data);
  }

  /**
   * Updates an organization
   * @param slug Organization slug
   * @param data Organization data to update
   * @returns Updated organization
   */
  public async update(slug: string, data: UpdateOrganizationRequest): Promise<APIResponse<Organization>> {
    return this.client.put<Organization>(`/organizations/${slug}`, data);
  }

  /**
   * Deletes an organization
   * @param slug Organization slug
   * @returns Success message
   */
  public async delete(slug: string): Promise<APIResponse<{ message: string }>> {
    return this.client.delete<{ message: string }>(`/organizations/${slug}`);
  }

  /**
   * Gets all branches
   * @param organizationId Organization ID (optional)
   * @returns List of branches
   */
  public async getBranches(organizationId?: string): Promise<APIResponse<Branch[]>> {
    const queryParams = organizationId ? `?organization_id=${organizationId}` : '';
    return this.client.get<Branch[]>(`/org-branches${queryParams}`);
  }

  /**
   * Gets a specific branch
   * @param id Branch ID
   * @returns Branch
   */
  public async getBranch(id: string): Promise<APIResponse<Branch>> {
    return this.client.get<Branch>(`/org-branches/${id}`);
  }

  /**
   * Creates a new branch
   * @param data Branch data
   * @returns Created branch
   */
  public async createBranch(data: CreateBranchRequest): Promise<APIResponse<Branch>> {
    return this.client.post<Branch>('/org-branches', data);
  }

  /**
   * Updates a branch
   * @param id Branch ID
   * @param data Branch data to update
   * @returns Updated branch
   */
  public async updateBranch(id: string, data: UpdateBranchRequest): Promise<APIResponse<Branch>> {
    return this.client.put<Branch>(`/org-branches/${id}`, data);
  }

  /**
   * Deletes a branch
   * @param id Branch ID
   * @returns Success message
   */
  public async deleteBranch(id: string): Promise<APIResponse<{ message: string }>> {
    return this.client.delete<{ message: string }>(`/org-branches/${id}`);
  }
}
