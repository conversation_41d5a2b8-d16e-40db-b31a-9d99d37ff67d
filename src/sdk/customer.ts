/**
 * ADC Credit SDK Customer Module
 */
import { ApiClient } from './client';
import { APIResponse } from './types';
import { MerchantShop, ShopCreditTransaction } from '@/types';

/**
 * Shop with credit balance
 */
export interface ShopWithCredit {
  id: string;
  slug: string;
  name: string;
  description: string;
  contact_email: string;
  contact_phone: string;
  owner_user_id: string;
  credit_balance: number;
  created_at: string;
  updated_at: string;
}

/**
 * Redeem credit code request
 */
export interface RedeemCreditCodeRequest {
  code: string;
}

/**
 * Use shop credit request
 */
export interface UseShopCreditRequest {
  amount: number;
  description?: string;
}

/**
 * Customer module for the ADC Credit SDK
 */
export class Customer {
  private client: ApiClient;

  /**
   * Creates a new Customer instance
   * @param client API client
   */
  constructor(client: ApiClient) {
    this.client = client;
  }

  /**
   * Gets all shops where the current user is a customer
   * @returns List of shops with credit balance
   */
  public async getShops(): Promise<APIResponse<ShopWithCredit[]>> {
    return this.client.get<ShopWithCredit[]>('/customer/shops');
  }

  /**
   * Gets a specific shop by ID
   * @param id Shop ID
   * @returns Shop details with credit balance
   */
  public async getShop(id: string): Promise<APIResponse<ShopWithCredit>> {
    return this.client.get<ShopWithCredit>(`/customer/shops/${id}`);
  }

  /**
   * Gets a specific shop by slug
   * @param slug Shop slug
   * @returns Shop details with credit balance
   */
  public async getShopBySlug(slug: string): Promise<APIResponse<ShopWithCredit>> {
    return this.client.get<ShopWithCredit>(`/customer/shops/slug/${slug}`);
  }

  /**
   * Redeems a credit code
   * @param data Credit code data
   * @returns Result of redeeming the code
   */
  public async redeemCreditCode(data: RedeemCreditCodeRequest): Promise<APIResponse<{
    shop: MerchantShop;
    credit_balance: number;
    amount: number;
    message: string;
  }>> {
    return this.client.post<{
      shop: MerchantShop;
      credit_balance: number;
      amount: number;
      message: string;
    }>('/customer/redeem', data);
  }

  /**
   * Uses credits from a shop balance
   * @param shopId Shop ID
   * @param data Credit usage data
   * @returns Updated credit balance
   */
  public async useShopCredit(shopId: string, data: UseShopCreditRequest): Promise<APIResponse<{
    credit_balance: number;
    message: string;
  }>> {
    return this.client.post<{
      credit_balance: number;
      message: string;
    }>(`/customer/shops/${shopId}/use`, data);
  }

  /**
   * Gets credit transaction history for a shop
   * @param shopId Shop ID
   * @returns List of credit transactions
   */
  public async getCreditTransactions(shopId: string): Promise<APIResponse<ShopCreditTransaction[]>> {
    return this.client.get<ShopCreditTransaction[]>(`/customer/shops/${shopId}/transactions`);
  }

  /**
   * Creates an API key for a customer to use with a specific shop
   * @param shopId Shop ID
   * @param name API key name
   * @param permissions Optional permissions
   * @returns Created API key
   */
  public async createShopApiKey(
    shopId: string, 
    name: string, 
    permissions?: string[]
  ): Promise<APIResponse<{ id: string; key: string; name: string }>> {
    return this.client.post<{ id: string; key: string; name: string }>(
      `/customer/shops/${shopId}/apikeys`, 
      { name, permissions }
    );
  }

  /**
   * Gets all API keys for a customer's shop access
   * @param shopId Shop ID
   * @returns List of API keys
   */
  public async getShopApiKeys(shopId: string): Promise<APIResponse<{ id: string; name: string; last_used?: string }[]>> {
    return this.client.get<{ id: string; name: string; last_used?: string }[]>(`/customer/shops/${shopId}/apikeys`);
  }

  /**
   * Deletes an API key for a customer's shop access
   * @param shopId Shop ID
   * @param keyId API key ID
   * @returns Success message
   */
  public async deleteShopApiKey(shopId: string, keyId: string): Promise<APIResponse<{ message: string }>> {
    return this.client.delete<{ message: string }>(`/customer/shops/${shopId}/apikeys/${keyId}`);
  }
}
