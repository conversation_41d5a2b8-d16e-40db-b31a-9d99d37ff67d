package main

import (
	"log"
	"time"

	"github.com/adc-credit/backend/internal/database"
	"github.com/adc-credit/backend/internal/models"
	"github.com/google/uuid"
	"github.com/joho/godotenv"
)

func main() {
	// Load environment variables
	if err := godotenv.Load(); err != nil {
		log.Println("No .env file found, using environment variables")
	}

	// Initialize database connection
	if err := database.InitDB(); err != nil {
		log.Fatalf("Failed to connect to database: %v", err)
	}

	// Migrate existing data to new organization structure
	if err := migrateToOrganizationStructure(); err != nil {
		log.Fatalf("Failed to migrate to organization structure: %v", err)
	}

	log.Println("Migration to organization structure completed successfully!")
}

func migrateToOrganizationStructure() error {
	// Create a default organization for existing users
	defaultOrg := models.Organization{
		ID:          uuid.New(),
		Name:        "Default Organization",
		Description: "Default organization created during migration",
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
	}

	// Create a default branch for the default organization
	defaultBranch := models.Branch{
		ID:             uuid.New(),
		OrganizationID: defaultOrg.ID,
		Name:           "Default Branch",
		Description:    "Default branch created during migration",
		CreatedAt:      time.Now(),
		UpdatedAt:      time.Now(),
	}

	// Begin transaction
	tx := database.DB.Begin()
	if tx.Error != nil {
		return tx.Error
	}

	// Create default organization
	if err := tx.Create(&defaultOrg).Error; err != nil {
		tx.Rollback()
		return err
	}

	// Create default branch
	if err := tx.Create(&defaultBranch).Error; err != nil {
		tx.Rollback()
		return err
	}

	// Create a test user if no users exist
	var userCount int64
	if err := tx.Model(&models.User{}).Count(&userCount).Error; err != nil {
		tx.Rollback()
		return err
	}

	var adminUser models.User
	if userCount == 0 {
		// Create a test user
		adminUser = models.User{
			ID:       uuid.New(),
			Email:    "<EMAIL>",
			Name:     "Admin User",
			Password: "password", // In a real app, this would be hashed
			Role:     "admin",
		}
		if err := tx.Create(&adminUser).Error; err != nil {
			tx.Rollback()
			return err
		}
		log.Println("Created admin user:", adminUser.ID)
	} else {
		// Find the first admin user to set as organization owner
		if err := tx.Where("role = ?", "admin").First(&adminUser).Error; err != nil {
			// If no admin user found, use the first user
			if err := tx.First(&adminUser).Error; err != nil {
				tx.Rollback()
				return err
			}
		}
	}

	// Set the owner of the default organization
	defaultOrg.OwnerUserID = adminUser.ID
	if err := tx.Save(&defaultOrg).Error; err != nil {
		tx.Rollback()
		return err
	}

	// Update all existing users to belong to the default organization and branch
	if err := tx.Exec("UPDATE users SET organization_id = ?, branch_id = ? WHERE id != ?",
		defaultOrg.ID, defaultBranch.ID, adminUser.ID).Error; err != nil {
		tx.Rollback()
		return err
	}

	// Update admin user separately
	adminUser.OrganizationID = &defaultOrg.ID
	adminUser.BranchID = &defaultBranch.ID
	if err := tx.Save(&adminUser).Error; err != nil {
		tx.Rollback()
		return err
	}

	// Check if transactions table exists and has any records
	var transactionCount int64
	if err := tx.Model(&models.Transaction{}).Count(&transactionCount).Error; err == nil && transactionCount > 0 {
		// Update all existing transactions to include organization and branch IDs
		if err := tx.Exec("UPDATE transactions SET organization_id = ?, branch_id = ?",
			defaultOrg.ID, defaultBranch.ID).Error; err != nil {
			tx.Rollback()
			log.Printf("Error updating transactions: %v", err)
			// Continue even if this fails
		}
	}

	// Commit transaction
	return tx.Commit().Error
}
