"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import Image from "next/image";
import Link from "next/link";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, <PERSON><PERSON>ooter, <PERSON><PERSON>eader, CardTitle } from "@/components/ui/card";
import { useToast } from "@/components/ui/use-toast";
import { ArrowLeft, Copy, Download, Share } from "phosphor-react";
import { useGetCreditCodesQuery, useGenerateQRCodeMutation } from "@/lib/api/apiSlice";

interface CreditCodeDetailClientProps {
  shopId: string;
  codeId: string;
}

export default function CreditCodeDetailClient({ shopId, codeId }: CreditCodeDetailClientProps) {
  const router = useRouter();
  const { toast } = useToast();
  const { data: creditCodes, isLoading } = useGetCreditCodesQuery(shopId);
  const [generateQRCode] = useGenerateQRCodeMutation();
  const [isDownloading, setIsDownloading] = useState(false);
  
  // Find the specific credit code
  const creditCode = creditCodes?.find(code => code.id === codeId);
  
  // QR code URL state
  const [qrCodeUrl, setQrCodeUrl] = useState<string | null>(null);
  
  useEffect(() => {
    async function fetchQRCode() {
      if (creditCode) {
        try {
          // Try to get QR code from backend
          const result = await generateQRCode({
            shopId: shopId,
            code: creditCode.code,
            amount: creditCode.amount
          }).unwrap();
          
          setQrCodeUrl(result.qr_code);
        } catch (error) {
          console.error("Failed to generate QR code from backend:", error);
          
          // Fallback to external service if backend fails
          const fallbackQrUrl = `https://api.qrserver.com/v1/create-qr-code/?size=256x256&data=shop:${shopId};code:${creditCode.code};amount:${creditCode.amount}`;
          setQrCodeUrl(fallbackQrUrl);
        }
      }
    }
    
    fetchQRCode();
  }, [creditCode, shopId, generateQRCode]);

  const copyToClipboard = () => {
    if (!creditCode) return;
    
    navigator.clipboard.writeText(creditCode.code).then(() => {
      toast({
        title: "Code copied!",
        description: "The credit code has been copied to your clipboard.",
      });
    });
  };

  const downloadQRCode = () => {
    if (!qrCodeUrl) return;
    
    setIsDownloading(true);
    
    const link = document.createElement('a');
    link.href = qrCodeUrl;
    link.download = `credit-code-${creditCode?.code || 'download'}.png`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    setTimeout(() => {
      setIsDownloading(false);
    }, 1000);
  };

  const shareCode = async () => {
    if (!creditCode) return;
    
    if (navigator.share) {
      try {
        await navigator.share({
          title: `Credit Code: $${creditCode.amount}`,
          text: `Here's your credit code: ${creditCode.code}`,
          url: window.location.href,
        });
        
        toast({
          title: "Shared successfully!",
          description: "The credit code has been shared.",
        });
      } catch (error) {
        console.error("Error sharing:", error);
      }
    } else {
      copyToClipboard();
    }
  };

  // Loading state with skeleton UI
  if (isLoading) {
    return (
      <div className="relative flex size-full min-h-screen flex-col bg-[#fbfaf9] justify-between overflow-x-hidden">
        <div className="flex items-center bg-[#fbfaf9] p-4 pb-2 justify-between">
          <div className="size-12 shrink-0"></div>
          <h2 className="text-[#181510] text-lg font-bold leading-tight tracking-[-0.015em] flex-1 text-center pr-12">Credit Code</h2>
        </div>
        
        <div className="p-4">
          <Card className="w-full max-w-md mx-auto">
            <CardHeader>
              <div className="h-6 w-48 mx-auto bg-gray-200 animate-pulse rounded"></div>
            </CardHeader>
            <CardContent className="flex flex-col items-center">
              <div className="w-64 h-64 mb-4 bg-gray-200 animate-pulse rounded"></div>
              <div className="w-full p-3 bg-gray-200 animate-pulse rounded-md mb-4 h-10"></div>
              <div className="h-4 w-32 bg-gray-200 animate-pulse rounded mb-2"></div>
            </CardContent>
            <CardFooter className="flex justify-between">
              <div className="h-9 w-28 bg-gray-200 animate-pulse rounded"></div>
              <div className="h-9 w-28 bg-gray-200 animate-pulse rounded"></div>
              <div className="h-9 w-28 bg-gray-200 animate-pulse rounded"></div>
            </CardFooter>
          </Card>
        </div>
        
        <div className="mt-auto">
          <div className="flex justify-stretch">
            <div className="flex flex-1 gap-3 flex-wrap px-4 py-3 justify-between">
              <div className="w-full h-12 bg-gray-200 animate-pulse rounded-lg"></div>
            </div>
          </div>
          <div className="h-5 bg-[#fbfaf9]"></div>
        </div>
      </div>
    );
  }
  
  // Error state
  if (!creditCode) {
    return (
      <div className="relative flex size-full min-h-screen flex-col bg-[#fbfaf9] justify-between overflow-x-hidden">
        <div className="flex items-center bg-[#fbfaf9] p-4 pb-2 justify-between">
          <Link href={`/dashboard/merchant/${shopId}`} className="text-[#181510] flex size-12 shrink-0 items-center">
            <ArrowLeft size={24} weight="regular" />
          </Link>
          <h2 className="text-[#181510] text-lg font-bold leading-tight tracking-[-0.015em] flex-1 text-center pr-12">Credit Code</h2>
        </div>
        
        <div className="p-4 text-center">
          <Card className="w-full max-w-md mx-auto">
            <CardContent className="flex flex-col items-center py-8">
              <div className="text-[#8a745c] text-lg mb-4">Credit code not found</div>
              <p className="text-[#8a745c] mb-6">The credit code you&apos;re looking for doesn&apos;t exist or has been deleted.</p>
              <Button 
                className="bg-[#e5ccb2] text-[#181510] hover:bg-[#d9b99a]"
                onClick={() => router.push(`/dashboard/merchant/${shopId}`)}
              >
                Return to Shop
              </Button>
            </CardContent>
          </Card>
        </div>
        
        <div className="h-5 bg-[#fbfaf9]"></div>
      </div>
    );
  }

  return (
    <div className="relative flex size-full min-h-screen flex-col bg-[#fbfaf9] justify-between overflow-x-hidden">
      {/* Header */}
      <div className="flex items-center bg-[#fbfaf9] p-4 pb-2 justify-between">
        <Link href={`/dashboard/merchant/${shopId}`} className="text-[#181510] flex size-12 shrink-0 items-center">
          <ArrowLeft size={24} weight="regular" />
        </Link>
        <h2 className="text-[#181510] text-lg font-bold leading-tight tracking-[-0.015em] flex-1 text-center pr-12">Credit Code</h2>
      </div>
      
      {/* Credit Code Card */}
      <div className="p-4">
        <Card className="w-full max-w-md mx-auto">
          <CardHeader>
            <CardTitle className="text-center">Credit Code: ${creditCode.amount || 0}</CardTitle>
          </CardHeader>
          <CardContent className="flex flex-col items-center">
            {qrCodeUrl ? (
              <div className="relative w-64 h-64 mb-4">
                <Image
                  src={qrCodeUrl}
                  alt={`QR Code for ${creditCode.code}`}
                  fill
                  className="object-contain"
                  priority
                />
              </div>
            ) : (
              <div className="w-64 h-64 mb-4 flex items-center justify-center bg-gray-100 rounded">
                <p className="text-[#8a745c]">Loading QR code...</p>
              </div>
            )}
            
            <div className="w-full p-3 bg-muted rounded-md text-center mb-4">
              <p className="text-lg font-mono font-bold break-all">{creditCode.code}</p>
            </div>
            
            {creditCode.description && (
              <p className="text-muted-foreground text-center mb-2">{creditCode.description}</p>
            )}
            
            {creditCode.expires_at && (
              <p className="text-sm text-muted-foreground text-center">
                Expires: {new Date(creditCode.expires_at).toLocaleDateString()}
              </p>
            )}
            
            {creditCode.is_used && (
              <div className="mt-4 p-2 bg-red-100 text-red-800 rounded-md text-center">
                This code has already been redeemed
              </div>
            )}
          </CardContent>
          <CardFooter className="flex flex-wrap justify-between gap-2">
            <Button 
              variant="outline" 
              size="sm" 
              onClick={copyToClipboard} 
              disabled={creditCode.is_used}
              className="flex-1 min-w-[100px]"
            >
              <Copy size={16} className="mr-2" />
              Copy Code
            </Button>
            <Button 
              variant="outline" 
              size="sm" 
              onClick={downloadQRCode} 
              disabled={isDownloading || !qrCodeUrl || creditCode.is_used}
              className="flex-1 min-w-[100px]"
            >
              <Download size={16} className="mr-2" />
              {isDownloading ? "Downloading..." : "Download QR"}
            </Button>
            <Button 
              variant="outline" 
              size="sm" 
              onClick={shareCode} 
              disabled={creditCode.is_used}
              className="flex-1 min-w-[100px]"
            >
              <Share size={16} className="mr-2" />
              Share
            </Button>
          </CardFooter>
        </Card>
      </div>
      
      {/* Footer */}
      <div className="mt-auto">
        <div className="flex justify-stretch">
          <div className="flex flex-1 gap-3 flex-wrap px-4 py-3 justify-between">
            <Button 
              className="flex w-full cursor-pointer items-center justify-center overflow-hidden rounded-lg h-12 px-5 bg-[#e5ccb2] text-[#181510] text-base font-bold leading-normal tracking-[0.015em] hover:bg-[#d9b99a]"
              onClick={() => router.push(`/dashboard/merchant/${shopId}`)}
            >
              <ArrowLeft size={20} className="mr-2" />
              <span className="truncate">Back to Shop</span>
            </Button>
          </div>
        </div>
        <div className="h-5 bg-[#fbfaf9]"></div>
      </div>
    </div>
  );
}
