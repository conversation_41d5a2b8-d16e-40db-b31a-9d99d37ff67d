"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { useSession } from "next-auth/react";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { APIKey } from "@/types";
import { toast } from "sonner";
import { useCreateAPIKeyMutation } from "@/lib/api/apiSlice";
import { ArrowLeft, Copy, Key } from "lucide-react";
import Link from "next/link";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage
} from "@/components/ui/form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import * as z from "zod";

// Define the form schema with Zod
const formSchema = z.object({
  name: z.string().min(1, "Name is required"),
  permissions: z.array(z.string()).optional(),
});

// Define available permissions
const availablePermissions = [
  { id: "read", label: "Read" },
  { id: "write", label: "Write" },
  { id: "delete", label: "Delete" },
  { id: "admin", label: "Admin" },
];

export default function NewAPIKeyPage() {
  const router = useRouter();
  const { data: session } = useSession();
  const [newKey, setNewKey] = useState<APIKey | null>(null);

  // Store access token in session storage if available
  useEffect(() => {
    // Use type assertion to access accessToken
    const userAccessToken = (session?.user as any)?.accessToken;
    const sessionAccessToken = (session as any)?.accessToken;

    if (userAccessToken) {
      console.log('Setting access token from user:', userAccessToken.substring(0, 10) + '...');
      sessionStorage.setItem('accessToken', userAccessToken);
    } else if (sessionAccessToken) {
      console.log('Setting access token from session:', sessionAccessToken.substring(0, 10) + '...');
      sessionStorage.setItem('accessToken', sessionAccessToken);
    } else {
      console.warn('No access token found in session');
      // Redirect to login page or show an error message
      toast.error("Authentication error: Please log in to continue");
      router.push('/auth/login');
    }
  }, [session, router]);

  // Use RTK Query hook for creating API key
  const [createAPIKey, { isLoading }] = useCreateAPIKeyMutation();

  // Initialize form with react-hook-form and zod validation
  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: "",
      permissions: ["read"], // Default to read permission
    },
  });

  // Handle form submission
  const onSubmit = async (values: z.infer<typeof formSchema>) => {
    // Check if we have an access token
    const token = sessionStorage.getItem('accessToken');
    if (!token) {
      console.error("No access token found in session storage");
      toast.error("Authentication error: No access token found");
      return;
    }

    console.log('Submitting form with values:', values);
    console.log('Using access token:', token.substring(0, 10) + '...');

    try {
      const result = await createAPIKey({
        name: values.name,
        permissions: values.permissions,
      }).unwrap();

      console.log('API key created successfully:', result);
      setNewKey(result);
      toast.success("API key created successfully");
    } catch (error: any) {
      console.error("Error creating API key:", error);

      // More detailed error handling
      if (error.status === 401) {
        toast.error("Authentication error: Please log in again");
      } else if (error.data?.error) {
        toast.error(`Error: ${error.data.error}`);
      } else {
        toast.error("Failed to create API key");
      }
    }
  };

  return (
    <div className="container py-10">
      <div className="flex items-center gap-2 mb-8">
        <Link href="/dashboard/api-keys">
          <Button variant="outline" size="icon" className="h-8 w-8">
            <ArrowLeft className="h-4 w-4" />
          </Button>
        </Link>
        <h1 className="text-3xl font-bold">Create New API Key</h1>
      </div>

      {newKey ? (
        <div className="space-y-6">
          <Alert className="mb-6">
            <AlertDescription className="flex flex-col gap-2">
              <p>
                <strong>Important:</strong> Copy your new API key now. You won&apos;t be able to see it again!
              </p>
              <div className="flex items-center gap-2">
                <code className="relative rounded bg-muted px-[0.3rem] py-[0.2rem] font-mono text-sm">
                  {newKey.key}
                </code>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    navigator.clipboard.writeText(newKey.key);
                    toast.success("API key copied to clipboard");
                  }}
                >
                  <Copy className="h-4 w-4 mr-2" />
                  Copy
                </Button>
              </div>
            </AlertDescription>
          </Alert>

          <div className="flex gap-4">
            <Button onClick={() => setNewKey(null)}>Create Another Key</Button>
            <Link href="/dashboard/api-keys">
              <Button variant="outline">Return to API Keys</Button>
            </Link>
          </div>
        </div>
      ) : (
        <Card>
          <CardHeader>
            <CardTitle>Create API Key</CardTitle>
            <CardDescription>
              Create a new API key for integrating with external systems. You will only be able to view the key once after creation.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                <FormField
                  control={form.control}
                  name="name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Name</FormLabel>
                      <FormControl>
                        <Input placeholder="My API Key" {...field} />
                      </FormControl>
                      <FormDescription>
                        A descriptive name to identify this API key
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <div className="space-y-3">
                  <FormLabel>Permissions</FormLabel>
                  <FormDescription>
                    Select the permissions for this API key
                  </FormDescription>
                  <div className="grid grid-cols-2 gap-4">
                    {availablePermissions.map((permission) => (
                      <FormField
                        key={permission.id}
                        control={form.control}
                        name="permissions"
                        render={({ field }) => (
                          <FormItem
                            key={permission.id}
                            className="flex flex-row items-start space-x-3 space-y-0"
                          >
                            <FormControl>
                              <Checkbox
                                checked={field.value?.includes(permission.id)}
                                onCheckedChange={(checked) => {
                                  const currentPermissions = field.value || [];
                                  if (checked) {
                                    field.onChange([...currentPermissions, permission.id]);
                                  } else {
                                    field.onChange(
                                      currentPermissions.filter((p) => p !== permission.id)
                                    );
                                  }
                                }}
                              />
                            </FormControl>
                            <FormLabel className="font-normal">
                              {permission.label}
                            </FormLabel>
                          </FormItem>
                        )}
                      />
                    ))}
                  </div>
                </div>

                <div className="flex justify-end gap-4">
                  <Link href="/dashboard/api-keys">
                    <Button variant="outline" type="button">Cancel</Button>
                  </Link>
                  <Button type="submit" disabled={isLoading}>
                    {isLoading ? (
                      <div className="flex items-center gap-2">
                        <div className="h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent"></div>
                        Creating...
                      </div>
                    ) : (
                      <div className="flex items-center gap-2">
                        <Key className="h-4 w-4" />
                        Create API Key
                      </div>
                    )}
                  </Button>
                </div>
              </form>
            </Form>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
